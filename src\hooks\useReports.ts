
import { useQuery } from '@tanstack/react-query';
import { getReportData, ReportData } from '@/data/reports';
import { TimeRange, CustomDateRange } from '@/components/dashboard/DashboardContent';
import { ReportType } from '@/components/pages/ReportsPage';

export function useReports(timeRange: TimeRange, reportType: ReportType, customDateRange?: CustomDateRange) {
  return useQuery<ReportData, Error>({
    queryKey: ['reports', timeRange, reportType, customDateRange],
    queryFn: () => getReportData({ timeRange, reportType, customDateRange }),
    placeholderData: { main: [], secondary: [] },
  });
}
