import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Gauge, Edit, Save, X, AlertTriangle, CheckCircle } from 'lucide-react';
import { useEnhancedParameterNorms, useUpdateEnhancedParameterNorm } from '@/hooks/useKilnMonitoring';
import { EnhancedKilnParameterNorm } from '@/types/kilnMonitoring';
import { toast } from 'sonner';

interface EditingState {
  [key: string]: {
    min_value: string;
    max_value: string;
    cause: string;
    action: string;
    reasoning: string;
    action_required: string;
  };
}

export const EnhancedParameterNorms: React.FC = () => {
  const { data: parameterNorms = [], isLoading } = useEnhancedParameterNorms();
  const updateParameterNorm = useUpdateEnhancedParameterNorm();
  const [editingRows, setEditingRows] = useState<Set<string>>(new Set());
  const [editingState, setEditingState] = useState<EditingState>({});

  const handleEdit = (norm: EnhancedKilnParameterNorm) => {
    setEditingRows(prev => new Set([...prev, norm.id]));
    setEditingState(prev => ({
      ...prev,
      [norm.id]: {
        min_value: norm.min_value.toString(),
        max_value: norm.max_value.toString(),
        cause: norm.cause || '',
        action: norm.action || '',
        reasoning: norm.reasoning || '',
        action_required: norm.action_required || ''
      }
    }));
  };

  const handleCancel = (normId: string) => {
    setEditingRows(prev => {
      const newSet = new Set(prev);
      newSet.delete(normId);
      return newSet;
    });
    setEditingState(prev => {
      const newState = { ...prev };
      delete newState[normId];
      return newState;
    });
  };

  const handleSave = async (norm: EnhancedKilnParameterNorm) => {
    const editData = editingState[norm.id];
    if (!editData) return;

    try {
      await updateParameterNorm.mutateAsync({
        id: norm.id,
        updates: {
          min_value: parseFloat(editData.min_value),
          max_value: parseFloat(editData.max_value),
          cause: editData.cause,
          action: editData.action,
          reasoning: editData.reasoning,
          action_required: editData.action_required
        }
      });

      handleCancel(norm.id);
    } catch (error) {
      console.error('Error saving parameter norm:', error);
    }
  };

  const handleInputChange = (normId: string, field: keyof EditingState[string], value: string) => {
    setEditingState(prev => ({
      ...prev,
      [normId]: {
        ...prev[normId],
        [field]: value
      }
    }));
  };

  const getParameterStatus = (norm: EnhancedKilnParameterNorm) => {
    if (!norm.reasoning && !norm.action_required) {
      return { status: 'incomplete', color: 'bg-yellow-100 text-yellow-800', icon: AlertTriangle };
    }
    return { status: 'complete', color: 'bg-green-100 text-green-800', icon: CheckCircle };
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gauge className="h-5 w-5" />
            Enhanced Parameter Norms & Actions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-700"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Gauge className="h-5 w-5" />
          Enhanced Parameter Norms & Actions
        </CardTitle>
        <p className="text-sm text-slate-600">
          Configure parameter limits, causes, actions, and reasoning for out-of-norm readings
        </p>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Parameter</TableHead>
                <TableHead>Range</TableHead>
                <TableHead>Cause</TableHead>
                <TableHead>Corrective Action</TableHead>
                <TableHead>Reasoning</TableHead>
                <TableHead>Action Required</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {parameterNorms.map((norm) => {
                const isEditing = editingRows.has(norm.id);
                const editData = editingState[norm.id];
                const { status, color, icon: StatusIcon } = getParameterStatus(norm);

                return (
                  <TableRow key={norm.id}>
                    <TableCell className="font-medium">
                      {norm.parameter_name} ({norm.unit})
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <div className="flex items-center gap-1">
                          <Input
                            type="number"
                            value={editData?.min_value || ''}
                            onChange={(e) => handleInputChange(norm.id, 'min_value', e.target.value)}
                            className="w-20"
                            step="0.1"
                          />
                          <span>-</span>
                          <Input
                            type="number"
                            value={editData?.max_value || ''}
                            onChange={(e) => handleInputChange(norm.id, 'max_value', e.target.value)}
                            className="w-20"
                            step="0.1"
                          />
                        </div>
                      ) : (
                        <span>{norm.min_value} - {norm.max_value}</span>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <Textarea
                          value={editData?.cause || ''}
                          onChange={(e) => handleInputChange(norm.id, 'cause', e.target.value)}
                          className="min-h-[60px]"
                          placeholder="Describe the cause..."
                        />
                      ) : (
                        <div className="max-w-xs">{norm.cause}</div>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <Textarea
                          value={editData?.action || ''}
                          onChange={(e) => handleInputChange(norm.id, 'action', e.target.value)}
                          className="min-h-[60px]"
                          placeholder="Describe the corrective action..."
                        />
                      ) : (
                        <div className="max-w-xs">{norm.action}</div>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <Textarea
                          value={editData?.reasoning || ''}
                          onChange={(e) => handleInputChange(norm.id, 'reasoning', e.target.value)}
                          className="min-h-[60px]"
                          placeholder="Explain the reasoning behind this parameter..."
                        />
                      ) : (
                        <div className="max-w-xs">{norm.reasoning || 'Not specified'}</div>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <Textarea
                          value={editData?.action_required || ''}
                          onChange={(e) => handleInputChange(norm.id, 'action_required', e.target.value)}
                          className="min-h-[60px]"
                          placeholder="Specify required actions for out-of-norm readings..."
                        />
                      ) : (
                        <div className="max-w-xs">{norm.action_required || 'Not specified'}</div>
                      )}
                    </TableCell>
                    
                    <TableCell>
                      <Badge className={color}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {status}
                      </Badge>
                    </TableCell>
                    
                    <TableCell>
                      {isEditing ? (
                        <div className="flex items-center gap-1">
                          <Button
                            size="sm"
                            onClick={() => handleSave(norm)}
                            disabled={updateParameterNorm.isPending}
                          >
                            <Save className="h-3 w-3" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => handleCancel(norm.id)}
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      ) : (
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleEdit(norm)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
