
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type Payment = Database['public']['Tables']['payments']['Row'];
export type NewPayment = Database['public']['Tables']['payments']['Insert'];

type PaymentWithEmployee = Payment & {
    employees: { name: string } | null;
}

export type PaymentWithEmployeeName = Omit<PaymentWithEmployee, 'employees'> & {
    employee_name: string;
};

export const getPayments = async (): Promise<PaymentWithEmployeeName[]> => {
  const { data, error } = await supabase
    .from('payments')
    .select(`
      *,
      employees (
        name
      )
    `)
    .order('payment_date', { ascending: false });

  if (error) {
    console.error("Error fetching payments", error);
    throw new Error(error.message);
  }

  return (data as PaymentWithEmployee[] || []).map(p => {
    const { employees, ...rest } = p;
    return {
      ...rest,
      employee_name: employees?.name || 'Unknown Employee'
    };
  });
};

export const addPayment = async (payment: NewPayment | NewPayment[]) => {
    const paymentsToInsert = Array.isArray(payment) ? payment : [payment];
    if (paymentsToInsert.length === 0) return [];
    
    const { data, error } = await supabase.from('payments').insert(paymentsToInsert).select();
    if (error) {
        console.error("Error adding payment:", error);
        throw error;
    }
    return data;
};
