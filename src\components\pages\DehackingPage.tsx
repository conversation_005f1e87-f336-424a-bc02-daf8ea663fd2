
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Filter, Calendar, Loader2 } from "lucide-react";
import { useState, useSyncExternalStore } from "react";
import { NewQuickDehackingEntryDialog } from "@/components/dashboard/dialogs/NewQuickDehackingEntryDialog";
import { DehackingFilters } from "@/components/dehacking/DehackingFilters";
import { MetricsCards } from "@/components/dehacking/MetricsCards";
import { PerformanceReport } from "@/components/dehacking/PerformanceReport";
import { isToday, isThisWeek, isThisMonth } from "date-fns";
import { useQuery } from "@tanstack/react-query";

import { getDehackingSummary, subscribeToDehacking, DehackingEntry } from "@/data/dehackingStore";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { getEmployees, Employee } from "@/data/employeeData";
import { getShiftType } from "@/utils/shiftHelpers";

export const DehackingPage = () => {
  const [selectedPeriod, setSelectedPeriod] = useState<"daily" | "weekly" | "monthly">("daily");
  const [isQuickEntryOpen, setIsQuickEntryOpen] = useState(false);
  const [isFiltersOpen, setIsFiltersOpen] = useState(false);
  const [filters, setFilters] = useState<{ employeeId?: string }>({});

  useSyncExternalStore(subscribeToDehacking, () => 0);

  const { data: employees = [], isLoading: isLoadingEmployees } = useQuery<Employee[]>({ 
    queryKey: ['employees'], 
    queryFn: getEmployees 
  });
  const { data: brickTypes = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({ 
    queryKey: ['managementBrickTypes'], 
    queryFn: getManagementBrickTypes 
  });

  const calculateEarnings = (entry: DehackingEntry) => {
    const brickType = brickTypes.find((b) => b.id === entry.brickTypeId);
    if (!brickType) return 0;

    // Convert hour number to hour string for getShiftType
    const shiftType = getShiftType(`${String(entry.hour).padStart(2, '0')}:00`);
    let rate = brickType.dehacking_rate;
    if (shiftType === "day" && brickType.dehacking_day_rate) {
      rate = brickType.dehacking_day_rate;
    }
    if (shiftType === "night" && brickType.dehacking_night_rate) {
      rate = brickType.dehacking_night_rate;
    }

    return entry.palletCount * rate;
  };

  const allEntries = getDehackingSummary(() => true);
  const transformedData = allEntries.map(entry => {
    const employee = employees.find(e => e.id === entry.employeeId);
    const brickType = brickTypes.find(b => b.id === entry.brickTypeId);
    const earningsValue = calculateEarnings(entry);
    return {
        employeeId: entry.employeeId,
        employee: employee ? employee.name : "Unknown",
        totalPallets: entry.palletCount,
        earnings: `R ${earningsValue.toFixed(2)}`,
        brickTypes: brickType ? brickType.name : "Unknown",
        date: entry.date,
    };
  });

  const periodData = transformedData.filter(item => {
    const itemDate = new Date(item.date);
    if (selectedPeriod === "daily") {
      return isToday(itemDate);
    }
    if (selectedPeriod === "weekly") {
      return isThisWeek(itemDate);
    }
    if (selectedPeriod === "monthly") {
      return isThisMonth(itemDate);
    }
    return false;
  });
  
  const totalPallets = periodData.reduce((sum, item) => sum + item.totalPallets, 0);
  const totalEarnings = periodData.reduce((sum, item) => {
    const value = parseFloat(item.earnings.replace("R ", "").replace(",", ""));
    return sum + value;
  }, 0);
  const activeEmployees = new Set(periodData.map(item => item.employeeId)).size;
  const averagePerEmployee = activeEmployees > 0 ? (totalPallets / activeEmployees).toFixed(1) : "0";

  const periodSubtitle = {
    daily: "Today",
    weekly: "This Week",
    monthly: "This Month",
  }[selectedPeriod];

  const handleApplyFilters = (newFilters: { employeeId?: string }) => {
    if (newFilters.employeeId === "all") {
      setFilters({});
      return;
    }
    setFilters(newFilters);
  };

  const handleResetFilters = () => {
    setFilters({});
  };

  const filteredData = periodData.filter(item => {
    if (filters.employeeId) {
      return String(item.employeeId) === filters.employeeId;
    }
    return true;
  });

  const isLoading = isLoadingEmployees || isLoadingBrickTypes;

  if (isLoading) {
    return (
        <div className="flex justify-center items-center h-64">
            <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            <p className="ml-2 text-slate-500">Loading page data...</p>
        </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Dehacking Management</h1>
          <p className="text-slate-600">Track and manage individual dehacking performance</p>
        </div>
        <div className="flex gap-3">
          <Button variant="outline" className="flex items-center gap-2" onClick={() => setIsQuickEntryOpen(true)}>
            <Calendar size={20} />
            Quick Entry
          </Button>
          <Button variant="outline" className="flex items-center gap-2" onClick={() => setIsFiltersOpen(true)}>
            <Filter size={20} />
            Filters
          </Button>
        </div>
      </div>

      <MetricsCards 
        totalPallets={totalPallets}
        totalEarnings={totalEarnings}
        activeEmployees={activeEmployees}
        averagePerEmployee={averagePerEmployee}
        periodSubtitle={periodSubtitle}
      />

      <PerformanceReport 
        selectedPeriod={selectedPeriod}
        onPeriodChange={setSelectedPeriod}
        filteredData={filteredData}
      />

      <NewQuickDehackingEntryDialog isOpen={isQuickEntryOpen} onClose={() => setIsQuickEntryOpen(false)} />
      <DehackingFilters
        open={isFiltersOpen}
        onOpenChange={setIsFiltersOpen}
        employees={employees}
        onApplyFilters={handleApplyFilters}
        onResetFilters={handleResetFilters}
        filters={filters}
      />
    </div>
  );
};
