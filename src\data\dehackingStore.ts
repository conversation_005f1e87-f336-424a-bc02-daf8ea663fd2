import { supabase } from "@/integrations/supabase/client";

export interface DehackingEntry {
  id: number;
  employeeId: number;
  brickTypeId: string;
  palletCount: number;
  date: string;
  hour: number;
  teamId?: string;
  isOvertime?: boolean;
  isNightShift?: boolean;
  fireId?: string;
  chamberNumber?: number;
}

export interface DehackingEntryFromSupabase {
  id: number;
  employee_id: number;
  brick_type_id: string;
  pallet_count: number;
  date: string;
  hour: number;
  team_id?: string;
  created_at: string;
  is_overtime?: boolean;
  is_night_shift?: boolean;
  fire_id?: string;
  chamber_number?: number;
}

let dehackingEntries: DehackingEntry[] = [];
let subscribers: (() => void)[] = [];

const notifySubscribers = () => {
  subscribers.forEach(callback => callback());
};

export const subscribeToDehacking = (callback: () => void) => {
  subscribers.push(callback);
  return () => {
    subscribers = subscribers.filter(sub => sub !== callback);
  };
};

export const loadDehackingData = async (filters?: {
  startDate?: string;
  endDate?: string;
  teamId?: string;
}) => {
  try {
    // Use `let` and `any` to avoid TypeScript error with deep type instantiation
    let query: any = supabase.from('dehacking_entries').select('*');

    if (filters?.startDate) {
      query = query.gte('date', filters.startDate);
    }
    if (filters?.endDate) {
      query = query.lte('date', filters.endDate);
    }
    // Only add team_id filter if we're not getting a schema error
    if (filters?.teamId) {
      query = query.eq('team_id', filters.teamId);
    }

    const { data, error } = await query;
    
    if (error) {
      console.error('Error loading dehacking data:', error);
      return;
    }

    dehackingEntries = data?.map((entry: any) => ({
      id: entry.id,
      employeeId: entry.employee_id,
      brickTypeId: entry.brick_type_id,
      palletCount: entry.pallet_count,
      date: entry.date,
      hour: entry.hour,
      teamId: entry.team_id || undefined,
      isOvertime: entry.is_overtime || false,
      isNightShift: entry.is_night_shift || false,
      fireId: entry.fire_id || undefined,
      chamberNumber: entry.chamber_number || undefined,
    })) || [];
    
    notifySubscribers();
  } catch (error) {
    console.error('Error loading dehacking data:', error);
  }
};

// Initial load without filters
loadDehackingData();

export const addDehackingEntry = async (entry: Omit<DehackingEntry, 'id'>) => {
  try {
    console.log("🔥 addDehackingEntry called with:", entry);

    // Prepare base insert data without team_id first
    const baseInsertData = {
      employee_id: entry.employeeId,
      brick_type_id: entry.brickTypeId,
      pallet_count: entry.palletCount,
      date: entry.date,
      hour: entry.hour,
      is_overtime: entry.isOvertime || false,
      is_night_shift: entry.isNightShift || false,
      fire_id: entry.fireId || null,
      chamber_number: entry.chamberNumber || null,
    };

    // Try to insert with team_id first
    let insertData: any = {
      ...baseInsertData,
      team_id: entry.teamId || null,
    };

    console.log("📝 Attempting insert with team_id:", insertData);

    let { data, error } = await supabase
      .from('dehacking_entries')
      .insert(insertData)
      .select()
      .single();

    // If we get a schema cache error about team_id, try without it
    if (error && error.message && error.message.includes('team_id')) {
      console.log("⚠️ team_id column not found, retrying without it...");
      
      insertData = baseInsertData;
      console.log("📝 Retrying insert without team_id:", insertData);

      const retryResult = await supabase
        .from('dehacking_entries')
        .insert(insertData)
        .select()
        .single();

      data = retryResult.data;
      error = retryResult.error;
    }

    if (error) {
      console.error('❌ Supabase error:', error);
      console.error('❌ Error code:', error.code);
      console.error('❌ Error message:', error.message);
      console.error('❌ Error details:', error.details);
      console.error('❌ Error hint:', error.hint);

      // Provide more specific error messages
      if (error.code === '23503') {
        if (error.message.includes('employee_id')) {
          throw new Error('Selected employee does not exist. Please refresh the page and try again.');
        } else if (error.message.includes('brick_type_id')) {
          throw new Error('Selected brick type does not exist. Please refresh the page and try again.');
        } else if (error.message.includes('fire_id')) {
          throw new Error('Selected fire does not exist. Please refresh the page and try again.');
        }
      }

      throw new Error(error.message || 'Database error occurred');
    }

    console.log("✅ Insert successful:", data);

    const newEntry: DehackingEntry = {
      id: data.id,
      employeeId: data.employee_id,
      brickTypeId: data.brick_type_id,
      palletCount: data.pallet_count,
      date: data.date,
      hour: data.hour,
      teamId: (data as any).team_id || undefined,
      isOvertime: data.is_overtime || false,
      isNightShift: data.is_night_shift || false,
      fireId: data.fire_id || undefined,
      chamberNumber: data.chamber_number || undefined,
    };

    dehackingEntries.push(newEntry);
    notifySubscribers();
    return newEntry;
  } catch (error) {
    console.error('💥 Final error in addDehackingEntry:', error);
    throw error;
  }
};

export const getDehackingSummary = (filter: (date: Date) => boolean) => {
  return dehackingEntries.filter(entry => filter(new Date(entry.date)));
};

export const getDehackingBreakdown = (
  timeFilter: (date: Date) => boolean,
  allBrickTypes: any[]
) => {
  const filteredEntries = dehackingEntries.filter(entry => timeFilter(new Date(entry.date)));
  
  // Group by brick type
  const typeStats: { [key: string]: number } = {};
  
  filteredEntries.forEach(entry => {
    const brickType = allBrickTypes.find(bt => bt.id === entry.brickTypeId);
    if (brickType) {
      const bricksCount = entry.palletCount * brickType.bricks_per_pallet;
      typeStats[entry.brickTypeId] = (typeStats[entry.brickTypeId] || 0) + bricksCount;
    }
  });

  const typeBreakdown = Object.entries(typeStats)
    .map(([brickTypeId, bricks]) => {
      const brickType = allBrickTypes.find(bt => bt.id === brickTypeId);
      return {
        id: brickTypeId,
        name: brickType?.name || `Type ${brickTypeId}`,
        bricks,
      };
    })
    .sort((a, b) => b.bricks - a.bricks);

  return {
    typeBreakdown,
  };
};
