
-- Drop old policies if they exist to avoid conflicts
DROP POLICY IF EXISTS "Allow authenticated users full access on kilns" ON public.kilns;
DROP POLICY IF EXISTS "Allow public read access to kilns" ON public.kilns;

DROP POLICY IF EXISTS "Allow authenticated users full access on fires" ON public.fires;
DROP POLICY IF EXISTS "Allow public read access to fires" ON public.fires;

-- Create new policies for public read access
CREATE POLICY "Allow public read access to kilns"
ON public.kilns
FOR SELECT
USING (true);

CREATE POLICY "Allow public read access to fires"
ON public.fires
FOR SELECT
USING (true);
