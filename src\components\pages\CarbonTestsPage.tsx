
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Plus, Calendar, Clock, Loader2, TestTube } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useCarbonTests, useAddCarbonTest } from "@/hooks/useCarbonTests";
import { format } from "date-fns";

const carbonTestSchema = z.object({
  date: z.string().min(1, "Date is required"),
  time: z.string().min(1, "Time is required"),
  test_type: z.enum(['maxi', 'imperial', 'spiral', 'ash'], {
    errorMap: () => ({ message: "Please select a test type" })
  }),
  average_carbon: z.number().min(0, "Average carbon must be positive"),
  average_sulphur: z.number().min(0, "Average sulphur must be positive")
});

type CarbonTestFormValues = z.infer<typeof carbonTestSchema>;

export const CarbonTestsPage = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const { data: carbonTests = [], isLoading, error } = useCarbonTests();
  const addCarbonTestMutation = useAddCarbonTest();

  const form = useForm<CarbonTestFormValues>({
    resolver: zodResolver(carbonTestSchema),
    defaultValues: {
      date: format(new Date(), "yyyy-MM-dd"),
      time: format(new Date(), "HH:mm"),
      test_type: "maxi",
      average_carbon: 0,
      average_sulphur: 0
    }
  });

  const onSubmit = (data: CarbonTestFormValues) => {
    // Ensure all required fields are present with proper types
    const submitData = {
      date: data.date,
      time: data.time,
      test_type: data.test_type,
      average_carbon: data.average_carbon,
      average_sulphur: data.average_sulphur
    };

    addCarbonTestMutation.mutate(submitData, {
      onSuccess: () => {
        toast.success("Carbon test recorded successfully!");
        form.reset({
          date: format(new Date(), "yyyy-MM-dd"),
          time: format(new Date(), "HH:mm"),
          test_type: "maxi" as const,
          average_carbon: 0,
          average_sulphur: 0
        });
        setIsFormVisible(false);
      },
      onError: (error) => {
        toast.error(`Failed to record carbon test: ${error.message}`);
      }
    });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Carbon Tests</h1>
          <p className="text-slate-600">Track carbon and sulphur test results</p>
        </div>
        <Button onClick={() => setIsFormVisible(!isFormVisible)} className="flex items-center gap-2">
          <Plus size={20} />
          {isFormVisible ? "Cancel" : "Add Test"}
        </Button>
      </div>

      {isFormVisible && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TestTube size={20} />
              Record Carbon Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date" className="flex items-center gap-2">
                    <Calendar size={16} />
                    Date
                  </Label>
                  <Input id="date" type="date" {...form.register("date")} />
                  {form.formState.errors.date && <p className="text-sm text-red-600">{form.formState.errors.date.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="time" className="flex items-center gap-2">
                    <Clock size={16} />
                    Time
                  </Label>
                  <Input id="time" type="time" {...form.register("time")} />
                  {form.formState.errors.time && <p className="text-sm text-red-600">{form.formState.errors.time.message}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="test_type">Test Type</Label>
                <Select onValueChange={(value) => form.setValue("test_type", value as any)} defaultValue="maxi">
                  <SelectTrigger>
                    <SelectValue placeholder="Select test type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="maxi">Maxi</SelectItem>
                    <SelectItem value="imperial">Imperial</SelectItem>
                    <SelectItem value="spiral">Spiral</SelectItem>
                    <SelectItem value="ash">Ash</SelectItem>
                  </SelectContent>
                </Select>
                {form.formState.errors.test_type && <p className="text-sm text-red-600">{form.formState.errors.test_type.message}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="average_carbon">Average Carbon (%)</Label>
                  <Input id="average_carbon" type="number" step="0.01" {...form.register("average_carbon", { valueAsNumber: true })} />
                  {form.formState.errors.average_carbon && <p className="text-sm text-red-600">{form.formState.errors.average_carbon.message}</p>}
                </div>
                
                <div className="space-y-2">
                  <Label htmlFor="average_sulphur">Average Sulphur (%)</Label>
                  <Input id="average_sulphur" type="number" step="0.01" {...form.register("average_sulphur", { valueAsNumber: true })} />
                  {form.formState.errors.average_sulphur && <p className="text-sm text-red-600">{form.formState.errors.average_sulphur.message}</p>}
                </div>
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={addCarbonTestMutation.isPending} className="flex items-center gap-2">
                  {addCarbonTestMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  Record Test
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsFormVisible(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Recent Carbon Tests</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p>Error loading carbon tests: {error.message}</p>
            </div>
          ) : carbonTests.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <TestTube className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No carbon tests recorded yet.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {carbonTests.slice(0, 10).map(test => (
                <div key={test.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar size={16} className="text-slate-500" />
                      <span className="font-medium">{format(new Date(test.date), "MMM dd, yyyy")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock size={16} className="text-slate-500" />
                      <span>{test.time}</span>
                    </div>
                    <div className="text-sm">
                      <span className="font-semibold capitalize">{test.test_type}</span>
                    </div>
                    <div className="text-sm">
                      <span>C: {test.average_carbon}%</span>
                      <span className="ml-2">S: {test.average_sulphur}%</span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
