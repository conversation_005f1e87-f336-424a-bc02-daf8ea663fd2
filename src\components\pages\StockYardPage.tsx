
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Plus, ArrowDown, ArrowUp } from "lucide-react";
import { InboundTable } from "@/components/stock-movement/InboundTable";
import { OutboundTable } from "@/components/stock-movement/OutboundTable";
import { AddInboundDialog } from "@/components/stock-movement/AddInboundDialog";
import { AddOutboundDialog } from "@/components/stock-movement/AddOutboundDialog";
import { StockSummaryCards } from "@/components/stock-movement/StockSummaryCards";
import { useStockMovement } from "@/hooks/useStockMovement";

const StockYardPage = () => {
  const [showAddInboundDialog, setShowAddInboundDialog] = useState(false);
  const [showAddOutboundDialog, setShowAddOutboundDialog] = useState(false);
  const [activeTab, setActiveTab] = useState("inbound");
  
  const { 
    inboundMovements, 
    outboundMovements, 
    isLoading,
    markAsReceived,
    addInboundMovement,
    addOutboundMovement
  } = useStockMovement();

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-slate-900">Stock Yard</h1>
          <p className="text-slate-600">Track brick deliveries between Worcester Bakstene and Kraaifontein stock yard</p>
        </div>
      </div>

      {/* Summary Cards */}
      <StockSummaryCards 
        inboundMovements={inboundMovements}
        outboundMovements={outboundMovements}
      />

      {/* Main Content */}
      <Card>
        <CardHeader>
          <CardTitle>Stock Movement Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="flex justify-between items-center mb-4">
              <TabsList className="grid w-full max-w-md grid-cols-2">
                <TabsTrigger value="inbound" className="flex items-center gap-2">
                  <ArrowDown className="h-4 w-4" />
                  Inbound Deliveries
                </TabsTrigger>
                <TabsTrigger value="outbound" className="flex items-center gap-2">
                  <ArrowUp className="h-4 w-4" />
                  Outbound Sales
                </TabsTrigger>
              </TabsList>
              
              <div className="flex gap-2">
                {activeTab === "inbound" && (
                  <Button onClick={() => setShowAddInboundDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Delivery
                  </Button>
                )}
                {activeTab === "outbound" && (
                  <Button onClick={() => setShowAddOutboundDialog(true)}>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Sale
                  </Button>
                )}
              </div>
            </div>

            <TabsContent value="inbound" className="space-y-4">
              <InboundTable 
                movements={inboundMovements}
                isLoading={isLoading}
                onMarkReceived={markAsReceived}
              />
            </TabsContent>

            <TabsContent value="outbound" className="space-y-4">
              <OutboundTable 
                movements={outboundMovements}
                isLoading={isLoading}
              />
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Dialogs */}
      <AddInboundDialog
        open={showAddInboundDialog}
        onOpenChange={setShowAddInboundDialog}
        onAdd={addInboundMovement}
      />

      <AddOutboundDialog
        open={showAddOutboundDialog}
        onOpenChange={setShowAddOutboundDialog}
        onAdd={addOutboundMovement}
      />
    </div>
  );
};

export default StockYardPage;
