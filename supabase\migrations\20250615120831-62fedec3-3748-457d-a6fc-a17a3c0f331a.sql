
-- 1. Allow public (unauthenticated) read access to management_brick_types table
ALTER TABLE public.management_brick_types ENABLE ROW LEVEL SECURITY;

-- Policy for public SELECT
CREATE POLICY "Allow public read for management_brick_types"
  ON public.management_brick_types
  FOR SELECT
  USING (true);

-- 2. Standardize status values to capitalized ("Active" and "Inactive") for all rows
UPDATE public.management_brick_types
  SET status = 
    CASE
      WHEN LOWER(status) = 'active' THEN 'Active'
      WHEN LOWER(status) = 'inactive' THEN 'Inactive'
      ELSE status
    END;

