
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Fuel, Loader2, PlusCircle } from "lucide-react";
import { toast } from "sonner";
import { useQuery } from "@tanstack/react-query";
import { getFuelBunkers } from "@/data/fuelBunkersData";

export const FuelBunkersDashboard = () => {
  const { data: fuelBunkersData = [], isLoading, isError } = useQuery({
    queryKey: ['fuelBunkers'],
    queryFn: getFuelBunkers
  });

  const handleAddBunker = () => {
    toast.info("Coming Soon: Add a new fuel bunker.");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800 flex items-center gap-2">
          <Fuel size={20} />
          Fuel Bunkers
        </CardTitle>
        <p className="text-sm text-slate-600">Current fuel levels and storage capacity</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {isLoading ? (
            <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
            </div>
          ) : isError ? (
             <div className="text-center py-8 text-red-500">
              <p>Failed to load fuel bunkers.</p>
            </div>
          ) : fuelBunkersData.length > 0 ? (
            <div className="space-y-4">
              {fuelBunkersData.map((bunker) => (
                <div key={bunker.id} className="border rounded-lg p-3">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium text-slate-700">{bunker.name}</h4>
                    <span className="text-sm font-semibold text-slate-800">
                      {bunker.current_level.toLocaleString()} L
                    </span>
                  </div>
                  <div className="w-full bg-slate-200 rounded-full h-1.5 mt-2">
                    <div
                      className="bg-blue-500 h-1.5 rounded-full"
                      style={{ width: `${(bunker.current_level / bunker.capacity) * 100}%` }}
                    ></div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-slate-500 mb-4">No active fuel bunkers found</p>
            </div>
          )}
           <Button 
              variant="outline" 
              onClick={handleAddBunker}
              className="w-full border-slate-300 flex items-center gap-2 mt-4"
            >
              <PlusCircle size={16} />
              Add New Bunker
            </Button>
        </div>
      </CardContent>
    </Card>
  );
};
