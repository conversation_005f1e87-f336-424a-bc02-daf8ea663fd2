
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Clock, Users } from "lucide-react";

interface HourlyRateCardProps {
  onManageHourlyRate: () => void;
}

export const HourlyRateCard = ({ onManageHourlyRate }: HourlyRateCardProps) => {
  return (
    <Card className="bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <Clock size={20} />
          Hourly Rate
        </CardTitle>
        <p className="text-sm text-purple-600">Record hourly work for kiln cleaning</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between text-sm text-purple-700">
          <span className="flex items-center gap-1">
            <Users size={16} />
            Rate: R28.79/hour
          </span>
        </div>
        
        <Button 
          onClick={onManageHourlyRate}
          className="w-full bg-purple-600 hover:bg-purple-700 text-white"
        >
          <Clock size={16} className="mr-2" />
          Record Hours
        </Button>
      </CardContent>
    </Card>
  );
};
