
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, Legend } from 'recharts';
import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { format, subDays } from 'date-fns';

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla Kiln" },
  { id: "kiln-1", name: "Kiln 1" },
  { id: "kiln-2", name: "Kiln 2" },
  { id: "kiln-3", name: "Kiln 3" },
  { id: "kiln-4", name: "Kiln 4" },
  { id: "kiln-5", name: "Kiln 5" },
];

const PARAMETER_TESTS = [
  'Brick Core Temp',
  'Brick Moisture',
  'CO',
  'CO₂',
  'Cooling Zone Temp',
  'Draught Pressure',
  'Fire Zone Temp',
  'O₂',
  'Preheat Temp'
];

const COLORS = [
  '#3b82f6', '#ef4444', '#10b981', '#f59e0b', '#8b5cf6',
  '#ec4899', '#06b6d4', '#84cc16', '#f97316'
];

export const ParameterTrends = () => {
  const [selectedKiln, setSelectedKiln] = useState("habla-kiln");
  const endDate = new Date();
  const startDate = subDays(endDate, 7);

  const { data: trendsData = [], isLoading } = useQuery({
    queryKey: ['parameter-trends', selectedKiln, startDate, endDate],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('kiln_monitoring_measurements')
        .select('*')
        .eq('kiln_id', selectedKiln)
        .gte('measurement_date', format(startDate, 'yyyy-MM-dd'))
        .lte('measurement_date', format(endDate, 'yyyy-MM-dd'))
        .order('measurement_date')
        .order('measurement_time');

      if (error) {
        console.error('Error fetching trends data:', error);
        return [];
      }

      // Transform data for chart
      const chartData = data.map(measurement => {
        const timestamp = `${measurement.measurement_date} ${measurement.measurement_time}`;
        const result: any = { timestamp };
        
        PARAMETER_TESTS.forEach(test => {
          const paramValue = (measurement.parameters as Record<string, number>)[test];
          if (paramValue !== undefined) {
            result[test] = paramValue;
          }
        });

        return result;
      });

      return chartData;
    },
  });

  return (
    <Card>
      <CardHeader>
        <CardTitle>Parameter Trends (Last 7 Days)</CardTitle>
        <div className="flex items-center gap-4">
          <Select value={selectedKiln} onValueChange={setSelectedKiln}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {KILN_OPTIONS.map((kiln) => (
                <SelectItem key={kiln.id} value={kiln.id}>
                  {kiln.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-96 w-full">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={trendsData}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis 
                dataKey="timestamp" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={80}
              />
              <YAxis />
              <Tooltip />
              <Legend />
              {PARAMETER_TESTS.map((test, index) => (
                <Line
                  key={test}
                  type="monotone"
                  dataKey={test}
                  stroke={COLORS[index % COLORS.length]}
                  strokeWidth={2}
                  dot={{ r: 3 }}
                  connectNulls={false}
                />
              ))}
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
};
