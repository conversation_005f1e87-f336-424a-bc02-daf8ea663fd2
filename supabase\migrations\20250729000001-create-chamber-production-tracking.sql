-- Create chamber_production_tracking table for daily chamber production data
CREATE TABLE public.chamber_production_tracking (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  date DATE NOT NULL,
  kiln_id TEXT NOT NULL REFERENCES public.kilns(id),
  chamber_number INTEGER NOT NULL,
  bricks_set INTEGER DEFAULT 0,
  bricks_dehacked INTEGER DEFAULT 0,
  output INTEGER GENERATED ALWAYS AS (bricks_dehacked) STORED,
  breakage INTEGER GENERATED ALWAYS AS (bricks_set - bricks_dehacked) STORED,
  notes TEXT,
  user_id UUID REFERENCES public.users(id),
  UNIQUE(date, kiln_id, chamber_number)
);

-- Add Row Level Security
ALTER TABLE public.chamber_production_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all chamber production tracking" ON public.chamber_production_tracking
  FOR SELECT USING (true);

CREATE POLICY "Users can insert chamber production tracking" ON public.chamber_production_tracking
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update chamber production tracking" ON public.chamber_production_tracking
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete chamber production tracking" ON public.chamber_production_tracking
  FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX idx_chamber_production_tracking_date ON public.chamber_production_tracking(date);
CREATE INDEX idx_chamber_production_tracking_kiln_chamber ON public.chamber_production_tracking(kiln_id, chamber_number);
CREATE INDEX idx_chamber_production_tracking_date_kiln ON public.chamber_production_tracking(date, kiln_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_chamber_production_tracking_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_chamber_production_tracking_updated_at
  BEFORE UPDATE ON public.chamber_production_tracking
  FOR EACH ROW
  EXECUTE FUNCTION update_chamber_production_tracking_updated_at();
