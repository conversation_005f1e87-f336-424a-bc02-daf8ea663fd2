
import { Dialog, <PERSON><PERSON><PERSON>ontent, Di<PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { PaymentWithEmployeeName } from "@/data/paymentsData";
import { getStatusBadgeClass } from "./utils";

interface ViewPaymentDialogProps {
  payment: PaymentWithEmployeeName | null;
  onOpenChange: (isOpen: boolean) => void;
}

export const ViewPaymentDialog = ({ payment, onOpenChange }: ViewPaymentDialogProps) => {
  return (
    <Dialog open={!!payment} onOpenChange={onOpenChange}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Payment Details</DialogTitle>
        </DialogHeader>
        {payment && (
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="text-sm font-medium text-gray-500">Payment ID</label>
                <p className="font-mono text-xs">{payment.id}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Employee</label>
                <p>{payment.employee_name}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Amount</label>
                <p className="font-medium">R{Number(payment.amount).toLocaleString()}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Type</label>
                <p>{payment.payment_type}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Date</label>
                <p>{payment.payment_date}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">Status</label>
                <Badge className={getStatusBadgeClass(payment.status)}>
                  {payment.status}
                </Badge>
              </div>
              {payment.notes && (
                <div className="col-span-2">
                  <label className="text-sm font-medium text-gray-500">Notes</label>
                  <p>{payment.notes}</p>
                </div>
              )}
            </div>
          </div>
        )}
      </DialogContent>
    </Dialog>
  );
};
