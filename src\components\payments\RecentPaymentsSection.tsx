
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CreditCard, Download, Eye, Receipt, Search } from "lucide-react";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { PaymentWithEmployeeName } from "@/data/paymentsData";
import { getStatusBadgeClass } from "./utils";

interface RecentPaymentsSectionProps {
    payments: PaymentWithEmployeeName[];
    onViewPayment: (payment: PaymentWithEmployeeName) => void;
}

export const RecentPaymentsSection = ({ payments, onViewPayment }: RecentPaymentsSectionProps) => {
    const { toast } = useToast();
    const [searchTerm, setSearchTerm] = useState("");
    const [statusFilter, setStatusFilter] = useState("all");
    const [typeFilter, setTypeFilter] = useState("all");

    const filteredPayments = payments.filter(payment => {
        const matchesSearch = payment.employee_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            payment.id.toLowerCase().includes(searchTerm.toLowerCase());
        const matchesStatus = statusFilter === "all" || payment.status.toLowerCase() === statusFilter;
        const matchesType = typeFilter === "all" || payment.payment_type.toLowerCase() === typeFilter;
        return matchesSearch && matchesStatus && matchesType;
    });

    const handleDownloadReceipt = (payment: any) => {
        toast({
            title: "Receipt Downloaded",
            description: `Receipt for ${payment.id.substring(0, 8)}... has been downloaded.`,
        });
    };

    const handleExportPayments = () => {
        toast({
            title: "Export Started",
            description: "Payment data is being exported to Excel.",
        });
    };
    
    return (
        <Card>
            <CardHeader>
                <CardTitle className="flex items-center gap-2">
                    <CreditCard size={20} />
                    Recent Payments
                </CardTitle>
            </CardHeader>
            <CardContent>
                <div className="flex gap-4 mb-6">
                    <div className="relative flex-1">
                        <Search size={16} className="absolute left-3 top-3 text-slate-400" />
                        <Input
                            placeholder="Search payments..."
                            className="pl-10"
                            value={searchTerm}
                            onChange={(e) => setSearchTerm(e.target.value)}
                        />
                    </div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                        <SelectTrigger className="w-40">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Status</SelectItem>
                            <SelectItem value="Paid">Paid</SelectItem>
                            <SelectItem value="Pending">Pending</SelectItem>
                            <SelectItem value="Processing">Processing</SelectItem>
                            <SelectItem value="Failed">Failed</SelectItem>
                        </SelectContent>
                    </Select>
                    <Select value={typeFilter} onValueChange={setTypeFilter}>
                        <SelectTrigger className="w-40">
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="all">All Types</SelectItem>
                            <SelectItem value="Salary">Salary</SelectItem>
                            <SelectItem value="Overtime">Overtime</SelectItem>
                            <SelectItem value="Bonus">Bonus</SelectItem>
                            <SelectItem value="Dehacking">Dehacking</SelectItem>
                            <SelectItem value="Setting">Setting</SelectItem>
                            <SelectItem value="Adjustment">Adjustment</SelectItem>
                        </SelectContent>
                    </Select>
                    <Button variant="outline" onClick={handleExportPayments}>
                        <Download size={16} className="mr-2" />
                        Export
                    </Button>
                </div>

                <div className="overflow-x-auto">
                    <table className="w-full">
                        <thead>
                            <tr className="border-b">
                                <th className="text-left py-3 px-4">Payment ID</th>
                                <th className="text-left py-3 px-4">Employee</th>
                                <th className="text-left py-3 px-4">Amount</th>
                                <th className="text-left py-3 px-4">Type</th>
                                <th className="text-left py-3 px-4">Date</th>
                                <th className="text-left py-3 px-4">Status</th>
                                <th className="text-left py-3 px-4">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {filteredPayments.map((payment) => (
                                <tr key={payment.id} className="border-b hover:bg-slate-50">
                                    <td className="py-3 px-4 font-medium font-mono text-xs">{payment.id}</td>
                                    <td className="py-3 px-4">{payment.employee_name}</td>
                                    <td className="py-3 px-4 font-medium">R{Number(payment.amount).toLocaleString()}</td>
                                    <td className="py-3 px-4">{payment.payment_type}</td>
                                    <td className="py-3 px-4">{payment.payment_date}</td>
                                    <td className="py-3 px-4">
                                        <Badge className={getStatusBadgeClass(payment.status)}>
                                            {payment.status}
                                        </Badge>
                                    </td>
                                    <td className="py-3 px-4">
                                        <div className="flex gap-2">
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => onViewPayment(payment)}
                                            >
                                                <Eye size={14} className="mr-1" />
                                                View
                                            </Button>
                                            <Button
                                                variant="outline"
                                                size="sm"
                                                onClick={() => handleDownloadReceipt(payment)}
                                            >
                                                <Receipt size={14} className="mr-1" />
                                                Receipt
                                            </Button>
                                        </div>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {filteredPayments.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                        No payments found matching your criteria.
                    </div>
                )}
            </CardContent>
        </Card>
    );
};
