
-- Create table for finished product counts
CREATE TABLE public.finished_product_counts (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  date DATE NOT NULL,
  pallet_count INTEGER NOT NULL,
  product_type TEXT NOT NULL CHECK (product_type IN (
    'Imperial 2nd Grade', 
    'Imperial Nfp', 
    'Imperial NFX', 
    'Imperial Blue', 
    'Maxi 2nd Grade', 
    'Maxi NFP', 
    'Maxi NFX'
  )),
  notes TEXT,
  user_id UUID NOT NULL REFERENCES public.users(id)
);

-- Add Row Level Security
ALTER TABLE public.finished_product_counts ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all finished product counts" ON public.finished_product_counts
  FOR SELECT USING (true);

CREATE POLICY "Users can insert finished product counts" ON public.finished_product_counts
  FOR INSERT WITH CHECK (auth.uid()::text IN (SELECT id::text FROM public.users));

-- Create index for better performance
CREATE INDEX idx_finished_product_counts_date ON public.finished_product_counts(date);
CREATE INDEX idx_finished_product_counts_user_id ON public.finished_product_counts(user_id);
