import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "../ui/dialog";
import { But<PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Badge } from "../ui/badge";
import { toast } from "sonner";
import { ManagementBrickType } from "@/data/managementBrickTypes";
import { Package, Plus, Minus, AlertTriangle, CheckCircle } from "lucide-react";

interface StockManagementDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  brickType: ManagementBrickType | null;
}

const StockManagementDialog: React.FC<StockManagementDialogProps> = ({ 
  open, 
  onOpenChange,
  brickType
}) => {
  const [adjustmentType, setAdjustmentType] = useState<'add' | 'remove'>('add');
  const [quantity, setQuantity] = useState('');
  const [reason, setReason] = useState('');

  // Mock stock data - in real app, this would come from database
  const mockStockData = {
    currentStock: 1250,
    reservedStock: 200,
    availableStock: 1050,
    reorderLevel: 500,
    maxStock: 2000,
    lastUpdated: new Date().toLocaleDateString()
  };

  const handleStockAdjustment = () => {
    if (!quantity || parseInt(quantity) <= 0) {
      toast.error("Please enter a valid quantity");
      return;
    }

    if (!reason.trim()) {
      toast.error("Please provide a reason for the stock adjustment");
      return;
    }

    const adjustmentQty = parseInt(quantity);
    const action = adjustmentType === 'add' ? 'added' : 'removed';
    
    toast.success(`Successfully ${action} ${adjustmentQty} units of ${brickType?.name}. Reason: ${reason}`);
    
    // Reset form
    setQuantity('');
    setReason('');
    onOpenChange(false);
  };

  const getStockStatus = () => {
    const { currentStock, reorderLevel } = mockStockData;
    if (currentStock <= reorderLevel) {
      return { status: 'Low Stock', color: 'bg-red-100 text-red-800', icon: AlertTriangle };
    }
    return { status: 'In Stock', color: 'bg-green-100 text-green-800', icon: CheckCircle };
  };

  const stockStatus = getStockStatus();
  const StatusIcon = stockStatus.icon;

  if (!brickType) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package size={20} />
            Stock Management: {brickType.name}
          </DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          {/* Current Stock Overview */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Current Stock Status</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <div className="text-2xl font-bold text-slate-800">{mockStockData.currentStock.toLocaleString()}</div>
                  <div className="text-sm text-slate-600">Total Stock</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{mockStockData.availableStock.toLocaleString()}</div>
                  <div className="text-sm text-slate-600">Available</div>
                </div>
              </div>
              
              <div className="flex items-center justify-between">
                <Badge className={stockStatus.color}>
                  <StatusIcon size={14} className="mr-1" />
                  {stockStatus.status}
                </Badge>
                <span className="text-sm text-slate-500">
                  Last updated: {mockStockData.lastUpdated}
                </span>
              </div>

              <div className="grid grid-cols-3 gap-2 text-sm">
                <div>
                  <div className="font-medium text-slate-700">{mockStockData.reservedStock}</div>
                  <div className="text-slate-500">Reserved</div>
                </div>
                <div>
                  <div className="font-medium text-slate-700">{mockStockData.reorderLevel}</div>
                  <div className="text-slate-500">Reorder Level</div>
                </div>
                <div>
                  <div className="font-medium text-slate-700">{mockStockData.maxStock}</div>
                  <div className="text-slate-500">Max Capacity</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Stock Adjustment */}
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm font-medium">Stock Adjustment</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Adjustment Type */}
              <div className="flex gap-2">
                <Button
                  variant={adjustmentType === 'add' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdjustmentType('add')}
                  className="flex-1"
                >
                  <Plus size={16} className="mr-1" />
                  Add Stock
                </Button>
                <Button
                  variant={adjustmentType === 'remove' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setAdjustmentType('remove')}
                  className="flex-1"
                >
                  <Minus size={16} className="mr-1" />
                  Remove Stock
                </Button>
              </div>

              {/* Quantity Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Quantity</label>
                <Input
                  type="number"
                  placeholder="Enter quantity"
                  value={quantity}
                  onChange={(e) => setQuantity(e.target.value)}
                  min="1"
                />
              </div>

              {/* Reason Input */}
              <div className="space-y-2">
                <label className="text-sm font-medium">Reason</label>
                <Input
                  placeholder="e.g., Production, Damage, Sale, etc."
                  value={reason}
                  onChange={(e) => setReason(e.target.value)}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            Cancel
          </Button>
          <Button onClick={handleStockAdjustment}>
            {adjustmentType === 'add' ? 'Add' : 'Remove'} Stock
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default StockManagementDialog;
