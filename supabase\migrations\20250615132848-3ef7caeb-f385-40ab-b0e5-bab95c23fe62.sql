
-- 1. Create table for fuel dispensing transactions

CREATE TABLE public.fuel_dispensing_transactions (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  fuel_bunker_id text NOT NULL REFERENCES fuel_bunkers(id),
  asset_id text NOT NULL REFERENCES assets(id),
  operator_id integer NOT NULL REFERENCES employees(id),
  transaction_date date NOT NULL DEFAULT CURRENT_DATE,
  starting_reading numeric,
  ending_reading numeric,
  quantity_liters numeric NOT NULL,
  notes text,
  created_at timestamptz NOT NULL DEFAULT NOW()
);

-- 2. Indexes for faster queries
CREATE INDEX idx_fuel_dispensing_bunker ON public.fuel_dispensing_transactions (fuel_bunker_id);
CREATE INDEX idx_fuel_dispensing_asset ON public.fuel_dispensing_transactions (asset_id);

-- 3. (Recommended) Enable RLS for privacy if authentication is in use
-- ALTER TABLE public.fuel_dispensing_transactions ENABLE ROW LEVEL SECURITY;

