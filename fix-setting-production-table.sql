-- ========================================
-- FIX SETTING PRODUCTION TABLE
-- ========================================
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and click RUN

-- Step 1: Check current table structure
SELECT 'Current setting_production_entries table structure:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'setting_production_entries' 
ORDER BY ordinal_position;

-- Step 2: Add hour column if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns 
                   WHERE table_name = 'setting_production_entries' 
                   AND column_name = 'hour') THEN
        -- Add hour column
        ALTER TABLE public.setting_production_entries 
        ADD COLUMN hour INTEGER;
        
        -- Add comment
        COMMENT ON COLUMN public.setting_production_entries.hour IS 'Hour of the day when production was recorded (0-23)';
        
        RAISE NOTICE 'Hour column added successfully to setting_production_entries table';
    ELSE
        RAISE NOTICE 'Hour column already exists in setting_production_entries table';
    END IF;
END $$;

-- Step 3: Verify the table structure after changes
SELECT 'Updated setting_production_entries table structure:' as info;
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'setting_production_entries' 
ORDER BY ordinal_position;

-- Step 4: Check foreign key constraints
SELECT 'Foreign key constraints:' as info;
SELECT 
    tc.constraint_name, 
    tc.table_name, 
    kcu.column_name, 
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name 
FROM 
    information_schema.table_constraints AS tc 
    JOIN information_schema.key_column_usage AS kcu
      ON tc.constraint_name = kcu.constraint_name
      AND tc.table_schema = kcu.table_schema
    JOIN information_schema.constraint_column_usage AS ccu
      ON ccu.constraint_name = tc.constraint_name
      AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY' 
AND tc.table_name='setting_production_entries';

-- Step 5: Test insert capability (this should work now)
SELECT 'Testing insert capability...' as info;
-- Note: This is just a test to see if the structure is correct
-- The actual insert will be done by the application
