
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Target, DollarSign, Users } from "lucide-react";

interface MetricsCardsProps {
  totalPallets: number;
  totalEarnings: number;
  activeEmployees: number;
  averagePerEmployee: string;
  periodSubtitle: string;
}

export const MetricsCards = ({ 
  totalPallets, 
  totalEarnings, 
  activeEmployees, 
  averagePerEmployee, 
  periodSubtitle 
}: MetricsCardsProps) => {
  const DEHACKING_TARGET_PER_EMPLOYEE = 12; // per shift

  const metrics = [
    {
      title: "Total Pallets",
      value: String(totalPallets),
      subtitle: periodSubtitle,
      icon: Target,
      target: null,
    },
    {
      title: "Total Earnings",
      value: `R ${totalEarnings.toFixed(2)}`,
      subtitle: periodSubtitle,
      icon: DollarSign,
      target: null,
    },
    {
      title: "Active Employees",
      value: String(activeEmployees),
      subtitle: `Active ${periodSubtitle.toLowerCase()}`,
      icon: Users,
      target: null,
    },
    {
      title: "Average Per Employee",
      value: averagePerEmployee,
      subtitle: `Target: ${DEHACKING_TARGET_PER_EMPLOYEE} pallets/shift`,
      icon: Target,
      target: DEHACKING_TARGET_PER_EMPLOYEE,
    },
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
      {metrics.map((metric) => {
        const Icon = metric.icon;
        return (
          <Card key={metric.title}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-slate-600">
                {metric.title}
              </CardTitle>
              <Icon size={16} className="text-slate-400" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-slate-800">{metric.value}</div>
              <p className="text-xs text-slate-500 mt-1">{metric.subtitle}</p>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
