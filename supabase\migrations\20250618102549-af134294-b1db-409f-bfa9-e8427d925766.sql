
-- Create users table if it doesn't exist (using lowercase enum values)
CREATE TABLE IF NOT EXISTS public.users (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  username VARCHAR UNIQUE NOT NULL,
  full_name VARCHAR NOT NULL,
  email VARCHAR,
  password_hash VARCHAR NOT NULL,
  role user_role DEFAULT 'admin',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Create notification settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.notification_settings (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  email_notifications BOOLEAN DEFAULT true,
  production_alerts BOOLEAN DEFAULT true,
  fuel_level_warnings BOOLEAN DEFAULT true,
  employee_updates BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(user_id)
);

-- Create system configuration table if it doesn't exist
CREATE TABLE IF NOT EXISTS public.system_config (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  company_name VARCHAR DEFAULT 'Worcester Bakstone',
  timezone VARCHAR DEFAULT 'South Africa Standard Time (UTC+2)',
  currency VARCHAR DEFAULT 'ZAR (South African Rand)',
  auto_backup BOOLEAN DEFAULT true,
  theme VARCHAR DEFAULT 'Light',
  language VARCHAR DEFAULT 'English',
  dashboard_layout VARCHAR DEFAULT 'Standard',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);

-- Insert default system configuration if not exists
INSERT INTO public.system_config (company_name) 
SELECT 'Worcester Bakstone'
WHERE NOT EXISTS (SELECT 1 FROM public.system_config);

-- Enable Row Level Security
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.notification_settings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.system_config ENABLE ROW LEVEL SECURITY;

-- Drop existing policies if they exist and recreate them
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;
DROP POLICY IF EXISTS "Administrators can view all users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Administrators can manage all users" ON public.users;
DROP POLICY IF EXISTS "Users can manage their own notification settings" ON public.notification_settings;
DROP POLICY IF EXISTS "Administrators can manage system config" ON public.system_config;
DROP POLICY IF EXISTS "All users can view system config" ON public.system_config;

-- Create security definer function to get current user role
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS user_role
LANGUAGE plpgsql
STABLE SECURITY DEFINER
AS $$
DECLARE
  current_role user_role;
BEGIN
  SELECT role INTO current_role
  FROM public.users 
  WHERE id = auth.uid();
  
  RETURN current_role;
END;
$$;

-- RLS Policies for users table
CREATE POLICY "Users can view their own profile" 
  ON public.users 
  FOR SELECT 
  USING (auth.uid() = id);

CREATE POLICY "Administrators can view all users" 
  ON public.users 
  FOR SELECT 
  USING (public.get_current_user_role() = 'admin');

CREATE POLICY "Users can update their own profile" 
  ON public.users 
  FOR UPDATE 
  USING (auth.uid() = id);

CREATE POLICY "Administrators can manage all users" 
  ON public.users 
  FOR ALL 
  USING (public.get_current_user_role() = 'admin');

-- RLS Policies for notification settings
CREATE POLICY "Users can manage their own notification settings" 
  ON public.notification_settings 
  FOR ALL 
  USING (auth.uid() = user_id);

-- RLS Policies for system config
CREATE POLICY "Administrators can manage system config" 
  ON public.system_config 
  FOR ALL 
  USING (public.get_current_user_role() = 'admin');

CREATE POLICY "All users can view system config" 
  ON public.system_config 
  FOR SELECT 
  TO authenticated;

-- Insert a default admin user for testing (password: admin123)
INSERT INTO public.users (username, full_name, email, password_hash, role) 
SELECT 'admin', 'System Administrator', '<EMAIL>', '$2b$10$rQ8QzKjLFoFv.VuF5YnYsuVnq1xSgQqoFZPQjYqXqWrJqLvq8G8Ni', 'admin'
WHERE NOT EXISTS (SELECT 1 FROM public.users WHERE username = 'admin');
