import React, { useState } from 'react';
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

interface QuickAccessCardProps {
  title: string;
  description: string;
  buttonText: string;
  onClick: () => void;
}

const QuickAccessCard: React.FC<QuickAccessCardProps> = ({
  title,
  description,
  buttonText,
  onClick,
}) => {
  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle>{title}</CardTitle>
        <CardDescription>{description}</CardDescription>
      </CardHeader>
      <CardContent>
        {/* Card content can be added here if needed */}
      </CardContent>
      <CardFooter>
        <Button onClick={onClick} className="w-full">
          {buttonText}
        </Button>
      </CardFooter>
    </Card>
  );
};

export const QuickAccessCards: React.FC = () => {
  const [showAddBrickDialog, setShowAddBrickDialog] = useState(false);
  const [brickName, setBrickName] = useState('');
  const [category, setCategory] = useState('');
  const [grade, setGrade] = useState('');
  const [settingRate, setSettingRate] = useState('0');
  const [dehackingRate, setDehackingRate] = useState('0');
  const [overtimeRate, setOvertimeRate] = useState('0');

  const handleAddBrick = () => {
    setShowAddBrickDialog(true);
  };

  const handleCloseDialog = () => {
    setShowAddBrickDialog(false);
    // Reset form fields
    setBrickName('');
    setCategory('');
    setGrade('');
    setSettingRate('0');
    setDehackingRate('0');
    setOvertimeRate('0');
  };

  const handleCreateBrickType = () => {
    // Here you would handle the submission of the new brick type
    console.log({
      brickName,
      category,
      grade,
      settingRate,
      dehackingRate,
      overtimeRate
    });
    
    // Close the dialog after submission
    handleCloseDialog();
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <QuickAccessCard
        title="Add Brick Type"
        description="Add a new brick type to the system"
        buttonText="Add Brick Type"
        onClick={handleAddBrick}
      />
      <QuickAccessCard
        title="Manage Teams"
        description="View and manage your teams"
        buttonText="Manage Teams"
        onClick={() => console.log("Manage Teams clicked")}
      />
      <QuickAccessCard
        title="View Reports"
        description="Access and download reports"
        buttonText="View Reports"
        onClick={() => console.log("View Reports clicked")}
      />

      {/* Add Brick Type Dialog */}
      <Dialog open={showAddBrickDialog} onOpenChange={setShowAddBrickDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Add New Brick Type</DialogTitle>
          </DialogHeader>
          
          <div className="grid gap-4 py-4">
            <div className="grid gap-2">
              <label htmlFor="brickName">Brick Name</label>
              <Input 
                id="brickName" 
                placeholder="Enter brick type name" 
                value={brickName}
                onChange={(e) => setBrickName(e.target.value)}
              />
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="grid gap-2">
                <label htmlFor="category">Category</label>
                <Select value={category} onValueChange={setCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="category1">Category 1</SelectItem>
                    <SelectItem value="category2">Category 2</SelectItem>
                    <SelectItem value="category3">Category 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              
              <div className="grid gap-2">
                <label htmlFor="grade">Grade</label>
                <Select value={grade} onValueChange={setGrade}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select grade" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="grade1">Grade 1</SelectItem>
                    <SelectItem value="grade2">Grade 2</SelectItem>
                    <SelectItem value="grade3">Grade 3</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="settingRate">Setting Rate (R per pallet)</label>
              <Input 
                id="settingRate" 
                type="number" 
                value={settingRate}
                onChange={(e) => setSettingRate(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="dehackingRate">Dehacking Rate (R per pallet)</label>
              <Input 
                id="dehackingRate" 
                type="number" 
                value={dehackingRate}
                onChange={(e) => setDehackingRate(e.target.value)}
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="overtimeRate">Overtime Rate (R per pallet)</label>
              <Input 
                id="overtimeRate" 
                type="number" 
                value={overtimeRate}
                onChange={(e) => setOvertimeRate(e.target.value)}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={handleCloseDialog}>Cancel</Button>
            <Button onClick={handleCreateBrickType}>Create Brick Type</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};