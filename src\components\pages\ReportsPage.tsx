import { useState, useMemo } from "react";
import { TimeRange, CustomDateRange } from "@/components/dashboard/DashboardContent";
import { ReportPreviewDialog } from "@/components/reports/ReportPreviewDialog";
import { ForkliftPerformanceDashboard } from "@/components/dashboard/ForkliftPerformanceDashboard";
import { useReports } from "@/hooks/useReports";
import { ReportHeader } from "@/components/reports/ReportHeader";
import { ReportControls } from "@/components/reports/ReportControls";
import { ReportChart } from "@/components/reports/ReportChart";
import { useUser } from "@/contexts/UserContext";

export type ReportType = 
  | "factory-output" 
  | "setting-teams" 
  | "dehacking" 
  | "fuel-management" 
  | "employee-hours"
  | "user-activity";

const reportTypes = [
  { value: "factory-output" as ReportType, label: "Factory Output" },
  { value: "setting-teams" as ReportType, label: "Setting Teams" },
  { value: "dehacking" as ReportType, label: "Dehacking" },
  { value: "fuel-management" as ReportType, label: "Fuel Management" },
  { value: "employee-hours" as ReportType, label: "Employee Hours & Earnings" },
  { value: "user-activity" as ReportType, label: "User Activity" },
];

export const ReportsPage = () => {
  const { canAccessReportType } = useUser();
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>("month");
  const [selectedReportType, setSelectedReportType] = useState<ReportType>("factory-output");
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [customDateRange, setCustomDateRange] = useState<CustomDateRange>({ from: "", to: "" });

  // Filter report types based on user access
  const accessibleReportTypes = useMemo(() => {
    return reportTypes.filter(type => canAccessReportType(type.value));
  }, [canAccessReportType]);

  // Set default report type based on accessible reports
  useMemo(() => {
    if (accessibleReportTypes.length > 0 && !canAccessReportType(selectedReportType)) {
      setSelectedReportType(accessibleReportTypes[0].value);
    }
  }, [accessibleReportTypes, canAccessReportType, selectedReportType]);

  const { data: reportData, isLoading, isError } = useReports(selectedTimeRange, selectedReportType, customDateRange);

  const handleGenerateReport = () => {
    setIsPreviewOpen(true);
  };

  return (
    <div className="space-y-6">
      <ReportHeader onGenerateReport={handleGenerateReport} isLoading={isLoading} />

      <ReportControls
        selectedTimeRange={selectedTimeRange}
        onTimeRangeChange={setSelectedTimeRange}
        selectedReportType={selectedReportType}
        onReportTypeChange={setSelectedReportType}
        reportTypes={accessibleReportTypes}
        customDateRange={customDateRange}
        onCustomDateChange={setCustomDateRange}
      />

      <ReportChart
        reportData={reportData}
        isLoading={isLoading}
        isError={isError}
        selectedReportType={selectedReportType}
        selectedTimeRange={selectedTimeRange}
        reportTypes={accessibleReportTypes}
      />

      <ForkliftPerformanceDashboard />

      <ReportPreviewDialog
        isOpen={isPreviewOpen}
        onClose={() => setIsPreviewOpen(false)}
        reportData={reportData}
        timeRange={selectedTimeRange}
        reportType={selectedReportType}
        reportTitle={reportTypes.find(type => type.value === selectedReportType)?.label || "Report"}
      />
    </div>
  );
};

export default ReportsPage;
