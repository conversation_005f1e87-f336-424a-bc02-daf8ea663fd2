
import React from "react";
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { recordFuelDelivery } from "@/data/fuelDeliveriesData";
import { FuelBunkerRadioGroup } from "@/components/fuel/FuelBunkerRadioGroup";
import { useQueryClient } from "@tanstack/react-query";

interface RecordFuelDeliveryDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bunkers: { id: string; name: string }[];
}

export const RecordFuelDeliveryDialog = ({
  isOpen,
  onClose,
  bunkers,
}: RecordFuelDeliveryDialogProps) => {
  const queryClient = useQueryClient();
  const [selectedBunker, setSelectedBunker] = useState<string>("");
  const [supplier, setSupplier] = useState("");
  const [invoiceNumber, setInvoiceNumber] = useState("");
  const [quantity, setQuantity] = useState("");
  const [costPerLiter, setCostPerLiter] = useState("");
  const [date, setDate] = useState<Date | undefined>(new Date());
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();

  // Reset on close/open
  React.useEffect(() => {
    if (!isOpen) {
      setSelectedBunker("");
      setSupplier("");
      setInvoiceNumber("");
      setQuantity("");
      setCostPerLiter("");
      setDate(new Date());
      setLoading(false);
    }
  }, [isOpen]);

  const handleSubmit = async () => {
    setLoading(true);
    // Basic validation
    if (!selectedBunker || !supplier || !invoiceNumber || !quantity || !costPerLiter || !date) {
      toast({ title: "Missing info", description: "Please complete all fields.", variant: "destructive" });
      setLoading(false);
      return;
    }
    // Insert into DB
    const deliveryDate = format(date, "yyyy-MM-dd"); // Date string only
    const { error } = await recordFuelDelivery({
      fuel_bunker_id: selectedBunker,
      supplier,
      invoice_number: invoiceNumber,
      quantity: Number(quantity),
      cost_per_liter: Number(costPerLiter),
      delivery_date: deliveryDate,
    });

    if (error) {
      toast({
        title: "Error recording delivery",
        description: error.message || "Database error. Please try again.",
        variant: "destructive",
      });
    } else {
      toast({
        title: "Delivery recorded",
        description: "Fuel delivery has been saved.",
      });
      queryClient.invalidateQueries({ queryKey: ['fuelBunkers'] });
      onClose();
    }
    setLoading(false);
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle>Record Fuel Delivery</DialogTitle>
          <DialogDescription>Enter fuel delivery details</DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {/* Radio group for fuel bunkers */}
          <FuelBunkerRadioGroup value={selectedBunker} onChange={setSelectedBunker} />

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="supplier">Supplier</Label>
              <Input
                id="supplier"
                value={supplier}
                onChange={(e) => setSupplier(e.target.value)}
                placeholder="e.g., Fuel Co."
                disabled={loading}
              />
            </div>
            <div>
              <Label htmlFor="invoiceNumber">Invoice Number</Label>
              <Input
                id="invoiceNumber"
                value={invoiceNumber}
                onChange={(e) => setInvoiceNumber(e.target.value)}
                placeholder="e.g., INV-2025-001"
                disabled={loading}
              />
            </div>
          </div>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="quantity">Quantity (Liters)</Label>
              <Input
                id="quantity"
                type="number"
                value={quantity}
                onChange={(e) => setQuantity(e.target.value)}
                placeholder="e.g., 5000"
                disabled={loading}
                min="0"
              />
            </div>
            <div>
              <Label htmlFor="costPerLiter">Cost per Liter</Label>
              <Input
                id="costPerLiter"
                type="number"
                value={costPerLiter}
                onChange={(e) => setCostPerLiter(e.target.value)}
                placeholder="e.g., 1.50"
                disabled={loading}
                min="0"
                step="any"
              />
            </div>
          </div>
          <div>
            <Label htmlFor="deliveryDate">Date</Label>
             <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !date && "text-muted-foreground"
                  )}
                  disabled={loading}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "yyyy/MM/dd") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={setDate}
                  initialFocus
                  disabled={loading}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? "Saving..." : "Record Delivery"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
