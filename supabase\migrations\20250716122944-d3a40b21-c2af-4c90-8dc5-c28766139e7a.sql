-- Fix RLS policies for chamber_fire_status table
DROP POLICY IF EXISTS "Allow all users to view chamber fire status" ON public.chamber_fire_status;
DROP POLICY IF EXISTS "Allow authenticated users to update chamber fire status" ON public.chamber_fire_status;

-- Create simpler, more permissive policies
CREATE POLICY "Allow all operations on chamber_fire_status" 
ON public.chamber_fire_status 
FOR ALL 
USING (true) 
WITH CHECK (true);