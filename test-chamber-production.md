# Chamber Production Tracking - Testing Guide

## Overview
This guide helps you test the new chamber production tracking functionality that has been added to the Kiln Monitoring Daily Summary tab.

## Database Setup

### 1. Create the Database Table
Run the following SQL in your Supabase SQL Editor:

```sql
-- Create chamber_production_tracking table for daily chamber production data
CREATE TABLE public.chamber_production_tracking (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  date DATE NOT NULL,
  kiln_id TEXT NOT NULL REFERENCES public.kilns(id),
  chamber_number INTEGER NOT NULL,
  bricks_set INTEGER DEFAULT 0,
  bricks_dehacked INTEGER DEFAULT 0,
  output INTEGER GENERATED ALWAYS AS (bricks_dehacked) STORED,
  breakage INTEGER GENERATED ALWAYS AS (bricks_set - bricks_dehacked) STORED,
  notes TEXT,
  user_id UUID REFERENCES public.users(id),
  UNIQUE(date, kiln_id, chamber_number)
);

-- Add Row Level Security
ALTER TABLE public.chamber_production_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view all chamber production tracking" ON public.chamber_production_tracking
  FOR SELECT USING (true);

CREATE POLICY "Users can insert chamber production tracking" ON public.chamber_production_tracking
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update chamber production tracking" ON public.chamber_production_tracking
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete chamber production tracking" ON public.chamber_production_tracking
  FOR DELETE USING (true);

-- Create indexes for better performance
CREATE INDEX idx_chamber_production_tracking_date ON public.chamber_production_tracking(date);
CREATE INDEX idx_chamber_production_tracking_kiln_chamber ON public.chamber_production_tracking(kiln_id, chamber_number);
CREATE INDEX idx_chamber_production_tracking_date_kiln ON public.chamber_production_tracking(date, kiln_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_chamber_production_tracking_updated_at()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at
CREATE TRIGGER update_chamber_production_tracking_updated_at
  BEFORE UPDATE ON public.chamber_production_tracking
  FOR EACH ROW
  EXECUTE FUNCTION update_chamber_production_tracking_updated_at();
```

## Testing Steps

### 1. Navigate to Kiln Monitoring
1. Start the application (`npm run dev`)
2. Navigate to the Kiln Monitoring section
3. Click on the "Daily Summary" tab

### 2. Verify Chamber Production Table
You should see a new "Chamber Production Tracking" table at the top of the Daily Summary tab with:
- Date selector (defaults to today)
- Tables for each kiln (Habla, Kiln 1-5)
- Columns for: Chamber, Bricks Set, Bricks Dehacked, Output, Breakage, Breakage %
- Daily Summary section with totals

### 3. Test Data Entry
1. Select a date using the date picker
2. Enter values in the "Bricks Set" and "Bricks Dehacked" columns for various chambers
3. Verify that:
   - Output automatically equals Bricks Dehacked
   - Breakage automatically calculates as (Bricks Set - Bricks Dehacked)
   - Breakage percentage is calculated correctly
   - Values turn red when breakage is high (>5%)

### 4. Test Data Persistence
1. Enter some data and click "Save All Changes"
2. Refresh the page or navigate away and back
3. Verify that the data persists

### 5. Test Integration with Existing Data
1. If you have existing setting and dehacking entries for the selected date:
   - The table should automatically populate with calculated values
   - Setting entries contribute to "Bricks Set"
   - Dehacking entries contribute to "Bricks Dehacked"

### 6. Test Daily Summary
1. Enter data for multiple chambers across different kilns
2. Verify that the Daily Summary section shows correct totals:
   - Total Bricks Set
   - Total Bricks Dehacked
   - Total Output
   - Total Breakage
   - Breakage Rate percentage

## Expected Behavior

### Automatic Calculations
- **Output**: Always equals Bricks Dehacked
- **Breakage**: Always equals (Bricks Set - Bricks Dehacked)
- **Breakage %**: (Breakage / Bricks Set) × 100

### Data Integration
- The system automatically pulls data from existing `setting_production_entries` and `dehacking_entries` tables
- Chamber-specific data is matched by `kiln_id` and `chamber_number`
- Brick counts are calculated using `pallet_count × bricks_per_pallet`

### Visual Indicators
- Breakage values > 0 appear in red
- Breakage percentages > 5% appear in red
- Positive values appear in green
- Save button only appears when there are unsaved changes

## Troubleshooting

### If the table doesn't appear:
1. Check browser console for errors
2. Verify the database table was created successfully
3. Check that the component is properly imported in SimplifiedDailySummary.tsx

### If data doesn't save:
1. Check browser console for API errors
2. Verify RLS policies are set up correctly
3. Check that the user has proper permissions

### If calculations are wrong:
1. Verify the database computed columns are working
2. Check that the frontend calculations match the database logic

## Files Modified/Created

### New Files:
- `src/data/chamberProductionData.ts` - Data access functions
- `src/hooks/useChamberProduction.ts` - React hooks for data management
- `src/components/kilns/ChamberProductionTable.tsx` - Main table component
- `supabase/migrations/20250729000001-create-chamber-production-tracking.sql` - Database migration

### Modified Files:
- `src/components/kilns/SimplifiedDailySummary.tsx` - Added chamber production table

## Notes
- The implementation preserves all existing functionality
- The chamber production table appears above the existing parameter tests
- Data is automatically calculated from existing production entries when available
- The breakage calculation follows the specified logic: Breakage = Bricks Set - Bricks Dehacked
