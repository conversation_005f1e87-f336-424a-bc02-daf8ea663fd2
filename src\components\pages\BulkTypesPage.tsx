
import { <PERSON>, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Archive, Plus, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export const BulkTypesPage = () => {
  const bulkTypes = [
    { id: 1, name: "Clay Mix A", category: "Raw Material", density: "1.8 kg/L", color: "Red Brown", status: "Active" },
    { id: 2, name: "Sand Blend B", category: "Aggregate", density: "1.6 kg/L", color: "Light Tan", status: "Active" },
    { id: 3, name: "Fire Clay", category: "Specialty", density: "2.1 kg/L", color: "White", status: "Low Stock" },
    { id: 4, name: "Standard Clay", category: "Raw Material", density: "1.9 kg/L", color: "Brown", status: "Active" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Bulk Types Management</h1>
          <p className="text-slate-600">Manage raw materials and bulk material specifications</p>
        </div>
        <Button className="bg-slate-800 hover:bg-slate-700">
          <Plus size={20} className="mr-2" />
          Add Bulk Type
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-slate-800">24</div>
            <p className="text-slate-600">Total Types</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">18</div>
            <p className="text-slate-600">Active</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-yellow-600">4</div>
            <p className="text-slate-600">Low Stock</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">2</div>
            <p className="text-slate-600">Out of Stock</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Archive size={20} />
            Material Specifications
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search size={16} className="absolute left-3 top-3 text-slate-400" />
              <Input 
                placeholder="Search bulk types..." 
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter by Category</Button>
            <Button variant="outline">Export Data</Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Material Name</th>
                  <th className="text-left py-3 px-4">Category</th>
                  <th className="text-left py-3 px-4">Density</th>
                  <th className="text-left py-3 px-4">Color</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {bulkTypes.map((type) => (
                  <tr key={type.id} className="border-b hover:bg-slate-50">
                    <td className="py-3 px-4 font-medium">{type.name}</td>
                    <td className="py-3 px-4">{type.category}</td>
                    <td className="py-3 px-4">{type.density}</td>
                    <td className="py-3 px-4">
                      <div className="flex items-center gap-2">
                        <div 
                          className="w-4 h-4 rounded border"
                          style={{
                            backgroundColor: type.color === 'Red Brown' ? '#8B4513' :
                                           type.color === 'Light Tan' ? '#D2B48C' :
                                           type.color === 'White' ? '#FFFFFF' : '#A0522D'
                          }}
                        ></div>
                        {type.color}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        type.status === 'Active' 
                          ? 'bg-green-100 text-green-800' 
                          : type.status === 'Low Stock'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {type.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">Edit</Button>
                        <Button variant="outline" size="sm">Specs</Button>
                        <Button variant="outline" size="sm">Stock</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
