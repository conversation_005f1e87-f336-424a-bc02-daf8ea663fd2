
import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { MessageSquare, Save } from "lucide-react";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

interface FireComment {
  id: string;
  fire_id: string;
  date: string;
  comment: string;
  user_id: string;
  created_at: string;
  updated_at: string;
}

interface FireCommentsProps {
  fireId: string;
  fireName: string;
}

export const FireComments = ({ fireId, fireName }: FireCommentsProps) => {
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10));
  const [comment, setComment] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [existingComment, setExistingComment] = useState<FireComment | null>(null);
  const { currentUser } = useAuth();

  // Load existing comment for selected date
  useEffect(() => {
    const loadCommentForDate = async () => {
      try {
        const { data, error } = await supabase
          .from('fire_comments')
          .select('*')
          .eq('fire_id', fireId)
          .eq('date', date)
          .maybeSingle();

        if (error) {
          console.error("Error loading comment:", error);
          return;
        }

        if (data) {
          setComment(data.comment);
          setExistingComment(data);
        } else {
          setComment("");
          setExistingComment(null);
        }
      } catch (error) {
        console.error("Error loading comment:", error);
      }
    };

    loadCommentForDate();
  }, [fireId, date]);

  const handleSave = async () => {
    if (!comment.trim()) {
      toast.error("Please enter a comment");
      return;
    }

    if (!currentUser) {
      toast.error("You must be logged in to save comments");
      return;
    }

    setIsLoading(true);
    try {
      if (existingComment) {
        // Update existing comment
        const { error } = await supabase
          .from('fire_comments')
          .update({ 
            comment: comment.trim(),
            updated_at: new Date().toISOString()
          })
          .eq('id', existingComment.id);

        if (error) throw error;
      } else {
        // Create new comment
        const { data, error } = await supabase
          .from('fire_comments')
          .insert({
            fire_id: fireId,
            date,
            comment: comment.trim(),
            user_id: currentUser.id
          })
          .select()
          .single();

        if (error) throw error;
        setExistingComment(data);
      }
      
      toast.success("Comment saved successfully!");
    } catch (error) {
      console.error("Error saving comment:", error);
      toast.error(`Failed to save comment: ${error.message || 'Unknown error'}`);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card className="mt-3">
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <MessageSquare size={16} />
          {fireName} Comments
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div>
          <Label htmlFor={`date-${fireId}`} className="text-xs">Date</Label>
          <Input
            id={`date-${fireId}`}
            type="date"
            value={date}
            onChange={(e) => setDate(e.target.value)}
            className="text-sm"
          />
        </div>
        <div>
          <Label htmlFor={`comment-${fireId}`} className="text-xs">Comments</Label>
          <Textarea
            id={`comment-${fireId}`}
            value={comment}
            onChange={(e) => setComment(e.target.value)}
            placeholder="Enter daily comments for this fire..."
            className="text-sm min-h-[80px]"
          />
        </div>
        <Button 
          onClick={handleSave} 
          disabled={isLoading || !comment.trim() || !currentUser}
          size="sm"
          className="w-full"
        >
          <Save size={14} className="mr-1" />
          {isLoading ? "Saving..." : "Save Comment"}
        </Button>
        {!currentUser && (
          <div className="text-xs text-red-500">
            Please log in to save comments
          </div>
        )}
        {existingComment && (
          <div className="text-xs text-slate-500 space-y-1">
            <div>Last updated: {new Date(existingComment.updated_at).toLocaleString()}</div>
            <div className="italic text-slate-600">"{existingComment.comment}"</div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
