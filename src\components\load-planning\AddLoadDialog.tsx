
import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useUser } from "@/contexts/UserContext";
import { useQuery } from "@tanstack/react-query";
import { getManagementBrickTypes } from "@/data/managementBrickTypes";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

const loadSchema = z.object({
  date: z.date(),
  clientName: z.string().min(1, "Client name is required"),
  loadDescription: z.string().optional(),
  transporter: z.string().min(1, "Transporter is required"),
  loadType: z.enum(["Strapped", "Pallets"], { required_error: "Load type is required" }),
  brickTypeId: z.string().min(1, "Brick type is required"),
  quantity: z.number().min(1, "Quantity must be at least 1"),
  rank: z.number().min(1, "Rank must be at least 1").max(20, "Rank must be at most 20"),
});

type LoadFormData = z.infer<typeof loadSchema>;

interface AddLoadDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  selectedDate?: Date;
}

export const AddLoadDialog = ({ open, onOpenChange, selectedDate }: AddLoadDialogProps) => {
  const { currentUser } = useUser();
  
  const { data: brickTypes = [], isLoading: brickTypesLoading } = useQuery({
    queryKey: ['managementBrickTypes'],
    queryFn: getManagementBrickTypes,
  });

  const {
    register,
    handleSubmit,
    setValue,
    watch,
    reset,
    formState: { errors, isSubmitting }
  } = useForm<LoadFormData>({
    resolver: zodResolver(loadSchema),
    defaultValues: {
      date: selectedDate || new Date(),
      loadType: "Strapped",
      rank: 1,
    }
  });

  const watchedDate = watch("date");
  const watchedBrickType = watch("brickTypeId");
  const watchedLoadType = watch("loadType");

  const onSubmit = async (data: LoadFormData) => {
    if (!currentUser?.id) {
      toast.error("User not authenticated");
      return;
    }

    try {
      const { error } = await supabase
        .from('load_planning')
        .insert({
          date: format(data.date, 'yyyy-MM-dd'),
          client_name: data.clientName,
          load_description: data.loadDescription || '',
          transporter: data.transporter,
          brick_type_id: data.brickTypeId,
          load_type: data.loadType,
          brick_count: data.quantity,
          rank: data.rank,
          user_id: currentUser.id
        });

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      toast.success("Load added successfully!");
      reset();
      onOpenChange(false);
    } catch (error) {
      console.error("Error adding load:", error);
      toast.error("Failed to add load");
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Add New Load</DialogTitle>
          <p className="text-sm text-muted-foreground">Schedule a new load with brick type and quantity</p>
        </DialogHeader>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="date">Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !watchedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {watchedDate ? format(watchedDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={watchedDate}
                  onSelect={(date) => date && setValue("date", date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
            {errors.date && <p className="text-sm text-red-500">{errors.date.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="clientName">Client Name</Label>
            <Input
              id="clientName"
              {...register("clientName")}
              placeholder="Enter client name"
            />
            {errors.clientName && <p className="text-sm text-red-500">{errors.clientName.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="transporter">Transporter</Label>
            <Input
              id="transporter"
              {...register("transporter")}
              placeholder="Enter transporter name"
            />
            {errors.transporter && <p className="text-sm text-red-500">{errors.transporter.message}</p>}
          </div>

          <div className="space-y-2">
            <Label>Load Type *</Label>
            <RadioGroup 
              value={watchedLoadType} 
              onValueChange={(value) => setValue("loadType", value as "Strapped" | "Pallets")}
              className="flex flex-row space-x-6"
            >
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Strapped" id="strapped" />
                <Label htmlFor="strapped">Strapped</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="Pallets" id="pallets" />
                <Label htmlFor="pallets">Pallets</Label>
              </div>
            </RadioGroup>
            {errors.loadType && <p className="text-sm text-red-500">{errors.loadType.message}</p>}
          </div>

          <div className="space-y-2">
            <Label>Brick Type *</Label>
            <Select 
              value={watchedBrickType || ""} 
              onValueChange={(value) => setValue("brickTypeId", value)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select brick type" />
              </SelectTrigger>
              <SelectContent>
                {brickTypesLoading ? (
                  <SelectItem value="" disabled>Loading brick types...</SelectItem>
                ) : brickTypes.length === 0 ? (
                  <SelectItem value="" disabled>No brick types available</SelectItem>
                ) : (
                  brickTypes.map((brickType) => (
                    <SelectItem key={brickType.id} value={brickType.id}>
                      {brickType.name} ({brickType.category})
                    </SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
            {errors.brickTypeId && <p className="text-sm text-red-500">{errors.brickTypeId.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="quantity">Quantity (Bricks) *</Label>
            <Input
              id="quantity"
              type="number"
              {...register("quantity", { valueAsNumber: true })}
              placeholder="Enter number of bricks"
              min="1"
              step="1"
            />
            {errors.quantity && <p className="text-sm text-red-500">{errors.quantity.message}</p>}
          </div>

          <div className="space-y-2">
            <Label htmlFor="rank">Priority Rank (1-20) *</Label>
            <Input
              id="rank"
              type="number"
              {...register("rank", { valueAsNumber: true })}
              placeholder="Enter priority rank (1 = highest priority)"
              min="1"
              max="20"
              step="1"
            />
            {errors.rank && <p className="text-sm text-red-500">{errors.rank.message}</p>}
            <p className="text-xs text-muted-foreground">1 = highest priority, 20 = lowest priority</p>
          </div>

          <div className="space-y-2">
            <Label htmlFor="loadDescription">Load Description (Optional)</Label>
            <Textarea
              id="loadDescription"
              {...register("loadDescription")}
              placeholder="Describe the load details..."
              rows={3}
            />
          </div>

          <div className="flex justify-end space-x-2 pt-4">
            <Button type="button" variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button type="submit" disabled={isSubmitting}>
              {isSubmitting ? "Adding..." : "Add Load"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
