
import { supabase } from "@/integrations/supabase/client";

export interface FuelBunker {
  id: string;
  name: string;
  capacity: number;
  current_level: number;
}

export interface Team {
    id: string;
    name: string;
}

export interface BrickType {
    id: string;
    name: string;
    bricks_per_pallet: number;
}

export const getFuelBunkers = async (): Promise<FuelBunker[]> => {
    console.log("[fuelBunkersData] Fetching fuel bunkers...");
    const { data, error } = await supabase.from('fuel_bunkers').select('*');
    if (error) {
        console.error("[fuelBunkersData] Error fetching fuel bunkers:", error);
        throw new Error(error.message || "Unknown error fetching fuel bunkers");
    }
    if (!data) {
        console.error("[fuelBunkersData] No data fetched from fuel_bunkers.");
        return [];
    }
    console.log("[fuelBunkersData] Bunkers fetched:", data);
    return data as FuelBunker[];
}

export const getTeams = async (): Promise<Team[]> => {
    const { data, error } = await supabase.from('teams').select('*');
    if (error) {
        console.error("Error fetching teams", error);
        throw new Error(error.message);
    }
    return data as Team[];
}

export const getBrickTypes = async (): Promise<BrickType[]> => {
    const { data, error } = await supabase.from('management_brick_types').select('id, name, bricks_per_pallet');
    if (error) {
        console.error("Error fetching brick types", error);
        throw new Error(error.message);
    }
    return data as BrickType[];
}
