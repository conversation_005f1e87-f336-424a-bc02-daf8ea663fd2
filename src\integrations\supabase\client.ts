// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://hhxwnoreclckmtenugmt.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk5MTA2ODksImV4cCI6MjA2NTQ4NjY4OX0.Sx0m7H7YgSbOANJQ2YhlrZqsNms9J757ttQlQnOr-ek";
const SUPABASE_SERVICE_ROLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhoeHdub3JlY2xja210ZW51Z210Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0OTkxMDY4OSwiZXhwIjoyMDY1NDg2Njg5fQ.qfKkAhXg4fTVqX2gULF-aQw6EuPazZNI0EGsfYldsk0";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

// Regular client for general operations
export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);

// Admin client with service role key for admin operations (bypasses RLS)
export const supabaseAdmin = createClient<Database>(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY);