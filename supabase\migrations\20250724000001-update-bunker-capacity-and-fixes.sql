-- Migration to update bunker capacity and fix breakdown issues
-- This migration addresses the requested changes for fuel management and breakdowns

-- 1. Update second bunker capacity to 2000L
-- First, identify the second bunker (not the main bunker)
UPDATE fuel_bunkers 
SET capacity = 2000 
WHERE name != 'Main Bunker' 
  AND id = (
    SELECT id 
    FROM fuel_bunkers 
    WHERE name != 'Main Bunker' 
    ORDER BY name 
    LIMIT 1
  );

-- Alternative approach if bunker names are different
-- Update by position (second bunker in the list)
UPDATE fuel_bunkers 
SET capacity = 2000 
WHERE id = (
  SELECT id 
  FROM fuel_bunkers 
  WHERE id != (SELECT id FROM fuel_bunkers ORDER BY name LIMIT 1)
  ORDER BY name 
  LIMIT 1
);

-- 2. Fix breakdown table to allow NULL start_time for unresolved breakdowns
-- Make start_time nullable to fix the breakdown recording issue
ALTER TABLE public.breakdowns 
ALTER COLUMN start_time DROP NOT NULL;

-- Update existing records with empty start_time to NULL
UPDATE public.breakdowns 
SET start_time = NULL 
WHERE start_time = '00:00:00' OR start_time = '';

-- 3. Add comments to document the changes
COMMENT ON COLUMN public.fuel_bunkers.capacity IS 'Bunker capacity in liters. Second bunker updated to 2000L as per user request.';
COMMENT ON COLUMN public.breakdowns.start_time IS 'Time when equipment was restored. NULL indicates unresolved breakdown.';

-- 4. Create an index on fuel_dispensing_transactions for better performance with the new hours/km calculation
CREATE INDEX IF NOT EXISTS idx_fuel_dispensing_transactions_asset_date 
ON public.fuel_dispensing_transactions(asset_id, transaction_date DESC);

-- 5. Add a function to calculate previous hours/km for fuel consumption tracking
CREATE OR REPLACE FUNCTION get_previous_hours_km(asset_id_param TEXT)
RETURNS NUMERIC AS $$
DECLARE
    previous_reading NUMERIC;
    second_previous_reading NUMERIC;
BEGIN
    -- Get the second most recent reading
    SELECT starting_reading INTO previous_reading
    FROM fuel_dispensing_transactions
    WHERE asset_id = asset_id_param
      AND starting_reading IS NOT NULL
    ORDER BY transaction_date DESC, created_at DESC
    OFFSET 1 LIMIT 1;
    
    -- Get the third most recent reading
    SELECT starting_reading INTO second_previous_reading
    FROM fuel_dispensing_transactions
    WHERE asset_id = asset_id_param
      AND starting_reading IS NOT NULL
    ORDER BY transaction_date DESC, created_at DESC
    OFFSET 2 LIMIT 1;
    
    -- Calculate the difference
    IF previous_reading IS NOT NULL AND second_previous_reading IS NOT NULL THEN
        RETURN previous_reading - second_previous_reading;
    ELSIF previous_reading IS NOT NULL THEN
        RETURN previous_reading;
    ELSE
        RETURN 0;
    END IF;
END;
$$ LANGUAGE plpgsql;

-- 6. Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_previous_hours_km(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_previous_hours_km(TEXT) TO anon;

-- 7. Verify the changes
-- Show updated bunker capacities
SELECT name, capacity, current_level, 
       ROUND((current_level::NUMERIC / capacity::NUMERIC) * 100, 1) as percentage_full
FROM fuel_bunkers 
ORDER BY name;

-- Show breakdown table structure
SELECT column_name, is_nullable, data_type 
FROM information_schema.columns 
WHERE table_name = 'breakdowns' 
  AND table_schema = 'public'
  AND column_name IN ('start_time', 'stop_time', 'time')
ORDER BY column_name;
