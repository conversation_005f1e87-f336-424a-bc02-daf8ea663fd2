-- Migration to add team_id column to strapping_records table
-- This enables team tracking for strapping work records

-- Add team_id column to strapping_records table if it doesn't exist
ALTER TABLE public.strapping_records 
ADD COLUMN IF NOT EXISTS team_id TEXT REFERENCES public.teams(id);

-- Add comment to document the new column
COMMENT ON COLUMN public.strapping_records.team_id IS 'References the team assigned to this strapping work';

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_strapping_records_team_id ON public.strapping_records(team_id);

-- Verify the change
SELECT column_name, data_type, is_nullable 
FROM information_schema.columns 
WHERE table_name = 'strapping_records' 
  AND table_schema = 'public'
ORDER BY column_name;
