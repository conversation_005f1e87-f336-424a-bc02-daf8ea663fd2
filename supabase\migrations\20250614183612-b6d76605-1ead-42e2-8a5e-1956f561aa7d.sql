
-- Insert all kilns referenced by fires, making sure each exists
INSERT INTO kilns (id, name, status) VALUES
  ('kiln_a', 'Habla', 'operational'),
  ('kiln_b', 'Kiln 1', 'operational'),
  ('kiln_c', 'Kiln 2', 'operational'),
  ('kiln_d', 'Kiln 3', 'operational'),
  ('kiln_e', 'Kiln 4', 'operational'),
  ('kiln_f', 'Kiln 5', 'operational')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, status = EXCLUDED.status;
