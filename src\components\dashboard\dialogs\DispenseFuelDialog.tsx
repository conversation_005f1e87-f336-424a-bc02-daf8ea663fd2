
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, DialogHeader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Calendar } from "@/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { recordFuelDispensingTransaction } from "@/data/fuelDispensingTransactions";
import { useAssets } from "@/hooks/useAssets";
import { useEmployees } from "@/hooks/useEmployees";
import { FuelBunkerSelect } from "@/components/fuel/FuelBunkerSelect";
import { FuelBunkerRadioGroup } from "@/components/fuel/FuelBunkerRadioGroup";
import { useQueryClient } from "@tanstack/react-query";
import { calculateAutomaticFuelUsage } from "@/utils/fuelUsageCalculations";
import { supabase } from "@/integrations/supabase/client";
import { v4 as uuidv4 } from "uuid";
import { useActivityTracking } from "@/hooks/useActivityTracking";
import { useFuelBunkers } from "@/hooks/useFuelBunkers";

interface DispenseFuelDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const DispenseFuelDialog = ({
  isOpen,
  onClose
}: DispenseFuelDialogProps) => {
  const queryClient = useQueryClient();
  const [tab, setTab] = useState<"delivery" | "dispensing">("dispensing");
  const { logActivity } = useActivityTracking();
  // Form state
  const [bunkerId, setBunkerId] = useState("");
  const [assetId, setAssetId] = useState("");
  const [operatorId, setOperatorId] = useState("");
  const [currentReading, setCurrentReading] = useState("");
  const [litres, setLitres] = useState("");
  const [transDate, setTransDate] = useState<Date | undefined>(new Date());
  const [notes, setNotes] = useState("");
  const [loading, setLoading] = useState(false);

  // Automatic calculation state
  const [usageCalculation, setUsageCalculation] = useState<{
    lastReading: number | null;
    usage: { usage: number; unit: string } | null;
    readingType: "hourly" | "km";
    assetInfo: { name: string; type: string } | null;
  } | null>(null);
  const [calculationLoading, setCalculationLoading] = useState(false);

  // State for filtered operators
  const [operatorTeamMembers, setOperatorTeamMembers] = useState<any[]>([]);
  const [isLoadingOperators, setIsLoadingOperators] = useState(false);

  const { toast } = useToast();

  // Hooks for fetching data
  const { data: assets = [], isLoading: isLoadingAssets } = useAssets();
  const { data: allEmployees = [], isLoading: isLoadingEmps } = useEmployees();
  const { data: fuelBunkers = [], isLoading: isLoadingBunkers } = useFuelBunkers();

  // Get selected bunker data for validation
  const selectedBunker = fuelBunkers.find(b => b.id === bunkerId);
  const availableFuel = selectedBunker?.current_level || 0;
  const requestedLitres = Number(litres) || 0;
  const hasInsufficientFuel = requestedLitres > availableFuel;

  // Fetch operator team members
  useEffect(() => {
    const fetchOperatorTeamMembers = async () => {
      setIsLoadingOperators(true);
      // Initialize with empty array - only show Operator Team members
      setOperatorTeamMembers([]);
      
      try {
        // First find the "Operator Team" - try both capitalization variants
        console.log('Looking for Operator Team...');
        let teams;
        let teamsError;
        
        // Try with capital T
        const capitalResult = await supabase
          .from('teams')
          .select('id')
          .eq('name', 'Operator Team')
          .single();
          
        if (capitalResult.error) {
          // Try with lowercase t
          console.log('Trying with lowercase "team"...');
          const lowercaseResult = await supabase
            .from('teams')
            .select('id')
            .eq('name', 'Operator team')
            .single();
            
          teams = lowercaseResult.data;
          teamsError = lowercaseResult.error;
        } else {
          teams = capitalResult.data;
          teamsError = capitalResult.error;
        }

        // If Operator Team doesn't exist, create it
        if (teamsError && teamsError.code === 'PGRST116') { // PGRST116 is the error code for "no rows returned"
          console.log('Operator Team not found, creating it...');
          const teamId = uuidv4();
          const { data: newTeam, error: createError } = await supabase
            .from('teams')
            .insert({ id: teamId, name: 'Operator Team' })
            .select()
            .single();

          if (createError) {
            console.error('Error creating Operator Team:', createError);
          } else {
            console.log('Created Operator Team:', newTeam);
            // New team created, but no members yet
          }
        } else if (teamsError) {
          console.error('Error finding Operator Team:', teamsError);
        } else if (teams) {
          console.log('Found Operator Team:', teams);

          // Try a different query approach - first get just the team memberships
          console.log('Fetching team members for team ID:', teams.id);
          const { data: memberships, error: membershipsError } = await supabase
            .from('team_memberships')
            .select('employee_id')
            .eq('team_id', teams.id);

          if (membershipsError) {
            console.error('Error fetching team memberships:', membershipsError);
          } else {
            console.log('Team memberships:', memberships);

            if (memberships && memberships.length > 0) {
              console.log('Raw memberships data:', memberships);
              
              // Extract employee IDs from memberships
              const employeeIds = memberships
                .map((m: any) => m.employee_id)
                .filter(Boolean);
              
              console.log('Employee IDs:', employeeIds);
              
              if (employeeIds.length > 0) {
                // Fetch the actual employee records
                const { data: employees, error: employeesError } = await supabase
                  .from('employees')
                  .select('*')
                  .in('id', employeeIds);
                
                if (employeesError) {
                  console.error('Error fetching employees:', employeesError);
                } else if (employees && employees.length > 0) {
                  console.log('Fetched employees:', employees);
                  setOperatorTeamMembers(employees);
                } else {
                  console.log('No employees found for the given IDs');
                }
              }
            } else {
              console.log('No team members found in Operator Team');
            }
          }
        }
      } catch (error) {
        console.error('Error fetching operator team members:', error);
      } finally {
        // Always set loading to false at the end
        setIsLoadingOperators(false);
      }
    };

    if (isOpen) {
      fetchOperatorTeamMembers();
    } else {
      // Reset state when dialog closes
      setOperatorTeamMembers([]);
      setIsLoadingOperators(false);
    }
  }, [isOpen]);

  // Function to calculate usage automatically
  const handleCalculateUsage = async () => {
    if (!assetId || !currentReading || !litres) {
      setUsageCalculation(null);
      return;
    }

    setCalculationLoading(true);
    try {
      const result = await calculateAutomaticFuelUsage(
        assetId,
        Number(currentReading),
        Number(litres)
      );
      setUsageCalculation(result);
    } catch (error) {
      console.error('Error calculating usage:', error);
      setUsageCalculation(null);
    }
    setCalculationLoading(false);
  };

  // Effect to trigger calculation when relevant fields change
  useEffect(() => {
    if (assetId && currentReading && litres) {
      handleCalculateUsage();
    } else {
      setUsageCalculation(null);
    }
  }, [assetId, currentReading, litres]);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      if (!bunkerId || !assetId || !operatorId || !litres || !transDate || !currentReading) {
        toast({ title: "Error", description: "Please complete all required fields.", variant: "destructive" });
        setLoading(false);
        return;
      }

      // Check if there's enough fuel before dispensing
      if (hasInsufficientFuel) {
        toast({ 
          title: "Insufficient Fuel", 
          description: `Cannot dispense ${requestedLitres}L. Only ${availableFuel}L available in bunker.`, 
          variant: "destructive" 
        });
        setLoading(false);
        return;
      }

      await recordFuelDispensingTransaction({
        fuel_bunker_id: bunkerId,
        asset_id: assetId,
        operator_id: Number(operatorId),
        transaction_date: format(transDate, "yyyy-MM-dd"),
        starting_reading: Number(currentReading), // Store current reading as starting_reading
        ending_reading: null, // No longer used
        quantity_liters: Number(litres),
        notes: notes || null,
      });

      // Show success message with usage calculation if available
      let successMessage = "Fuel dispensing recorded!";
      if (usageCalculation?.usage) {
        successMessage += ` Usage: ${usageCalculation.usage.usage.toFixed(2)} ${usageCalculation.usage.unit}`;
      }

      toast({ title: "Success", description: successMessage });
      
      // Log the activity
      const selectedAsset = assets.find(a => a.id === assetId);
      const selectedOperator = operatorTeamMembers.find(o => o.id === Number(operatorId));
      
      logActivity(
        `Dispensed Fuel`,
        `${litres} liters to ${selectedAsset?.name || 'Asset'} operated by ${selectedOperator?.name || 'Operator'}`,
        'fuel'
      );
      
      queryClient.invalidateQueries({ queryKey: ["fuelBunkers"] });
      onClose();
    } catch (e: any) {
      toast({ title: "Error recording transaction", description: e.message || e.toString(), variant: "destructive" });
    }
    setLoading(false);
  };

  useEffect(() => {
    if (!isOpen) {
      setBunkerId("");
      setAssetId("");
      setOperatorId("");
      setCurrentReading("");
      setTransDate(new Date());
      setLitres("");
      setNotes("");
      setUsageCalculation(null);
    }
  }, [isOpen]);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <Tabs defaultValue="dispensing" value={tab} onValueChange={v => setTab(v as "dispensing" | "delivery")}>
              <TabsList className="grid grid-cols-2 w-80">
                <TabsTrigger value="delivery" disabled>Record Delivery</TabsTrigger>
                <TabsTrigger value="dispensing">Dispense Fuel</TabsTrigger>
              </TabsList>
            </Tabs>
            <Button
              type="button"
              variant="ghost"
              className="text-xs h-7 px-2"
              onClick={() => setTab(tab === "dispensing" ? "delivery" : "dispensing")}
              disabled={tab === "delivery"}
            >
              Switch to Delivery
            </Button>
          </div>
          <DialogTitle className="text-xl mt-2">Dispense Fuel</DialogTitle>
          <DialogDescription>Enter dispensing details below.</DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FuelBunkerRadioGroup value={bunkerId} onChange={setBunkerId} />
          <div>
            <Label htmlFor="asset">Asset</Label>
            <Select value={assetId} onValueChange={setAssetId} disabled={isLoadingAssets}>
              <SelectTrigger id="asset">
                <SelectValue placeholder={isLoadingAssets ? "Loading..." : "Select asset"} />
              </SelectTrigger>
              <SelectContent>
                {assets.map((a: any) => (
                  <SelectItem key={a.id} value={a.id}>{a.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div>
            <Label htmlFor="currentReading">
              Current Reading {usageCalculation?.readingType === "km" ? "(KM)" : "(Hours)"}
            </Label>
            <Input
              id="currentReading"
              type="number"
              value={currentReading}
              min="0"
              onChange={e => setCurrentReading(e.target.value)}
              placeholder={usageCalculation?.readingType === "km" ? "e.g. 12400" : "e.g. 1248.5"}
            />
          </div>
          <div>
            <Label htmlFor="litres">Litres Dispensed</Label>
            <Input
              id="litres"
              type="number"
              value={litres}
              min="0"
              max={availableFuel > 0 ? availableFuel : undefined}
              onChange={e => setLitres(e.target.value)}
              placeholder="e.g. 50"
              className={hasInsufficientFuel ? "border-red-500" : ""}
            />
            {selectedBunker && (
              <p className="text-xs text-slate-500 mt-1">
                Available: {availableFuel.toLocaleString()}L
              </p>
            )}
            {hasInsufficientFuel && (
              <p className="text-xs text-red-500 mt-1">
                Insufficient fuel! Only {availableFuel}L available.
              </p>
            )}
          </div>
        </div>

        {/* Usage Calculation Display */}
        {(usageCalculation || calculationLoading) && (
          <div className="mt-2 p-3 bg-slate-50 rounded-lg border">
            <h4 className="text-sm font-medium text-slate-700 mb-2">Automatic Usage Calculation</h4>
            {calculationLoading ? (
              <p className="text-sm text-slate-600">Calculating...</p>
            ) : usageCalculation ? (
              <div className="space-y-1 text-sm">
                {usageCalculation.lastReading !== null ? (
                  <>
                    <p className="text-slate-600">
                      Last Reading: <span className="font-medium">{usageCalculation.lastReading.toLocaleString()}</span>
                    </p>
                    <p className="text-slate-600">
                      Current Reading: <span className="font-medium">{Number(currentReading).toLocaleString()}</span>
                    </p>
                    {usageCalculation.usage ? (
                      <p className="text-green-700 font-medium">
                        Usage: {usageCalculation.usage.usage.toFixed(2)} {usageCalculation.usage.unit}
                      </p>
                    ) : (
                      <p className="text-amber-600">Unable to calculate usage (invalid readings)</p>
                    )}
                  </>
                ) : (
                  <p className="text-slate-600">No previous reading found for this asset</p>
                )}
              </div>
            ) : null}
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-2">
          <div>
            <Label htmlFor="operator">Operator</Label>
            <Select 
              value={operatorId} 
              onValueChange={setOperatorId} 
              disabled={isLoadingOperators || operatorTeamMembers.length === 0}
            >
              <SelectTrigger id="operator">
              <SelectValue 
                  placeholder={
                    isLoadingOperators 
                      ? "Loading operators..." 
                      : operatorTeamMembers.length === 0 
                        ? "No Operator Team members available" 
                        : "Select operator"
                  } 
                />
              </SelectTrigger>
              <SelectContent>
                {operatorTeamMembers.length === 0 ? (
                  <SelectItem value="none" disabled>No Operator Team members available</SelectItem>
                ) : (
                  operatorTeamMembers.map(o => (
                    <SelectItem key={o.id} value={String(o.id)}>{o.name}</SelectItem>
                  ))
                )}
              </SelectContent>
            </Select>
          </div>
          <div>
            <Label htmlFor="date">Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !transDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {transDate ? format(transDate, "yyyy/MM/dd") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
                <Calendar
                  mode="single"
                  selected={transDate}
                  onSelect={setTransDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>
        <div className="mt-2">
          <Label htmlFor="notes">Notes (optional)</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={e => setNotes(e.target.value)}
            placeholder="Add any additional notes..."
            rows={2}
            className="resize-none"
          />
        </div>
        <DialogFooter className="space-y-2 mt-2">
          <Button variant="outline" onClick={onClose} disabled={loading}>Cancel</Button>
          <Button 
            className="bg-blue-600 hover:bg-blue-700" 
            onClick={handleSubmit} 
            disabled={
              loading ||
              !bunkerId ||
              !assetId ||
              !operatorId ||
              !litres ||
              !currentReading ||
              !transDate ||
              hasInsufficientFuel ||
              availableFuel <= 0
            }
          >
            {loading ? "Saving..." : "Record Fuel Dispensing"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
