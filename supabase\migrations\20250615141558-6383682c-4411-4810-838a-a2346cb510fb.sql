
-- Create fuel_deliveries table to record fuel delivery transactions
CREATE TABLE public.fuel_deliveries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  fuel_bunker_id text NOT NULL REFERENCES public.fuel_bunkers(id),
  supplier text NOT NULL,
  invoice_number text NOT NULL,
  quantity numeric NOT NULL,
  cost_per_liter numeric NOT NULL,
  delivery_date date NOT NULL,
  created_at timestamptz NOT NULL DEFAULT now()
);

-- Enable RLS for fuel_deliveries
ALTER TABLE public.fuel_deliveries ENABLE ROW LEVEL SECURITY;

-- Allow all users (including unauthenticated) to read fuel deliveries
CREATE POLICY "Allow public read access to fuel_deliveries"
  ON public.fuel_deliveries
  FOR SELECT
  USING (true);

-- Allow all users (including unauthenticated) to add deliveries
CREATE POLICY "Allow public insert for fuel_deliveries"
  ON public.fuel_deliveries
  FOR INSERT
  WITH CHECK (true);
