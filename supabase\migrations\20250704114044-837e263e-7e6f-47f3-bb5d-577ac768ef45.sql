-- Create function to update timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create load_planning table
CREATE TABLE public.load_planning (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL,
  client_name TEXT NOT NULL,
  load_description TEXT,
  transporter TEXT NOT NULL,
  load_type TEXT NOT NULL CHECK (load_type IN ('Strapped', 'Pallets')),
  brick_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  user_id UUID NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.load_planning ENABLE ROW LEVEL SECURITY;

-- Create policies for load planning
CREATE POLICY "Users can view all load planning entries" 
ON public.load_planning 
FOR SELECT 
USING (true);

CREATE POLICY "Users can create load planning entries" 
ON public.load_planning 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Users can update load planning entries" 
ON public.load_planning 
FOR UPDATE 
USING (true);

CREATE POLICY "Users can delete load planning entries" 
ON public.load_planning 
FOR DELETE 
USING (true);

-- Create trigger for automatic timestamp updates
CREATE TRIGGER update_load_planning_updated_at
BEFORE UPDATE ON public.load_planning
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();