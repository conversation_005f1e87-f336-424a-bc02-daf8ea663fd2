
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Calendar, Package, Truck, Edit, CheckCircle, Star, RotateCcw, X } from "lucide-react";
import { EditLoadDialog } from "./EditLoadDialog";
import { RescheduleLoadDialog } from "./RescheduleLoadDialog";
import { useLoadPlanning } from "@/hooks/useLoadPlanning";
import { format } from "date-fns";
import { toast } from "sonner";
import { supabase } from "@/integrations/supabase/client";
import { useQueryClient } from "@tanstack/react-query";
import { LoadPlanningEntry } from "@/types/loadPlanning";

interface LoadsListProps {
  loads: LoadPlanningEntry[];
  isLoading: boolean;
}

export const LoadsList = ({ loads, isLoading }: LoadsListProps) => {
  const [editingLoad, setEditingLoad] = useState<LoadPlanningEntry | null>(null);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [reschedulingLoad, setReschedulingLoad] = useState<LoadPlanningEntry | null>(null);
  const [isRescheduleDialogOpen, setIsRescheduleDialogOpen] = useState(false);
  const [isCancelling, setIsCancelling] = useState<string | null>(null);
  const { markAsDispached, markAsReady, isMarkingDispatched, isMarkingReady } = useLoadPlanning();
  const queryClient = useQueryClient();

  const getStatusColor = (load: LoadPlanningEntry) => {
    if (load.dispatched) return "bg-green-100 text-green-800";
    if (load.ready) return "bg-blue-100 text-blue-800";
    return "bg-yellow-100 text-yellow-800";
  };

  const getStatusText = (load: LoadPlanningEntry) => {
    if (load.dispatched) return "Dispatched";
    if (load.ready) return "Ready";
    return "Pending";
  };

  const getPriorityColor = (rank?: number) => {
    if (!rank) return "secondary";
    if (rank <= 5) return "destructive";
    if (rank <= 10) return "default";
    return "secondary";
  };

  const handleEdit = (load: LoadPlanningEntry) => {
    setEditingLoad(load);
    setIsEditDialogOpen(true);
  };

  const handleReschedule = (load: LoadPlanningEntry) => {
    setReschedulingLoad(load);
    setIsRescheduleDialogOpen(true);
  };

  const handleCancel = async (load: LoadPlanningEntry) => {
    if (!window.confirm(`Are you sure you want to cancel the load for ${load.client_name}? This action cannot be undone.`)) {
      return;
    }

    setIsCancelling(load.id);

    try {
      const { error } = await supabase
        .from('load_planning')
        .delete()
        .eq('id', load.id);

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      toast.success("Load cancelled successfully!");

      // Refresh the loads list using query invalidation
      queryClient.invalidateQueries({ queryKey: ['load-planning'] });

    } catch (error) {
      console.error("Error cancelling load:", error);
      toast.error("Failed to cancel load");
    } finally {
      setIsCancelling(null);
    }
  };

  const handleStatusChange = (loadId: string, status: string) => {
    if (status === "ready") {
      markAsReady(loadId);
    } else if (status === "dispatched") {
      markAsDispached(loadId);
    }
  };

  if (isLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {[...Array(6)].map((_, i) => (
          <Card key={i} className="animate-pulse">
            <CardHeader className="space-y-2">
              <div className="h-4 bg-gray-200 rounded w-3/4"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="h-3 bg-gray-200 rounded"></div>
              <div className="h-3 bg-gray-200 rounded w-5/6"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {loads.map((load) => (
          <Card key={load.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex justify-between items-start">
                <CardTitle className="text-lg font-semibold text-slate-800">
                  {load.client_name}
                </CardTitle>
                <div className="flex items-center gap-2">
                  {load.rank && (
                    <Badge variant={getPriorityColor(load.rank)} className="text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      {load.rank}
                    </Badge>
                  )}
                  <Badge className={getStatusColor(load)}>
                    {getStatusText(load)}
                  </Badge>
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center text-sm text-slate-600">
                <Calendar className="h-4 w-4 mr-2 text-slate-400" />
                <span>{format(new Date(load.date), 'PPP')}</span>
              </div>

              <div className="flex items-center text-sm text-slate-600">
                <Truck className="h-4 w-4 mr-2 text-slate-400" />
                <span>{load.transporter}</span>
              </div>

              <div className="flex items-center text-sm text-slate-600">
                <Package className="h-4 w-4 mr-2 text-slate-400" />
                <span>{load.brick_count.toLocaleString()} bricks ({load.load_type})</span>
              </div>

              {load.management_brick_types && (
                <div className="text-sm text-slate-600">
                  <strong>Type:</strong> {load.management_brick_types.name} ({load.management_brick_types.category})
                </div>
              )}

              {load.load_description && (
                <div className="text-sm text-slate-500 bg-slate-50 p-2 rounded">
                  {load.load_description}
                </div>
              )}

              <div className="space-y-2 pt-2">
                {/* Primary Actions Row */}
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(load)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  {!load.ready && !load.dispatched && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleStatusChange(load.id, "ready")}
                      disabled={isMarkingReady}
                      className="flex-1"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Ready
                    </Button>
                  )}
                  {load.ready && !load.dispatched && (
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleStatusChange(load.id, "dispatched")}
                      disabled={isMarkingDispatched}
                      className="flex-1"
                    >
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Dispatch
                    </Button>
                  )}
                </div>

                {/* Secondary Actions Row - Only show for non-dispatched loads */}
                {!load.dispatched && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReschedule(load)}
                      className="flex-1"
                    >
                      <RotateCcw className="h-3 w-3 mr-1" />
                      Reschedule
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleCancel(load)}
                      disabled={isCancelling === load.id}
                      className="flex-1 text-red-600 hover:text-red-700 hover:border-red-300"
                    >
                      <X className="h-3 w-3 mr-1" />
                      {isCancelling === load.id ? "Cancelling..." : "Cancel"}
                    </Button>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {loads.length === 0 && !isLoading && (
        <div className="text-center py-8 text-slate-500">
          <Package className="h-12 w-12 mx-auto mb-4 text-slate-300" />
          <p>No loads scheduled yet</p>
          <p className="text-sm">Click "Add Load" to schedule your first delivery</p>
        </div>
      )}

      {editingLoad && (
        <EditLoadDialog
          load={editingLoad}
          open={isEditDialogOpen}
          onOpenChange={setIsEditDialogOpen}
        />
      )}

      {reschedulingLoad && (
        <RescheduleLoadDialog
          load={reschedulingLoad}
          open={isRescheduleDialogOpen}
          onOpenChange={setIsRescheduleDialogOpen}
        />
      )}
    </>
  );
};
