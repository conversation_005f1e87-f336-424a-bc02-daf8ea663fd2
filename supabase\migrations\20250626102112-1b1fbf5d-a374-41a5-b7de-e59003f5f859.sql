
-- Ensure fire_id and chamber_number columns exist in dehacking_entries table
ALTER TABLE public.dehacking_entries 
ADD COLUMN IF NOT EXISTS fire_id text,
ADD COLUMN IF NOT EXISTS chamber_number integer;

-- Add foreign key constraint for fire_id if it doesn't exist
DO $$ 
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM information_schema.table_constraints 
        WHERE constraint_name = 'dehacking_entries_fire_id_fkey' 
        AND table_name = 'dehacking_entries'
    ) THEN
        ALTER TABLE public.dehacking_entries 
        ADD CONSTRAINT dehacking_entries_fire_id_fkey 
        FOREIGN KEY (fire_id) REFERENCES public.fires(id);
    END IF;
END $$;

-- Add comments to explain the columns
COMMENT ON COLUMN public.dehacking_entries.fire_id IS 'Reference to the fire associated with this dehacking entry';
COMMENT ON COLUMN public.dehacking_entries.chamber_number IS 'The chamber number (1-12) associated with this dehacking entry';
