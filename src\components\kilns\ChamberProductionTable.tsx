import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Loader2, Save, Calculator } from "lucide-react";
import { format } from "date-fns";
import { toast } from "sonner";
import {
  useChamberProductionData,
  useProductionDataFromEntries,
  useUpsertChamberProductionData,
} from "@/hooks/useChamberProduction";

const KILN_OPTIONS = [
  { id: "habla-kiln", name: "Habla", chambers: 10 },
  { id: "kiln-1", name: "Kiln 1", chambers: 24 },
  { id: "kiln-2", name: "Kiln 2", chambers: 24 },
  { id: "kiln-3", name: "Kiln 3", chambers: 24 },
  { id: "kiln-4", name: "Kiln 4", chambers: 24 },
  { id: "kiln-5", name: "Kiln 5", chambers: 24 },
];

interface ChamberProductionTableProps {
  selectedDate: string;
}

interface ChamberData {
  chamber_number: number;
  bricks_set: number;
  bricks_dehacked: number;
  output: number;
  breakage: number;
  notes?: string;
}

export const ChamberProductionTable: React.FC<ChamberProductionTableProps> = ({
  selectedDate,
}) => {
  const [editingData, setEditingData] = useState<Record<string, ChamberData>>({});
  const [hasChanges, setHasChanges] = useState(false);

  const { data: productionData = [], isLoading: isLoadingProduction } = useChamberProductionData(selectedDate);
  const { data: entriesData, isLoading: isLoadingEntries } = useProductionDataFromEntries(selectedDate);
  const upsertMutation = useUpsertChamberProductionData();

  // Initialize editing data when production data changes
  useEffect(() => {
    const initialData: Record<string, ChamberData> = {};
    
    KILN_OPTIONS.forEach(kiln => {
      for (let chamber = 1; chamber <= kiln.chambers; chamber++) {
        const key = `${kiln.id}-${chamber}`;
        const existingData = productionData.find(
          p => p.kiln_id === kiln.id && p.chamber_number === chamber
        );
        
        if (existingData) {
          initialData[key] = {
            chamber_number: chamber,
            bricks_set: existingData.bricks_set,
            bricks_dehacked: existingData.bricks_dehacked,
            output: existingData.output,
            breakage: existingData.breakage,
            notes: existingData.notes,
          };
        } else {
          // Calculate from existing entries if available
          let bricksSet = 0;
          let bricksDehacked = 0;

          if (entriesData) {
            // Calculate bricks set from setting entries
            const settingForChamber = entriesData.settingEntries.filter(
              (entry: any) => entry.fires?.kiln_id === kiln.id && entry.chamber_number === chamber
            );
            bricksSet = settingForChamber.reduce((sum: number, entry: any) => {
              const bricksPerPallet = entry.management_brick_types?.bricks_per_pallet || 0;
              return sum + (entry.pallet_count * bricksPerPallet);
            }, 0);

            // Calculate bricks dehacked from dehacking entries
            const dehackingForChamber = entriesData.dehackingEntries.filter(
              (entry: any) => entry.fires?.kiln_id === kiln.id && entry.chamber_number === chamber
            );
            bricksDehacked = dehackingForChamber.reduce((sum: number, entry: any) => {
              const bricksPerPallet = entry.management_brick_types?.bricks_per_pallet || 0;
              return sum + (entry.pallet_count * bricksPerPallet);
            }, 0);
          }

          initialData[key] = {
            chamber_number: chamber,
            bricks_set: bricksSet,
            bricks_dehacked: bricksDehacked,
            output: bricksDehacked,
            breakage: bricksSet - bricksDehacked,
          };
        }
      }
    });

    setEditingData(initialData);
    setHasChanges(false);
  }, [productionData, entriesData]);

  const handleInputChange = (kilnId: string, chamber: number, field: 'bricks_set' | 'bricks_dehacked' | 'notes', value: string | number) => {
    const key = `${kilnId}-${chamber}`;
    setEditingData(prev => {
      const updated = { ...prev };
      if (!updated[key]) {
        updated[key] = {
          chamber_number: chamber,
          bricks_set: 0,
          bricks_dehacked: 0,
          output: 0,
          breakage: 0,
        };
      }

      if (field === 'bricks_set' || field === 'bricks_dehacked') {
        const numValue = typeof value === 'string' ? parseInt(value) || 0 : value;
        updated[key] = {
          ...updated[key],
          [field]: numValue,
        };

        // Recalculate output and breakage
        updated[key].output = updated[key].bricks_dehacked;
        updated[key].breakage = updated[key].bricks_set - updated[key].bricks_dehacked;
      } else {
        updated[key] = {
          ...updated[key],
          [field]: value,
        };
      }

      return updated;
    });
    setHasChanges(true);
  };

  const handleSaveAll = async () => {
    try {
      const promises = Object.entries(editingData).map(([key, data]) => {
        const [kilnId, chamberStr] = key.split('-');
        const chamber = parseInt(chamberStr);
        
        return upsertMutation.mutateAsync({
          date: selectedDate,
          kiln_id: kilnId,
          chamber_number: chamber,
          bricks_set: data.bricks_set,
          bricks_dehacked: data.bricks_dehacked,
          notes: data.notes,
        });
      });

      await Promise.all(promises);
      setHasChanges(false);
      toast.success('All chamber production data saved successfully');
    } catch (error) {
      console.error('Error saving chamber production data:', error);
      toast.error('Failed to save chamber production data');
    }
  };

  const calculateTotals = () => {
    return Object.values(editingData).reduce(
      (totals, chamber) => ({
        totalSet: totals.totalSet + chamber.bricks_set,
        totalDehacked: totals.totalDehacked + chamber.bricks_dehacked,
        totalOutput: totals.totalOutput + chamber.output,
        totalBreakage: totals.totalBreakage + chamber.breakage,
      }),
      { totalSet: 0, totalDehacked: 0, totalOutput: 0, totalBreakage: 0 }
    );
  };

  const totals = calculateTotals();
  const breakagePercentage = totals.totalSet > 0 ? (totals.totalBreakage / totals.totalSet) * 100 : 0;

  if (isLoadingProduction || isLoadingEntries) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Chamber Production Tracking</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-8 w-8 animate-spin" />
            <span className="ml-2">Loading chamber production data...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle>Chamber Production Tracking</CardTitle>
          <div className="flex items-center gap-2">
            {hasChanges && (
              <Button
                onClick={handleSaveAll}
                disabled={upsertMutation.isPending}
                size="sm"
              >
                {upsertMutation.isPending ? (
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                Save All Changes
              </Button>
            )}
          </div>
        </div>
        <div className="text-sm text-slate-600">
          Date: {format(new Date(selectedDate), 'PPP')}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {KILN_OPTIONS.map((kiln) => (
            <div key={kiln.id}>
              <h3 className="font-semibold text-lg mb-3">{kiln.name}</h3>
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Chamber</TableHead>
                      <TableHead className="text-center">Bricks Set</TableHead>
                      <TableHead className="text-center">Bricks Dehacked</TableHead>
                      <TableHead className="text-center">Output</TableHead>
                      <TableHead className="text-center">Breakage</TableHead>
                      <TableHead className="text-center">Breakage %</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {Array.from({ length: kiln.chambers }, (_, i) => i + 1).map((chamber) => {
                      const key = `${kiln.id}-${chamber}`;
                      const data = editingData[key] || {
                        chamber_number: chamber,
                        bricks_set: 0,
                        bricks_dehacked: 0,
                        output: 0,
                        breakage: 0,
                      };
                      const chamberBreakagePercentage = data.bricks_set > 0 ? (data.breakage / data.bricks_set) * 100 : 0;

                      return (
                        <TableRow key={chamber}>
                          <TableCell className="font-medium">Chamber {chamber}</TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={data.bricks_set}
                              onChange={(e) => handleInputChange(kiln.id, chamber, 'bricks_set', e.target.value)}
                              className="w-20 text-center"
                              min="0"
                            />
                          </TableCell>
                          <TableCell>
                            <Input
                              type="number"
                              value={data.bricks_dehacked}
                              onChange={(e) => handleInputChange(kiln.id, chamber, 'bricks_dehacked', e.target.value)}
                              className="w-20 text-center"
                              min="0"
                            />
                          </TableCell>
                          <TableCell className="text-center font-medium">
                            {data.output.toLocaleString()}
                          </TableCell>
                          <TableCell className="text-center">
                            <span className={data.breakage > 0 ? 'text-red-600 font-medium' : 'text-green-600'}>
                              {data.breakage.toLocaleString()}
                            </span>
                          </TableCell>
                          <TableCell className="text-center">
                            <span className={chamberBreakagePercentage > 5 ? 'text-red-600 font-medium' : 'text-slate-600'}>
                              {chamberBreakagePercentage.toFixed(1)}%
                            </span>
                          </TableCell>
                        </TableRow>
                      );
                    })}
                  </TableBody>
                </Table>
              </div>
            </div>
          ))}

          {/* Summary Section */}
          <div className="border-t pt-4">
            <h3 className="font-semibold text-lg mb-3 flex items-center">
              <Calculator className="h-5 w-5 mr-2" />
              Daily Summary
            </h3>
            <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">
                  {totals.totalSet.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Bricks Set</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">
                  {totals.totalDehacked.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Bricks Dehacked</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-slate-800">
                  {totals.totalOutput.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Output</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${totals.totalBreakage > 0 ? 'text-red-600' : 'text-green-600'}`}>
                  {totals.totalBreakage.toLocaleString()}
                </div>
                <div className="text-sm text-slate-600">Total Breakage</div>
              </div>
              <div className="text-center">
                <div className={`text-2xl font-bold ${breakagePercentage > 5 ? 'text-red-600' : 'text-green-600'}`}>
                  {breakagePercentage.toFixed(1)}%
                </div>
                <div className="text-sm text-slate-600">Breakage Rate</div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
