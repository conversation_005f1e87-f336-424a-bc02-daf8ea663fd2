
-- Re-create the function to update fuel bunker level after a delivery
CREATE OR REPLACE FUNCTION public.handle_fuel_delivery()
RETURNS TRIGGER AS $$
BEGIN
  -- Add the delivered quantity to the bunker's current level
  UPDATE public.fuel_bunkers
  SET current_level = current_level + NEW.quantity
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the old trigger if it exists and create a new one
DROP TRIGGER IF EXISTS on_fuel_delivery_insert ON public.fuel_deliveries;
CREATE TRIGGER on_fuel_delivery_insert
  AFTER INSERT ON public.fuel_deliveries
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_fuel_delivery();

-- Re-create the function to update fuel bunker level after dispensing
CREATE OR REPLACE FUNCTION public.handle_fuel_dispensing()
RETURNS TRIGGER AS $$
BEGIN
  -- Subtract the dispensed quantity from the bunker's current level
  UPDATE public.fuel_bunkers
  SET current_level = current_level - NEW.quantity_liters
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Drop the old trigger if it exists and create a new one
DROP TRIGGER IF EXISTS on_fuel_dispensing_insert ON public.fuel_dispensing_transactions;
CREATE TRIGGER on_fuel_dispensing_insert
  AFTER INSERT ON public.fuel_dispensing_transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_fuel_dispensing();

-- Corrective action for historical data for Main Bunker ('bunker1')
-- This recalculates the current level based on all past deliveries and dispensing events.
UPDATE public.fuel_bunkers
SET current_level = 
  (SELECT COALESCE(SUM(quantity), 0) FROM public.fuel_deliveries WHERE fuel_bunker_id = 'bunker1') - 
  (SELECT COALESCE(SUM(quantity_liters), 0) FROM public.fuel_dispensing_transactions WHERE fuel_bunker_id = 'bunker1')
WHERE id = 'bunker1';

-- Corrective action for historical data for 2nd Bunker ('bunker2')
UPDATE public.fuel_bunkers
SET current_level = 
  (SELECT COALESCE(SUM(quantity), 0) FROM public.fuel_deliveries WHERE fuel_bunker_id = 'bunker2') - 
  (SELECT COALESCE(SUM(quantity_liters), 0) FROM public.fuel_dispensing_transactions WHERE fuel_bunker_id = 'bunker2')
WHERE id = 'bunker2';
