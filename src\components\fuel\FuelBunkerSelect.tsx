
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { useFuelBunkers } from "@/hooks/useFuelBunkers";

interface FuelBunkerSelectProps {
  value: string;
  onChange: (val: string) => void;
}

export function FuelBunkerSelect({ value, onChange }: FuelBunkerSelectProps) {
  const { data: bunkers = [], isLoading, error } = useFuelBunkers();

  if (error) {
    return (
      <div className="mb-2">
        <Label htmlFor="bunker">Fuel Bunker</Label>
        <div className="text-red-600 text-sm py-1">
          Error fetching fuel bunkers: {error.message || String(error)}
        </div>
      </div>
    );
  }

  return (
    <div>
      <Label htmlFor="bunker">Fuel Bunker</Label>
      <Select value={value} onValueChange={onChange} disabled={isLoading}>
        <SelectTrigger id="bunker">
          <SelectValue placeholder={isLoading ? "Loading..." : "Select bunker"} />
        </SelectTrigger>
        <SelectContent>
          {Array.isArray(bunkers) && bunkers.length > 0 ? (
            bunkers.map((b: any) =>
              b.id && b.name ? (
                <SelectItem key={b.id} value={b.id}>
                  {b.name}
                </SelectItem>
              ) : null
            )
          ) : (
            <div className="px-4 py-2 text-muted-foreground text-sm">
              {isLoading
                ? "Loading..."
                : "No fuel bunkers found. Please add one in Fuel Management."}
            </div>
          )}
        </SelectContent>
      </Select>
    </div>
  );
}
