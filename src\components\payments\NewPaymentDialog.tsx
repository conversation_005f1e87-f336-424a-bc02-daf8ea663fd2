
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, Plus } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAddPayment } from "@/hooks/usePayments";
import { Employee } from "@/data/employeeData";
import { NewPayment } from "@/data/paymentsData";

interface NewPaymentDialogProps {
  employees: Employee[];
}

export const NewPaymentDialog = ({ employees }: NewPaymentDialogProps) => {
  const { toast } = useToast();
  const addPaymentMutation = useAddPayment();

  const [isNewPaymentD<PERSON>ogO<PERSON>, setIsNewPaymentDialogOpen] = useState(false);
  const [newPaymentEmployeeId, setNewPaymentEmployeeId] = useState<string | undefined>();
  const [newPaymentAmount, setNewPaymentAmount] = useState("");
  const [newPaymentType, setNewPaymentType] = useState<NewPayment['payment_type'] | undefined>();
  const [newPaymentDate, setNewPaymentDate] = useState(new Date().toISOString().slice(0, 10));
  const [newPaymentNotes, setNewPaymentNotes] = useState("");

  const handleProcessPayment = () => {
    if (!newPaymentEmployeeId || !newPaymentAmount || !newPaymentType || !newPaymentDate) {
      toast({ title: "Error", description: "Please fill all required fields.", variant: "destructive" });
      return;
    }
    
    addPaymentMutation.mutate({
      employee_id: parseInt(newPaymentEmployeeId, 10),
      amount: parseFloat(newPaymentAmount),
      payment_type: newPaymentType,
      payment_date: newPaymentDate,
      status: 'Paid',
      notes: newPaymentNotes,
    }, {
      onSuccess: () => {
        setIsNewPaymentDialogOpen(false);
        setNewPaymentEmployeeId(undefined);
        setNewPaymentAmount("");
        setNewPaymentType(undefined);
        setNewPaymentDate(new Date().toISOString().slice(0, 10));
        setNewPaymentNotes("");
      }
    });
  };

  return (
    <Dialog open={isNewPaymentDialogOpen} onOpenChange={setIsNewPaymentDialogOpen}>
      <DialogTrigger asChild>
        <Button className="bg-slate-800 hover:bg-slate-700">
          <Plus size={20} className="mr-2" />
          Process Payment
        </Button>
      </DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Process New Payment</DialogTitle>
        </DialogHeader>
        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Employee</label>
            <Select value={newPaymentEmployeeId} onValueChange={setNewPaymentEmployeeId}>
              <SelectTrigger>
                <SelectValue placeholder="Select employee" />
              </SelectTrigger>
              <SelectContent>
                {employees.map(emp => (
                  <SelectItem key={emp.id} value={String(emp.id)}>{emp.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium">Amount (R)</label>
            <Input type="number" placeholder="Enter amount" value={newPaymentAmount} onChange={e => setNewPaymentAmount(e.target.value)} />
          </div>
          <div>
            <label className="text-sm font-medium">Payment Type</label>
            <Select value={newPaymentType} onValueChange={(value) => setNewPaymentType(value as NewPayment['payment_type'])}>
              <SelectTrigger>
                <SelectValue placeholder="Select type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Salary">Salary</SelectItem>
                <SelectItem value="Overtime">Overtime</SelectItem>
                <SelectItem value="Bonus">Bonus</SelectItem>
                <SelectItem value="Dehacking">Dehacking</SelectItem>
                <SelectItem value="Setting">Setting</SelectItem>
                <SelectItem value="Adjustment">Adjustment</SelectItem>
              </SelectContent>
            </Select>
          </div>
           <div>
            <label className="text-sm font-medium">Payment Date</label>
            <Input type="date" value={newPaymentDate} onChange={e => setNewPaymentDate(e.target.value)} />
          </div>
          <div>
            <label className="text-sm font-medium">Notes</label>
            <Textarea placeholder="Optional notes" value={newPaymentNotes} onChange={e => setNewPaymentNotes(e.target.value)} />
          </div>
          <Button onClick={handleProcessPayment} className="w-full" disabled={addPaymentMutation.isPending}>
            {addPaymentMutation.isPending && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Process Payment
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
