
-- Migration script to populate database with initial mock data.

-- Populate management_brick_types from src/data/quickAccessData.ts
INSERT INTO public.management_brick_types (id, name, bricks_per_pallet, category, grade, setting_rate, dehacking_rate, overtime_rate, status) VALUES
('imperial', 'Imperial', 320, 'Standard', 'A', 1.0, 1.0, 1.5, 'active'),
('maxi', 'Maxi', 280, 'Standard', 'A', 1.0, 1.0, 1.5, 'active')
ON CONFLICT (id) DO NOTHING;

-- Populate teams from src/data/quickAccessData.ts
INSERT INTO public.teams (id, name) VALUES
('team1', 'Team Alpha'),
('team2', 'Team Beta'),
('team3', 'Team Gamma')
ON CONFLICT (id) DO NOTHING;

-- Populate employees with some mock data
INSERT INTO public.employees (name)
SELECT * FROM (VALUES ('<PERSON>'), ('<PERSON>'), ('<PERSON>')) AS E(name)
WHERE NOT EXISTS (SELECT 1 FROM public.employees WHERE public.employees.name = E.name);

-- Populate kilns with some mock data
INSERT INTO public.kilns (id, name, status) VALUES
('kiln_a', 'Kiln A', 'operational'),
('kiln_b', 'Kiln B', 'operational')
ON CONFLICT (id) DO NOTHING;

-- Populate fires with some mock data
INSERT INTO public.fires (id, name, kiln_id, status) VALUES
('fire_1', 'Fire 1', 'kiln_a', 'active'),
('fire_2', 'Fire 2', 'kiln_a', 'active'),
('fire_3', 'Fire 3', 'kiln_b', 'inactive')
ON CONFLICT (id) DO NOTHING;

-- Create fuel_bunkers table for data from src/data/fuelBunkersData.ts
CREATE TABLE IF NOT EXISTS public.fuel_bunkers (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    capacity NUMERIC NOT NULL,
    current_level NUMERIC NOT NULL
);

-- Enable RLS for fuel_bunkers
ALTER TABLE public.fuel_bunkers ENABLE ROW LEVEL SECURITY;

-- Add RLS policies for fuel_bunkers (allow authenticated read/write for now)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1
        FROM pg_policy
        WHERE polname = 'Allow authenticated users full access on fuel_bunkers'
        AND polrelid = 'public.fuel_bunkers'::regclass
    ) THEN
        CREATE POLICY "Allow authenticated users full access on fuel_bunkers" ON public.fuel_bunkers FOR ALL
        USING (auth.role() = 'authenticated')
        WITH CHECK (auth.role() = 'authenticated');
    END IF;
END;
$$;

-- Populate fuel_bunkers from src/data/fuelBunkersData.ts
INSERT INTO public.fuel_bunkers (id, name, capacity, current_level) VALUES
('bunker1', 'Main Bunker', 6000, 4500),
('bunker2', '2nd Bunker', 1000, 300)
ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  capacity = EXCLUDED.capacity,
  current_level = EXCLUDED.current_level;
