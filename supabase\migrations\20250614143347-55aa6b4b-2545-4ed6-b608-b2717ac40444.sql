
-- Create ENUM types for statuses to ensure data consistency
CREATE TYPE public.kiln_status AS ENUM ('operational', 'offline', 'maintenance');
CREATE TYPE public.fire_status AS ENUM ('active', 'inactive', 'maintenance');

-- Create kilns table to store kiln configurations
CREATE TABLE public.kilns (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    status public.kiln_status NOT NULL
);

-- <PERSON><PERSON> fires table, linked to the kilns table
CREATE TABLE public.fires (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    kiln_id TEXT NOT NULL REFERENCES public.kilns(id) ON DELETE CASCADE,
    status public.fire_status NOT NULL
);

-- Create employees table for employee information
CREATE TABLE public.employees (
    id SERIAL PRIMARY KEY,
    name TEXT NOT NULL
);

-- Create teams table for team configurations
CREATE TABLE public.teams (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL
);

-- Create management_brick_types table for brick specifications and rates
CREATE TABLE public.management_brick_types (
    id TEXT PRIMARY KEY,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    grade TEXT NOT NULL,
    setting_rate NUMERIC NOT NULL,
    dehacking_rate NUMERIC NOT NULL,
    overtime_rate NUMERIC NOT NULL,
    status TEXT NOT NULL,
    bricks_per_pallet INTEGER NOT NULL
);

-- Enable Row Level Security for all new tables
ALTER TABLE public.kilns ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.fires ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employees ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.teams ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.management_brick_types ENABLE ROW LEVEL SECURITY;

-- Create policies to allow authenticated users to read and write data.
-- We will refine these policies later when we implement a role-based access system.
CREATE POLICY "Allow authenticated users full access on kilns" ON public.kilns FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated users full access on fires" ON public.fires FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated users full access on employees" ON public.employees FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated users full access on teams" ON public.teams FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');
CREATE POLICY "Allow authenticated users full access on management_brick_types" ON public.management_brick_types FOR ALL USING (auth.role() = 'authenticated') WITH CHECK (auth.role() = 'authenticated');
