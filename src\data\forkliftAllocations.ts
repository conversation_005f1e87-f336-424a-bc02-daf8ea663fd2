
import { supabase } from "@/integrations/supabase/client";

export interface ForkliftAllocation {
  id: number;
  forklift_driver_id: number;
  allocation_type: "setting" | "dehacking";
  allocated_teams: string[]; // For 'setting', team IDs. For 'dehacking', employee IDs.
  start_date: string;
  end_date?: string | null;
  is_active: boolean;
}

export const getForkliftAllocations = async (): Promise<ForkliftAllocation[]> => {
  const { data, error } = await supabase
    .from("forklift_allocations")
    .select("*")
    .eq("is_active", true);

  if (error) throw new Error(error.message);
  return data as ForkliftAllocation[];
};
