
import { useFuelBunkers } from "@/hooks/useFuelBunkers";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";

interface FuelBunkerRadioGroupProps {
  value: string;
  onChange: (val: string) => void;
}

export function FuelBunkerRadioGroup({ value, onChange }: FuelBunkerRadioGroupProps) {
  const { data: bunkers = [], isLoading, error } = useFuelBunkers();

  if (isLoading) {
    return (
      <div>
        <Label>Fuel Bunker</Label>
        <div className="text-muted-foreground text-sm">Loading...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Label>Fuel Bunker</Label>
        <div className="text-red-600 text-sm py-1">
          Error fetching fuel bunkers: {error.message || String(error)}
        </div>
      </div>
    );
  }

  if (!Array.isArray(bunkers) || bunkers.length === 0) {
    return (
      <div>
        <Label>Fuel Bunker</Label>
        <div className="text-muted-foreground text-sm">No fuel bunkers found. Please add one in Fuel Management.</div>
      </div>
    );
  }

  return (
    <div>
      <Label>Fuel Bunker</Label>
      <RadioGroup value={value} onValueChange={onChange} className="flex flex-col gap-1 mt-1">
        {bunkers.map((bunker: any) =>
          bunker.id && bunker.name ? (
            <Label className="flex items-center space-x-2 cursor-pointer" key={bunker.id}>
              <RadioGroupItem
                value={bunker.id}
                id={`bunker-radio-${bunker.id}`}
                className="mr-2"
              />
              <span>{bunker.name}</span>
            </Label>
          ) : null
        )}
      </RadioGroup>
    </div>
  );
}
