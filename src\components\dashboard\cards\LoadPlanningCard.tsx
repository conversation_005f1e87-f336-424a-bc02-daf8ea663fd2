import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Truck, Calendar, TrendingUp, Package } from "lucide-react";
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { useLoadPlanning } from "@/hooks/useLoadPlanning";

// Define the product types we want to track (same as forecast table)
const PRODUCT_TYPES = [{
  key: 'imp_nfx',
  label: 'IMP NFX',
  category: 'Imperial'
}, {
  key: 'imp_nfp',
  label: 'IMP NFP',
  category: 'Imperial'
}, {
  key: 'imp_2nd_grade',
  label: 'IMP 2nd Grade',
  category: 'Imperial'
}, {
  key: 'maxi_nfx',
  label: 'MAXI NFX',
  category: 'Maxi'
}, {
  key: 'maxi_nfp',
  label: 'MAXI NFP',
  category: 'Maxi'
}, {
  key: 'maxi_2nd_grade',
  label: 'MAXI 2nd Grade',
  category: 'Maxi'
}];
export const LoadPlanningCard = () => {
  // Get all loads without date filtering
  const {
    loads,
    isLoading
  } = useLoadPlanning();
  const today = new Date();

  // Helper function to normalize brick type names for matching
  const normalizeBrickTypeName = (name: string): string => {
    return name.toLowerCase().replace(/\s+/g, '_').replace('imperial', 'imp').replace('maxi', 'maxi').replace('2nd_grade', '2nd_grade').replace('nfp', 'nfp').replace('nfx', 'nfx');
  };

  // Helper function to get loads for a specific period
  const getLoadsForPeriod = (startDate: Date, endDate: Date) => {
    return loads.filter(load => {
      const loadDate = new Date(load.date);
      return loadDate >= startDate && loadDate <= endDate;
    });
  };

  // Helper function to calculate brick requirements by product type
  const calculateBrickRequirements = (periodLoads: any[]) => {
    const requirements: Record<string, number> = {};

    // Initialize all product types to 0
    PRODUCT_TYPES.forEach(product => {
      requirements[product.key] = 0;
    });
    periodLoads.forEach(load => {
      if (load.management_brick_types?.name && load.brick_count) {
        const normalizedName = normalizeBrickTypeName(load.management_brick_types.name);

        // Find matching product type
        const matchingProduct = PRODUCT_TYPES.find(product => {
          const productKey = product.key.toLowerCase();
          return normalizedName.includes(productKey.replace('_', '')) || normalizedName.includes(productKey) || productKey.includes('nfx') && normalizedName.includes('nfx') || productKey.includes('nfp') && normalizedName.includes('nfp') || productKey.includes('2nd_grade') && (normalizedName.includes('2nd') || normalizedName.includes('second'));
        });
        if (matchingProduct) {
          requirements[matchingProduct.key] += load.brick_count;
        }
      }
    });
    return requirements;
  };

  // Calculate periods
  const todayStart = startOfDay(today);
  const todayEnd = endOfDay(today);
  const weekStart = startOfWeek(today, {
    weekStartsOn: 1
  });
  const weekEnd = endOfWeek(today, {
    weekStartsOn: 1
  });

  // Get loads for each period
  const todayLoads = getLoadsForPeriod(todayStart, todayEnd);
  const weekLoads = getLoadsForPeriod(weekStart, weekEnd);

  // Calculate requirements
  const todayRequirements = calculateBrickRequirements(todayLoads);
  const weekRequirements = calculateBrickRequirements(weekLoads);

  // Calculate totals
  const todayTotal = Object.values(todayRequirements).reduce((sum, count) => sum + count, 0);
  const weekTotal = Object.values(weekRequirements).reduce((sum, count) => sum + count, 0);
  if (isLoading) {
    return <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Load Planning Forecast</CardTitle>
          <TrendingUp className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-2">
            <div className="h-4 bg-slate-200 rounded w-1/2"></div>
            <div className="h-3 bg-slate-200 rounded w-3/4"></div>
            <div className="space-y-1">
              {[...Array(4)].map((_, i) => <div key={i} className="h-6 bg-slate-200 rounded"></div>)}
            </div>
          </div>
        </CardContent>
      </Card>;
  }
  return;
};