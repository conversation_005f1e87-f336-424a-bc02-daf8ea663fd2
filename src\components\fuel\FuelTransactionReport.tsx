
import { useState, useMemo, useEffect } from "react";
import { Card, CardHeader, Card<PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { Table, TableHeader, TableHead, TableBody, TableRow, TableCell } from "@/components/ui/table";
import { getFuelBunkers, type FuelBunker } from "@/data/fuelBunkersData";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { format } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { Loader2 } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";

interface FuelDelivery {
  id: string;
  delivery_date: string;
  supplier: string;
  invoice_number: string;
  quantity: number;
  cost_per_liter: number;
}

interface FuelDispensing {
  id: string;
  transaction_date: string;
  asset_id: string;
  quantity_liters: number;
  starting_reading: number | null;
  ending_reading: number | null;
}

export const FuelTransactionReport = () => {
  const { data: fuelBunkersData = [], isLoading: isLoadingBunkers } = useQuery<FuelBunker[]>({
    queryKey: ['fuelBunkers'],
    queryFn: getFuelBunkers,
  });

  const [selectedBunker, setSelectedBunker] = useState("");

  // Set default bunker when data loads
  useEffect(() => {
    if (fuelBunkersData.length > 0 && !selectedBunker) {
      setSelectedBunker(fuelBunkersData[0].id);
    }
  }, [fuelBunkersData, selectedBunker]);

  // Fetch fuel deliveries for selected bunker
  const { data: deliveries = [], isLoading: isLoadingDeliveries } = useQuery({
    queryKey: ['fuelDeliveries', selectedBunker],
    queryFn: async () => {
      if (!selectedBunker) return [];
      const { data, error } = await supabase
        .from('fuel_deliveries')
        .select('*')
        .eq('fuel_bunker_id', selectedBunker)
        .order('delivery_date', { ascending: false });
      
      if (error) throw error;
      return data as FuelDelivery[];
    },
    enabled: !!selectedBunker,
  });

  // Fetch fuel dispensing transactions for selected bunker
  const { data: dispensings = [], isLoading: isLoadingDispensings } = useQuery({
    queryKey: ['fuelDispensings', selectedBunker],
    queryFn: async () => {
      if (!selectedBunker) return [];
      const { data, error } = await supabase
        .from('fuel_dispensing_transactions')
        .select('*')
        .eq('fuel_bunker_id', selectedBunker)
        .order('transaction_date', { ascending: false });
      
      if (error) throw error;
      return data as FuelDispensing[];
    },
    enabled: !!selectedBunker,
  });

  // Combine and sort transactions
  const allTransactions = useMemo(() => {
    const deliveryTxns = deliveries.map(d => ({
      id: d.id,
      date: d.delivery_date,
      type: 'delivery' as const,
      supplier: d.supplier,
      invoiceNumber: d.invoice_number,
      quantity: d.quantity,
      costPerLiter: d.cost_per_liter,
    }));

    const dispensingTxns = dispensings.map(d => ({
      id: d.id,
      date: d.transaction_date,
      type: 'dispensing' as const,
      assetId: d.asset_id,
      litresFilled: d.quantity_liters,
      startReading: d.starting_reading,
      endReading: d.ending_reading,
    }));

    return [...deliveryTxns, ...dispensingTxns]
      .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }, [deliveries, dispensings]);

  const isLoading = isLoadingBunkers || isLoadingDeliveries || isLoadingDispensings;

  return (
    <Card>
      <CardHeader>
        <CardTitle>Fuel Transaction Report</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="mb-4 flex gap-2 items-center">
          <span className="text-sm text-slate-600">Bunker:</span>
          <Select value={selectedBunker} onValueChange={setSelectedBunker} disabled={isLoadingBunkers || !selectedBunker}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Select bunker" />
            </SelectTrigger>
            <SelectContent>
              {fuelBunkersData.map((b) => (
                <SelectItem value={b.id} key={b.id}>
                  {b.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="overflow-x-auto">
          {isLoading ? (
             <div className="flex justify-center items-center py-8">
              <Loader2 className="h-6 w-6 animate-spin text-slate-500" />
            </div>
          ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Date</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Asset / Supplier</TableHead>
                <TableHead>Litres In</TableHead>
                <TableHead>Litres Out</TableHead>
                <TableHead>Info</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {allTransactions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-sm text-slate-500 text-center">
                    No transactions found for this bunker.
                  </TableCell>
                </TableRow>
              ) : (
                allTransactions.map((t) =>
                  t.type === "delivery" ? (
                    <TableRow key={t.id}>
                      <TableCell>{format(new Date(t.date), "yyyy-MM-dd")}</TableCell>
                      <TableCell>Delivery</TableCell>
                      <TableCell>{t.supplier} (Invoice: {t.invoiceNumber})</TableCell>
                      <TableCell>{t.quantity.toLocaleString()}</TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>Cost/L: R{t.costPerLiter}</TableCell>
                    </TableRow>
                  ) : (
                    <TableRow key={t.id}>
                      <TableCell>{format(new Date(t.date), "yyyy-MM-dd")}</TableCell>
                      <TableCell>Dispensing</TableCell>
                      <TableCell>{t.assetId}</TableCell>
                      <TableCell>-</TableCell>
                      <TableCell>{t.litresFilled.toLocaleString()}</TableCell>
                      <TableCell>
                        {t.startReading !== null && `Start: ${t.startReading}`}
                        {t.endReading !== null && t.startReading !== null && `, End: ${t.endReading}`}
                        {t.startReading === null && t.endReading === null && 'No readings'}
                      </TableCell>
                    </TableRow>
                  )
                )
              )}
            </TableBody>
          </Table>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
