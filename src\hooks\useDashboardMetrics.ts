
import { useQuery } from '@tanstack/react-query';
import { TimeRange } from '@/components/dashboard/DashboardContent';
import { getDashboardMetrics } from '@/data/dashboardData';

export function useDashboardMetrics(timeRange: TimeRange) {
  return useQuery({
    queryKey: ['dashboardMetrics', timeRange],
    queryFn: () => getDashboardMetrics(timeRange),
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
}
