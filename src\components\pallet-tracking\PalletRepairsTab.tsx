
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Wrench, Package, AlertTriangle } from "lucide-react";
import { toast } from "sonner";

interface PalletRepairEntry {
  id: string;
  date: string;
  palletsRepaired: number;
  brokenReceived: number;
  brokenWaiting: number;
  factoryPalletCount?: number;
  strappingPalletCount?: number;
  repairer: string;
}

export const PalletRepairsTab = () => {
  const [repairEntries, setRepairEntries] = useState<PalletRepairEntry[]>([]);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().split('T')[0],
    palletsRepaired: "",
    brokenReceived: "",
    brokenWaiting: "",
    factoryPalletCount: "",
    strappingPalletCount: "",
    repairer: ""
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({
      ...prev,
      [e.target.id]: e.target.value
    }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.date || !formData.palletsRepaired || !formData.repairer) {
      toast.error("Please fill in all required fields");
      return;
    }

    const newEntry: PalletRepairEntry = {
      id: Date.now().toString(),
      date: formData.date,
      palletsRepaired: parseInt(formData.palletsRepaired),
      brokenReceived: parseInt(formData.brokenReceived) || 0,
      brokenWaiting: parseInt(formData.brokenWaiting) || 0,
      factoryPalletCount: formData.factoryPalletCount ? parseInt(formData.factoryPalletCount) : undefined,
      strappingPalletCount: formData.strappingPalletCount ? parseInt(formData.strappingPalletCount) : undefined,
      repairer: formData.repairer
    };

    setRepairEntries(prev => [newEntry, ...prev]);
    setFormData({
      date: new Date().toISOString().split('T')[0],
      palletsRepaired: "",
      brokenReceived: "",
      brokenWaiting: "",
      factoryPalletCount: "",
      strappingPalletCount: "",
      repairer: ""
    });
    
    toast.success("Pallet repair entry recorded successfully!");
  };

  const totalRepaired = repairEntries.reduce((sum, entry) => sum + entry.palletsRepaired, 0);
  const totalBrokenReceived = repairEntries.reduce((sum, entry) => sum + entry.brokenReceived, 0);
  const totalBrokenWaiting = repairEntries.reduce((sum, entry) => sum + entry.brokenWaiting, 0);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-green-600">{totalRepaired}</div>
                <p className="text-slate-600">Total Repaired</p>
              </div>
              <Wrench className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-blue-600">{totalBrokenReceived}</div>
                <p className="text-slate-600">Broken Received</p>
              </div>
              <Package className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-2xl font-bold text-red-600">{totalBrokenWaiting}</div>
                <p className="text-slate-600">Waiting Repair</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Recording Form */}
      <Card>
        <CardHeader>
          <CardTitle>Record Pallet Repairs</CardTitle>
          <p className="text-sm text-slate-600">Track daily pallet repair activities and counts</p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="date">Date *</Label>
                <Input
                  id="date"
                  type="date"
                  value={formData.date}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="repairer">Repairer Name *</Label>
                <Input
                  id="repairer"
                  type="text"
                  value={formData.repairer}
                  onChange={handleInputChange}
                  placeholder="Enter repairer name"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="palletsRepaired">Pallets Repaired *</Label>
                <Input
                  id="palletsRepaired"
                  type="number"
                  value={formData.palletsRepaired}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="brokenReceived">Broken Pallets Received</Label>
                <Input
                  id="brokenReceived"
                  type="number"
                  value={formData.brokenReceived}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
              </div>
              
              <div>
                <Label htmlFor="brokenWaiting">Broken Pallets Waiting Repair</Label>
                <Input
                  id="brokenWaiting"
                  type="number"
                  value={formData.brokenWaiting}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
              </div>

              <div>
                <Label htmlFor="factoryPalletCount">Factory Pallet Count</Label>
                <Input
                  id="factoryPalletCount"
                  type="number"
                  value={formData.factoryPalletCount}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
                <p className="text-xs text-slate-500 mt-1">Fortnightly pallet count</p>
              </div>
              
              <div>
                <Label htmlFor="strappingPalletCount">Strapping Pallet Count</Label>
                <Input
                  id="strappingPalletCount"
                  type="number"
                  value={formData.strappingPalletCount}
                  onChange={handleInputChange}
                  placeholder="0"
                  min="0"
                />
                <p className="text-xs text-slate-500 mt-1">Strapping area count</p>
              </div>
            </div>
            
            <Button type="submit" className="bg-slate-800 hover:bg-slate-700">
              Record Repair Entry
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Repair Entries Table */}
      <Card>
        <CardHeader>
          <CardTitle>Repair History</CardTitle>
          <p className="text-sm text-slate-600">Recent pallet repair entries</p>
        </CardHeader>
        <CardContent>
          {repairEntries.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              No repair entries recorded yet
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Date</TableHead>
                  <TableHead>Repairer</TableHead>
                  <TableHead>Pallets Repaired</TableHead>
                  <TableHead>Broken Received</TableHead>
                  <TableHead>Waiting Repair</TableHead>
                  <TableHead>Factory Count</TableHead>
                  <TableHead>Strapping Count</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {repairEntries.map((entry) => (
                  <TableRow key={entry.id}>
                    <TableCell>{entry.date}</TableCell>
                    <TableCell>{entry.repairer}</TableCell>
                    <TableCell>{entry.palletsRepaired}</TableCell>
                    <TableCell>{entry.brokenReceived}</TableCell>
                    <TableCell>{entry.brokenWaiting}</TableCell>
                    <TableCell>{entry.factoryPalletCount || '-'}</TableCell>
                    <TableCell>{entry.strappingPalletCount || '-'}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
