
export function calculateLitresPerHour(startHours: number, endHours: number, litresFilled: number): number | null {
  const hours = endHours - startHours;
  if (hours > 0) return litresFilled / hours;
  return null;
}

export function calculateKmPerLitre(startKm: number, endKm: number, litresFilled: number): number | null {
  const km = endKm - startKm;
  if (litresFilled > 0 && km > 0) return km / litresFilled;
  return null;
}
