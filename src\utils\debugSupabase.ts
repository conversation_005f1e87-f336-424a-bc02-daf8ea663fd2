
import { supabase } from "@/integrations/supabase/client";

export const debugSupabase = () => {
  console.log("🔍 Supabase Debug Info:");
  console.log("📍 Environment:", process.env.NODE_ENV);
  
  // Get configuration from environment variables instead of protected properties
  console.log("🌐 Supabase URL:", import.meta.env.VITE_SUPABASE_URL || "Not configured");
  console.log("🔑 Supabase Key:", import.meta.env.VITE_SUPABASE_ANON_KEY ? "Configured" : "Not configured");
  
  console.log("⚡ Client instance:", supabase ? "✅ Available" : "❌ Not available");
  
  // Test basic connectivity
  supabase
    .from('users')
    .select('count', { count: 'exact', head: true })
    .then(({ error }) => {
      if (error) {
        console.log("❌ Connection test failed:", error.message);
      } else {
        console.log("✅ Connection test successful");
      }
    });
};

export const debugSupabaseConnection = debugSupabase;
