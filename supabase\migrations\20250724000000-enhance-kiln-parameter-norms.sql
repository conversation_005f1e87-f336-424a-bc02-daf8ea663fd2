-- Migration to enhance kiln parameter norms with reasoning and action tracking
-- This adds new fields to support enhanced parameter management and measurement actions

-- Add new columns to kiln_parameter_norms table
ALTER TABLE public.kiln_parameter_norms 
ADD COLUMN IF NOT EXISTS reasoning TEXT,
ADD COLUMN IF NOT EXISTS action_required TEXT,
ADD COLUMN IF NOT EXISTS last_action_taken TEXT,
ADD COLUMN IF NOT EXISTS last_action_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS last_action_by UUID;

-- Add comments to document the new columns
COMMENT ON COLUMN public.kiln_parameter_norms.reasoning IS 'Detailed reasoning behind the parameter limits and their importance';
COMMENT ON COLUMN public.kiln_parameter_norms.action_required IS 'Specific actions required when readings are out of norm';
COMMENT ON COLUMN public.kiln_parameter_norms.last_action_taken IS 'Description of the last action taken for this parameter';
COMMENT ON COLUMN public.kiln_parameter_norms.last_action_date IS 'Timestamp of when the last action was taken';
COMMENT ON COLUMN public.kiln_parameter_norms.last_action_by IS 'User ID of who took the last action';

-- Create table for tracking measurement actions
CREATE TABLE IF NOT EXISTS public.kiln_measurement_actions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  measurement_id UUID NOT NULL,
  parameter_name TEXT NOT NULL,
  reading_value NUMERIC NOT NULL,
  is_out_of_norm BOOLEAN NOT NULL DEFAULT false,
  reasoning TEXT,
  action_required TEXT,
  action_taken TEXT,
  action_date TIMESTAMPTZ,
  action_by UUID,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Add comments to document the measurement actions table
COMMENT ON TABLE public.kiln_measurement_actions IS 'Tracks actions taken for specific measurement readings, especially when out of norm';
COMMENT ON COLUMN public.kiln_measurement_actions.measurement_id IS 'References the specific measurement this action relates to';
COMMENT ON COLUMN public.kiln_measurement_actions.parameter_name IS 'The parameter name this action is for';
COMMENT ON COLUMN public.kiln_measurement_actions.reading_value IS 'The actual reading value that triggered this action';
COMMENT ON COLUMN public.kiln_measurement_actions.is_out_of_norm IS 'Whether this reading was outside the normal parameter range';
COMMENT ON COLUMN public.kiln_measurement_actions.reasoning IS 'Reasoning for why this action was taken';
COMMENT ON COLUMN public.kiln_measurement_actions.action_required IS 'What action was required for this reading';
COMMENT ON COLUMN public.kiln_measurement_actions.action_taken IS 'Description of the actual action that was taken';
COMMENT ON COLUMN public.kiln_measurement_actions.action_date IS 'When the action was taken';
COMMENT ON COLUMN public.kiln_measurement_actions.action_by IS 'User who took the action';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_kiln_measurement_actions_measurement_id ON public.kiln_measurement_actions(measurement_id);
CREATE INDEX IF NOT EXISTS idx_kiln_measurement_actions_parameter_name ON public.kiln_measurement_actions(parameter_name);
CREATE INDEX IF NOT EXISTS idx_kiln_measurement_actions_date ON public.kiln_measurement_actions(action_date);
CREATE INDEX IF NOT EXISTS idx_kiln_measurement_actions_out_of_norm ON public.kiln_measurement_actions(is_out_of_norm);

-- Enable RLS on the new table
ALTER TABLE public.kiln_measurement_actions ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for kiln_measurement_actions
CREATE POLICY "Enable read access for all users" ON public.kiln_measurement_actions
  FOR SELECT USING (true);

CREATE POLICY "Enable insert for all users" ON public.kiln_measurement_actions
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Enable update for all users" ON public.kiln_measurement_actions
  FOR UPDATE USING (true);

CREATE POLICY "Enable delete for all users" ON public.kiln_measurement_actions
  FOR DELETE USING (true);

-- Update existing parameter norms with some default reasoning and action required text
UPDATE public.kiln_parameter_norms 
SET 
  reasoning = CASE 
    WHEN parameter_name = 'Brick Core Temp' THEN 'Core temperature is critical for proper brick firing and strength development. Too low results in underfired bricks, too high can cause cracking or deformation.'
    WHEN parameter_name = 'Brick Moisture' THEN 'Proper moisture content ensures even drying and prevents cracking during the firing process. High moisture can cause steam explosions.'
    WHEN parameter_name = 'CO' THEN 'Carbon monoxide levels indicate combustion efficiency. High CO levels suggest incomplete combustion and potential safety hazards.'
    WHEN parameter_name = 'CO₂' THEN 'Carbon dioxide levels indicate combustion efficiency and fuel utilization. Proper levels ensure optimal fuel consumption.'
    WHEN parameter_name = 'Cooling Zone Temp' THEN 'Controlled cooling prevents thermal shock and cracking. Too fast cooling can cause stress fractures in bricks.'
    WHEN parameter_name = 'Draught Pressure' THEN 'Proper draught ensures adequate air flow for combustion and heat distribution throughout the kiln.'
    ELSE 'Parameter monitoring is essential for optimal kiln operation and product quality.'
  END,
  action_required = CASE 
    WHEN parameter_name = 'Brick Core Temp' THEN 'Monitor fire speed and adjust fuel input. Check temperature distribution across chambers. Verify thermocouple accuracy.'
    WHEN parameter_name = 'Brick Moisture' THEN 'Improve drying process. Check loading patterns. Verify drying chamber conditions and air circulation.'
    WHEN parameter_name = 'CO' THEN 'Adjust air-fuel ratio. Check for air leaks. Ensure proper fuel mixing. Monitor combustion air supply.'
    WHEN parameter_name = 'CO₂' THEN 'Fine-tune air-fuel ratio. Check fuel quality. Verify combustion air distribution and mixing.'
    WHEN parameter_name = 'Cooling Zone Temp' THEN 'Adjust cooling fan speeds. Check air dampers. Monitor cooling curve progression.'
    WHEN parameter_name = 'Draught Pressure' THEN 'Inspect fan operation. Check for blockages in flues. Verify damper positions and seal integrity.'
    ELSE 'Investigate cause and take corrective action based on parameter-specific procedures.'
  END
WHERE reasoning IS NULL OR action_required IS NULL;

-- Create a function to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for kiln_measurement_actions table
CREATE TRIGGER update_kiln_measurement_actions_updated_at 
    BEFORE UPDATE ON public.kiln_measurement_actions 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Grant necessary permissions
GRANT ALL ON public.kiln_measurement_actions TO authenticated;
GRANT ALL ON public.kiln_measurement_actions TO anon;
