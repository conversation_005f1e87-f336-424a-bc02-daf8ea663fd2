import { useState } from "react";
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { X, Loader2 } from "lucide-react";
import { toast } from "sonner";
import { useQuery, useQueryClient } from "@tanstack/react-query";

import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { getFires, FireConfig, getKilns, KilnConfig } from "@/data/kilnData";
import { getEmployees, Employee } from "@/data/employeeData";
import { addDehackingEntry } from "@/data/dehackingStore";
import { useDehackingTeams, useTeamMembers } from "@/hooks/useTeams";
import { Team } from "@/data/fuelBunkersData";

const hours = [
  "06:00", "07:00", "08:00", "09:00", "10:00", "11:00", "12:00", "13:00",
  "14:00", "15:00", "16:00", "17:00", "18:00", "19:00", "20:00"
];

interface NewQuickDehackingEntryDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const NewQuickDehackingEntryDialog = ({ isOpen, onClose }: NewQuickDehackingEntryDialogProps) => {
  const queryClient = useQueryClient();
  const [date, setDate] = useState(() => new Date().toISOString().slice(0, 10));
  const [teamId, setTeamId] = useState<string | undefined>();
  const [employeeId, setEmployeeId] = useState<string | undefined>();
  const [brickTypeId, setBrickTypeId] = useState<string | undefined>();
  const [palletCount, setPalletCount] = useState("");
  const [hour, setHour] = useState<string | undefined>();
  const [isOvertime, setIsOvertime] = useState(false);
  const [isNightShift, setIsNightShift] = useState(false);
  const [fireId, setFireId] = useState<string | undefined>();
  const [chamberNumber, setChamberNumber] = useState<string | undefined>();

  const { data: managementBrickTypesData = [], isLoading: isLoadingBrickTypes } = useQuery<ManagementBrickType[]>({
    queryKey: ['managementBrickTypes'],
    queryFn: getManagementBrickTypes,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  const { data: dehackingTeams = [], isLoading: isLoadingTeams } = useDehackingTeams();

  // Get team members for the selected team
  const { data: teamMembers = [], isLoading: isLoadingTeamMembers } = useTeamMembers(teamId);

  // Get fires data
  const { data: firesData = [], isLoading: isLoadingFires } = useQuery<FireConfig[]>({
    queryKey: ['fires'],
    queryFn: getFires,
  });

  // Get kilns data
  const { data: kilnsData = [], isLoading: isLoadingKilns } = useQuery<KilnConfig[]>({
    queryKey: ['kilns'],
    queryFn: getKilns,
  });

  // Get the selected fire to determine available chambers
  const selectedFire = firesData.find(f => f.id === fireId);
  const selectedKiln = kilnsData.find(k => k.id === selectedFire?.kiln_id);

  // Generate chamber numbers based on kiln type: Habla gets 12, Kilns 1-5 get 24
  const chambers = Array.from({ 
    length: selectedKiln?.name === "Habla" ? 12 : 24 
  }, (_, i) => i + 1);

  // Debug: Log the teams and members data
  console.log('Dehacking teams loaded:', dehackingTeams);
  console.log('Teams loading state:', isLoadingTeams);
  console.log('Selected team ID:', teamId);
  console.log('Team members loaded:', teamMembers);
  console.log('Team members loading state:', isLoadingTeamMembers);

  const handleClose = () => {
    setDate(new Date().toISOString().slice(0, 10));
    setTeamId(undefined);
    setEmployeeId(undefined);
    setBrickTypeId(undefined);
    setPalletCount("");
    setHour(undefined);
    setIsOvertime(false);
    setIsNightShift(false);
    setFireId(undefined);
    setChamberNumber(undefined);
    onClose();
  };

  const handleSave = async () => {
    console.log("🎯 Starting dehacking entry save...");
    console.log("📝 Form data:", { date, teamId, employeeId, brickTypeId, hour, palletCount, isOvertime, isNightShift, fireId, chamberNumber });

    const parsedPallets = parseInt(palletCount);
    if (!teamId || !employeeId || !brickTypeId || !hour || !palletCount || parsedPallets <= 0 || !fireId || !chamberNumber) {
      console.error("❌ Validation failed:", { teamId, employeeId, brickTypeId, hour, palletCount, parsedPallets, fireId, chamberNumber });
      toast.error("Please fill out all fields with valid values.");
      return;
    }

    try {
      // Convert hour string (e.g., "14:00") to just the hour number (e.g., 14)
      const hourNumber = parseInt(hour.split(':')[0]);

      console.log("💾 Calling addDehackingEntry with:", {
        date,
        employeeId: parseInt(employeeId),
        brickTypeId,
        palletCount: parsedPallets,
        hour: hourNumber,
        teamId,
        isOvertime,
        isNightShift,
        fireId,
        chamberNumber: parseInt(chamberNumber),
      });

      await addDehackingEntry({
        date,
        employeeId: parseInt(employeeId),
        brickTypeId,
        palletCount: parsedPallets,
        hour: hourNumber,
        teamId,
        isOvertime,
        isNightShift,
        fireId,
        chamberNumber: parseInt(chamberNumber),
      });

      console.log("✅ Dehacking entry saved successfully!");
      toast.success("Dehacking entry recorded!");
      queryClient.invalidateQueries({ queryKey: ['dehackingForLoss'] });
      queryClient.invalidateQueries({ queryKey: ['dehackingBreakdown'] });
      handleClose();
    } catch (error) {
      console.error("❌ Failed to save dehacking entry:", error);
      console.error("❌ Error details:", JSON.stringify(error, null, 2));
      toast.error(`Failed to save entry: ${error.message || 'Unknown error'}`);
    }
  };

  const isLoading = isLoadingBrickTypes || isLoadingTeams || isLoadingFires || isLoadingKilns;

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleClose()}>
      <DialogContent className="sm:max-w-4xl p-6 bg-white rounded-lg">
        <DialogHeader className="mb-4">
          <DialogTitle className="text-xl font-semibold">Quick Dehacking Entry</DialogTitle>
           <button onClick={handleClose} className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground">
            <X className="h-4 w-4" />
            <span className="sr-only">Close</span>
          </button>
          <DialogDescription>Record a new dehacking entry.</DialogDescription>
        </DialogHeader>
        {isLoading ? (
          <div className="flex items-center justify-center h-64">
            <Loader2 className="h-6 w-6 animate-spin mr-2" />
            <span>Loading data...</span>
          </div>
        ) : (
          <div className="grid grid-cols-3 gap-x-6 gap-y-4">
            <div>
              <Label htmlFor="dehackingDate">Date</Label>
              <Input id="dehackingDate" type="date" value={date} onChange={e => setDate(e.target.value)} />
            </div>
            <div>
              <Label>Team</Label>
              <Select value={teamId} onValueChange={(value) => {
                setTeamId(value);
                // Reset employee selection when team changes
                setEmployeeId(undefined);
              }}>
                <SelectTrigger><SelectValue placeholder="Select team" /></SelectTrigger>
                <SelectContent>
                  {dehackingTeams.length === 0 ? (
                    <SelectItem value="no-teams" disabled>No teams available</SelectItem>
                  ) : (
                    dehackingTeams.map(team => (
                      <SelectItem key={team.id} value={team.id}>
                        {team.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {/* Debug info */}
              <div className="text-xs text-gray-500 mt-1">
                Teams loaded: {dehackingTeams.length} | Loading: {isLoadingTeams ? 'Yes' : 'No'}
              </div>
            </div>
            <div>
              <Label>Employee</Label>
              <Select value={employeeId} onValueChange={setEmployeeId} disabled={!teamId}>
                <SelectTrigger><SelectValue placeholder={!teamId ? "Select team first" : "Select employee"} /></SelectTrigger>
                <SelectContent>
                  {!teamId ? (
                    <SelectItem value="no-team" disabled>Please select a team first</SelectItem>
                  ) : teamMembers.length === 0 ? (
                    <SelectItem value="no-members" disabled>No members in this team</SelectItem>
                  ) : teamMembers.length >= 40 ? (
                    <SelectItem value="team-full" disabled>Team is full (40/40 members)</SelectItem>
                  ) : (
                    teamMembers.map(emp => (
                      <SelectItem key={emp.id} value={String(emp.id)}>
                        {emp.name}
                      </SelectItem>
                    ))
                  )}
                </SelectContent>
              </Select>
              {/* Debug info */}
              <div className="text-xs text-gray-500 mt-1">
                Team members: {teamMembers.length} | Loading: {isLoadingTeamMembers ? 'Yes' : 'No'}
              </div>
            </div>
            <div>
              <Label htmlFor="fire-select" className="font-medium">Fire</Label>
              <Select value={fireId || ""} onValueChange={(value) => {
                setFireId(value);
                setChamberNumber(undefined); // Reset chamber when fire changes
              }}>
                <SelectTrigger id="fire-select" className="w-full">
                  <SelectValue placeholder="Select fire" />
                </SelectTrigger>
                <SelectContent>
                  {isLoadingFires && <SelectItem value="" disabled>Loading...</SelectItem>}
                  {firesData.map(fire => (
                    <SelectItem key={fire.id} value={fire.id}>{fire.name}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="chamber-select" className="font-medium">Chamber</Label>
              <Select value={chamberNumber || ""} onValueChange={setChamberNumber} disabled={!fireId}>
                <SelectTrigger id="chamber-select" className="w-full">
                  <SelectValue placeholder={!fireId ? "Select fire first" : "Select chamber"} />
                </SelectTrigger>
                <SelectContent>
                  {chambers.map(chamber => (
                    <SelectItem key={chamber} value={chamber.toString()}>
                      Chamber {chamber}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Brick Type</Label>
              <Select value={brickTypeId} onValueChange={setBrickTypeId}>
                <SelectTrigger><SelectValue placeholder="Select brick type" /></SelectTrigger>
                <SelectContent>
                  {managementBrickTypesData.map(bt => <SelectItem key={bt.id} value={bt.id}>{bt.name}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label>Hour</Label>
              <Select value={hour} onValueChange={setHour}>
                <SelectTrigger><SelectValue placeholder="Select hour" /></SelectTrigger>
                <SelectContent>
                  {hours.map(h => <SelectItem key={h} value={h}>{h}</SelectItem>)}
                </SelectContent>
              </Select>
            </div>
            <div>
              <Label htmlFor="dehackingPallets">Pallet Count</Label>
              <Input id="dehackingPallets" type="number" placeholder="e.g. 10" value={palletCount} onChange={e => setPalletCount(e.target.value)} />
            </div>

            {/* Overtime and Night Shift Radio Buttons */}
            <div className="col-span-3 grid grid-cols-2 gap-4">
              <div className="flex flex-col gap-2">
                <Label className="font-medium">Overtime</Label>
                <RadioGroup 
                  value={isOvertime ? "yes" : "no"} 
                  onValueChange={(value) => setIsOvertime(value === "yes")}
                  className="flex flex-row gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="dehacking-overtime-no" />
                    <Label htmlFor="dehacking-overtime-no" className="text-sm">No</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="dehacking-overtime-yes" />
                    <Label htmlFor="dehacking-overtime-yes" className="text-sm">Yes</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="flex flex-col gap-2">
                <Label className="font-medium">Night Shift</Label>
                <RadioGroup 
                  value={isNightShift ? "yes" : "no"} 
                  onValueChange={(value) => setIsNightShift(value === "yes")}
                  className="flex flex-row gap-4"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="dehacking-nightshift-no" />
                    <Label htmlFor="dehacking-nightshift-no" className="text-sm">No</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="dehacking-nightshift-yes" />
                    <Label htmlFor="dehacking-nightshift-yes" className="text-sm">Yes</Label>
                  </div>
                </RadioGroup>
              </div>
            </div>

            {/* Summary display */}
            <div className="col-span-3 bg-gray-50 border p-2 rounded text-sm text-gray-700 my-2">
              {(isOvertime || isNightShift) && (
                <div className="text-blue-600 mb-2">
                  {isNightShift && <span className="mr-2">🌙 Night Shift</span>}
                  {isOvertime && <span>⏰ Overtime</span>}
                </div>
              )}
              {selectedKiln && chamberNumber && (
                <div>
                  <strong>Kiln:</strong> {selectedKiln.name}, <strong>Chamber:</strong> {chamberNumber}
                </div>
              )}
              {selectedFire && (
                <div>
                  <strong>Fire:</strong> {selectedFire.name}
                </div>
              )}
            </div>
          </div>
        )}
        <DialogFooter className="mt-6">
          <Button variant="outline" onClick={handleClose}>Cancel</Button>
          <Button onClick={handleSave} className="bg-green-500 hover:bg-green-600" disabled={isLoading}>Save Entry</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
