
-- Update the fuel dispensing trigger to prevent negative fuel levels
CREATE OR REPLACE FUNCTION public.handle_fuel_dispensing()
RETURNS TRIGGER AS $$
DECLARE
  current_fuel_level numeric;
BEGIN
  -- Get the current fuel level
  SELECT current_level INTO current_fuel_level
  FROM public.fuel_bunkers
  WHERE id = NEW.fuel_bunker_id;
  
  -- Check if there's enough fuel
  IF current_fuel_level < NEW.quantity_liters THEN
    RAISE EXCEPTION 'Insufficient fuel in bunker. Available: %L, Requested: %L', 
      current_fuel_level, NEW.quantity_liters;
  END IF;
  
  -- Subtract the dispensed quantity from the bunker's current level
  UPDATE public.fuel_bunkers
  SET current_level = GREATEST(0, current_level - NEW.quantity_liters)
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Ensure current levels cannot go below zero (fix existing negative values)
UPDATE public.fuel_bunkers 
SET current_level = GREATEST(0, current_level)
WHERE current_level < 0;
