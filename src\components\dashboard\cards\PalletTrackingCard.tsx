
import { <PERSON>, CardContent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Truck, Package, ArrowRight } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getAllPalletMovements } from "@/data/palletTrackingData";

interface PalletTrackingCardProps {
  onManagePallets: () => void;
}

export const PalletTrackingCard = ({ onManagePallets }: PalletTrackingCardProps) => {
  const { data: movements = [] } = useQuery({
    queryKey: ["pallet-movements"],
    queryFn: getAllPalletMovements,
  });

  // Calculate quick stats
  const totalPallets = movements.reduce((sum, m) => sum + m.pallets_loaded, 0);
  const inTransit = movements.filter(m => m.status === "In Transit").reduce((sum, m) => sum + m.pallets_loaded, 0);
  const pendingReturn = movements.filter(m => m.status === "Pending Return").reduce((sum, m) => sum + m.pallets_loaded, 0);

  return (
    <Card className="bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-blue-800">
          <Truck size={20} />
          Pallet Tracking
        </CardTitle>
        <p className="text-sm text-blue-600">Monitor pallet deliveries</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div className="text-center">
            <div className="text-sm text-blue-600">Total Pallets</div>
            <div className="text-2xl font-bold text-blue-800">{totalPallets.toLocaleString()}</div>
          </div>
          <div className="text-center">
            <div className="text-sm text-blue-600">In Transit</div>
            <div className="text-2xl font-bold text-blue-800">{inTransit.toLocaleString()}</div>
          </div>
        </div>
        
        <div className="flex items-center justify-between text-sm text-blue-700">
          <span>Pending Return</span>
          <span className="font-semibold">{pendingReturn} pallets</span>
        </div>
        
        <Button 
          onClick={onManagePallets}
          className="w-full bg-blue-600 hover:bg-blue-700 text-white"
        >
          <Package size={16} className="mr-2" />
          Manage Pallets
          <ArrowRight size={16} className="ml-2" />
        </Button>
      </CardContent>
    </Card>
  );
};
