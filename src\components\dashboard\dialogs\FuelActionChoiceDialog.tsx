
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Droplet, Truck } from "lucide-react";

interface FuelActionChoiceDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onRecordDispensing: () => void;
  onRecordDelivery: () => void;
}

export const FuelActionChoiceDialog = ({
  isOpen,
  onClose,
  onRecordDispensing,
  onRecordDelivery,
}: FuelActionChoiceDialogProps) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Manage Fuel</DialogTitle>
          <DialogDescription>
            What would you like to do?
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col space-y-4 py-4">
          <Button onClick={onRecordDispensing} variant="outline" className="w-full justify-start text-base py-6">
            <Droplet className="mr-2 h-5 w-5" />
            Record Fuel Dispensing
          </Button>
          <Button onClick={onRecordDelivery} variant="outline" className="w-full justify-start text-base py-6">
            <Truck className="mr-2 h-5 w-5" />
            Record Fuel Delivery
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
};
