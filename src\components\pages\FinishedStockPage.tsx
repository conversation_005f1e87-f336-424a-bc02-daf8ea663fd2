
import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>nt, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Calculator, Plus, Calendar, Hash, Loader2, Package } from "lucide-react";
import { toast } from "sonner";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useFinishedProductCounts, useAddFinishedProductCount } from "@/hooks/useFinishedProductCounts";
import { format } from "date-fns";
import { useActivityTracking } from "@/hooks/useActivityTracking";

const productTypes = [
  "Imperial 2nd Grade", 
  "Imperial Nfp", 
  "Imperial NFX", 
  "Imperial Blue", 
  "Maxi 2nd Grade", 
  "Maxi NFP", 
  "Maxi NFX"
] as const;

type ProductType = typeof productTypes[number];

const productTypeFields = productTypes.reduce((acc, pt) => {
  const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_');
  acc[fieldName] = z.number().min(0, "Count must be non-negative").optional();
  return acc;
}, {} as Record<string, z.ZodOptional<z.ZodNumber>>);

const finishedProductCountSchema = z.object({
  date: z.string().min(1, "Date is required"),
  notes: z.string().optional(),
  ...productTypeFields
}).refine(data => {
  return Object.keys(productTypeFields).some(field => (data[field] || 0) > 0);
}, {
  message: "At least one product count is required",
  path: [`${productTypes[0].toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_')}`]
});

type FinishedProductCountFormValues = z.infer<typeof finishedProductCountSchema>;

export const FinishedStockPage = () => {
  const [isFormVisible, setIsFormVisible] = useState(false);
  const {
    data: finishedProductCounts = [],
    isLoading,
    error
  } = useFinishedProductCounts();
  const addFinishedProductCountMutation = useAddFinishedProductCount();
  const { logActivity } = useActivityTracking();
  const form = useForm<FinishedProductCountFormValues>({
    resolver: zodResolver(finishedProductCountSchema),
    defaultValues: {
      date: format(new Date(), "yyyy-MM-dd"),
      notes: "",
      ...productTypes.reduce((acc, pt) => {
        const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_');
        acc[fieldName] = 0;
        return acc;
      }, {} as Record<string, number>)
    }
  });

  // Debug form state
  console.log("📋 Form state:", form.watch());
  console.log("❌ Form errors:", form.formState.errors);

  const onSubmit = (data: FinishedProductCountFormValues) => {
    console.log("🎯 Form submitted with data:", data);

    const countsToSubmit: { date: string; pallet_count: number; product_type: ProductType; notes: string; }[] = [];

    productTypes.forEach(pt => {
      const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_') as keyof FinishedProductCountFormValues;
      const count = Number(data[fieldName]);
      if (count && count > 0) {
        countsToSubmit.push({
          date: data.date,
          pallet_count: count,
          product_type: pt,
          notes: data.notes || ""
        });
      }
    });

    if (countsToSubmit.length === 0) {
      toast.warning("Please enter a count for at least one product type.");
      return;
    }

    countsToSubmit.forEach(submitData => {
      addFinishedProductCountMutation.mutate(submitData, {
        onSuccess: result => {
          console.log("✅ Mutation successful for:", submitData.product_type, result);
          toast.success(`Count for ${submitData.product_type} recorded successfully!`);
          
          // Log the activity
          logActivity(
            `Recorded ${submitData.product_type} Count`,
            `Added ${submitData.pallet_count} pallets for ${format(new Date(submitData.date), "MMM dd, yyyy")}`,
            'production'
          );
        },
        onError: error => {
          console.error(`❌ Mutation failed for ${submitData.product_type}:`, error);
          toast.error(`Failed to record count for ${submitData.product_type}: ${error.message}`);
        }
      });
    });

    // Reset form after all mutations are initiated
    form.reset({
      date: format(new Date(), "yyyy-MM-dd"),
      notes: "",
      ...productTypes.reduce((acc, pt) => {
        const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_');
        acc[fieldName] = 0;
        return acc;
      }, {} as Record<string, number>)
    });
    setIsFormVisible(false);
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Finished Stock Count</h1>
          <p className="text-slate-600">Track finished stock pallets to prevent theft</p>
        </div>
        <div className="flex gap-2">
          <Button onClick={() => setIsFormVisible(!isFormVisible)} className="flex items-center gap-2">
            <Plus size={20} />
            {isFormVisible ? "Cancel" : "Add Count"}
          </Button>
        </div>
      </div>

      {/* Add Count Form */}
      {isFormVisible && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator size={20} />
              Record Finished Product Count
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="space-y-2">
                <Label htmlFor="date" className="flex items-center gap-2">
                  <Calendar size={16} />
                  Date
                </Label>
                <Input id="date" type="date" {...form.register("date")} className="w-full max-w-sm" />
                {form.formState.errors.date && <p className="text-sm text-red-600">{form.formState.errors.date.message}</p>}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-6">
                {/* Imperial Products */}
                <div className="space-y-4 p-4 border rounded-lg">
                  <h3 className="font-semibold text-lg text-slate-700">Imperial Bricks</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {productTypes.filter(pt => pt.startsWith("Imperial")).map(pt => {
                      const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_') as keyof FinishedProductCountFormValues;
                      return (
                        <div className="space-y-2" key={fieldName}>
                          <Label htmlFor={fieldName}>{pt.replace("Imperial ", "")}</Label>
                          <Input id={fieldName} type="number" min="0" placeholder="Count" {...form.register(fieldName, { valueAsNumber: true })} />
                        </div>
                      )
                    })}
                  </div>
                </div>

                {/* Maxi Products */}
                <div className="space-y-4 p-4 border rounded-lg">
                  <h3 className="font-semibold text-lg text-slate-700">Maxi Bricks</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    {productTypes.filter(pt => pt.startsWith("Maxi")).map(pt => {
                      const fieldName = pt.toLowerCase().replace(/ /g, '_').replace('2nd_', 'second_') as keyof FinishedProductCountFormValues;
                      return (
                        <div className="space-y-2" key={fieldName}>
                          <Label htmlFor={fieldName}>{pt.replace("Maxi ", "")}</Label>
                          <Input id={fieldName} type="number" min="0" placeholder="Count" {...form.register(fieldName, { valueAsNumber: true })} />
                        </div>
                      )
                    })}
                  </div>
                </div>
              </div>
              
              {Object.keys(form.formState.errors).length > 0 && (
                <div className="text-sm text-red-600">
                  {Object.entries(form.formState.errors).map(([fieldName, error]) => (
                    (error as any).message && fieldName !== 'root' && <p key={fieldName}>{(error as any).message}</p>
                  ))}
                  {form.formState.errors.root && <p>{form.formState.errors.root.message}</p>}
                </div>
              )}

              <div className="space-y-2 pt-2">
                <Label htmlFor="notes">Notes (optional)</Label>
                <Textarea id="notes" placeholder="Add any additional notes about the count..." rows={3} {...form.register("notes")} className="resize-none" />
              </div>

              <div className="flex gap-3 pt-4">
                <Button type="submit" disabled={addFinishedProductCountMutation.isPending} className="flex items-center gap-2">
                  {addFinishedProductCountMutation.isPending && <Loader2 className="h-4 w-4 animate-spin" />}
                  Record Count
                </Button>
                <Button type="button" variant="outline" onClick={() => setIsFormVisible(false)}>
                  Cancel
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Recent Counts */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Finished Product Counts</CardTitle>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex justify-center py-8">
              <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
            </div>
          ) : error ? (
            <div className="text-center py-8 text-red-500">
              <p>Error loading finished product counts: {error.message}</p>
            </div>
          ) : finishedProductCounts.length === 0 ? (
            <div className="text-center py-8 text-slate-500">
              <Calculator className="h-12 w-12 mx-auto mb-4 text-slate-300" />
              <p>No finished product counts recorded yet.</p>
              <p className="text-sm">Click "Add Count" to record your first count.</p>
            </div>
          ) : (
            <div className="space-y-3">
              {finishedProductCounts.slice(0, 10).map(count => (
                <div key={count.id} className="flex items-center justify-between p-4 bg-slate-50 rounded-lg border">
                  <div className="flex items-center gap-4">
                    <div className="flex items-center gap-2">
                      <Calendar size={16} className="text-slate-500" />
                      <span className="font-medium">{format(new Date(count.date), "MMM dd, yyyy")}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Package size={16} className="text-slate-500" />
                      <span className="text-lg font-semibold text-slate-800">{count.pallet_count.toLocaleString()}</span>
                      <span className="text-sm text-slate-500">{count.product_type}</span>
                    </div>
                  </div>
                  <div className="text-sm text-slate-500">
                    {format(new Date(count.created_at), "HH:mm")}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
