import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon, Clock, Thermometer, AlertTriangle, CheckCircle, Activity } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { useDailySummary } from '@/hooks/useKilnMonitoring';
import { DailySummaryTimeSlot } from '@/types/kilnMonitoring';

export const DailySummaryView: React.FC = () => {
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const { data: dailySummary, isLoading } = useDailySummary(format(selectedDate, 'yyyy-MM-dd'));

  const getStatusColor = (status: 'normal' | 'warning' | 'critical') => {
    switch (status) {
      case 'critical':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-green-100 text-green-800 border-green-200';
    }
  };

  const getStatusIcon = (status: 'normal' | 'warning' | 'critical') => {
    switch (status) {
      case 'critical':
        return <AlertTriangle className="h-3 w-3" />;
      case 'warning':
        return <AlertTriangle className="h-3 w-3" />;
      default:
        return <CheckCircle className="h-3 w-3" />;
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Activity className="h-5 w-5" />
            Daily Summary
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-700"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Date Selector and Summary Stats */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle className="flex items-center gap-2">
              <Activity className="h-5 w-5" />
              Daily Summary
            </CardTitle>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "justify-start text-left font-normal",
                    !selectedDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {selectedDate ? format(selectedDate, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={selectedDate}
                  onSelect={(date) => date && setSelectedDate(date)}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-slate-800">
                {dailySummary?.total_measurements || 0}
              </div>
              <div className="text-sm text-slate-600">Total Measurements</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">
                {dailySummary?.parameters_out_of_norm || 0}
              </div>
              <div className="text-sm text-slate-600">Out of Norm</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">
                {dailySummary?.actions_taken || 0}
              </div>
              <div className="text-sm text-slate-600">Actions Taken</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Time Slot Details */}
      <div className="space-y-4">
        {dailySummary?.time_slots?.map((timeSlot: DailySummaryTimeSlot) => {
          const hasData = Object.keys(timeSlot.kilns).length > 0;
          
          if (!hasData) {
            return (
              <Card key={timeSlot.time_slot} className="opacity-50">
                <CardHeader className="pb-3">
                  <CardTitle className="flex items-center gap-2 text-lg">
                    <Clock className="h-4 w-4" />
                    {timeSlot.time_slot}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-center text-slate-500 py-4">
                    No measurements recorded for this time slot
                  </div>
                </CardContent>
              </Card>
            );
          }

          return (
            <Card key={timeSlot.time_slot}>
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-lg">
                  <Clock className="h-4 w-4" />
                  {timeSlot.time_slot}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {Object.entries(timeSlot.kilns).map(([kilnId, kilnData]) => (
                    <div key={kilnId} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-4">
                        <h4 className="font-semibold text-slate-800 flex items-center gap-2">
                          <Thermometer className="h-4 w-4" />
                          {kilnData.kiln_name}
                        </h4>
                        <Badge variant="outline">
                          {kilnData.total_tests} test{kilnData.total_tests !== 1 ? 's' : ''} performed
                        </Badge>
                      </div>

                      {/* Test Results */}
                      <div className="space-y-3">
                        <div>
                          <h5 className="text-sm font-medium text-slate-700 mb-2">Test Results:</h5>
                          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                            {Object.entries(kilnData.measurements).map(([parameter, measurement]) => (
                              <div
                                key={parameter}
                                className={cn(
                                  "p-2 rounded border text-xs",
                                  getStatusColor(measurement.status)
                                )}
                              >
                                <div className="flex items-center justify-between">
                                  <span className="font-medium">{parameter}</span>
                                  {getStatusIcon(measurement.status)}
                                </div>
                                <div className="mt-1">
                                  <span className="font-bold">{measurement.value}</span>
                                </div>
                                {measurement.action_taken && (
                                  <div className="mt-1 text-xs text-slate-600">
                                    Action: {measurement.action_taken}
                                  </div>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Tests Performed Summary */}
                        <div>
                          <h5 className="text-sm font-medium text-slate-700 mb-2">Different Tests Performed:</h5>
                          <div className="flex flex-wrap gap-1">
                            {kilnData.tests_performed.map((test) => (
                              <Badge key={test} variant="secondary" className="text-xs">
                                {test}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* No Data Message */}
      {(!dailySummary?.time_slots || dailySummary.time_slots.length === 0) && (
        <Card>
          <CardContent className="py-8">
            <div className="text-center text-slate-500">
              <Activity className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="text-lg font-medium mb-2">No Data Available</h3>
              <p>No measurements were recorded for {format(selectedDate, "PPP")}</p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
