
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>itle, CardContent } from "@/components/ui/card";
import { ArrowLeftRight, ArrowDown, ArrowUp, Package } from "lucide-react";
import { useStockMovement } from "@/hooks/useStockMovement";

export const StockMovementSummaryCard = () => {
  const { inboundMovements, outboundMovements, isLoading } = useStockMovement();

  if (isLoading) {
    return (
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Stock Yard Movement</CardTitle>
          <ArrowLeftRight className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-slate-700"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  // Calculate totals
  const totalReceived = inboundMovements
    .filter(movement => movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalPending = inboundMovements
    .filter(movement => !movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalSold = outboundMovements
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const stockOnHand = totalReceived - totalSold;

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Stock Yard Movement</CardTitle>
        <ArrowLeftRight className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ArrowDown className="h-3 w-3 text-green-600" />
              <span className="text-xs text-muted-foreground">Received</span>
            </div>
            <span className="text-sm font-medium text-green-600">
              {totalReceived.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-3 w-3 text-orange-600" />
              <span className="text-xs text-muted-foreground">Pending</span>
            </div>
            <span className="text-sm font-medium text-orange-600">
              {totalPending.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Package className="h-3 w-3 text-blue-600" />
              <span className="text-xs text-muted-foreground">On Hand</span>
            </div>
            <span className="text-sm font-medium text-blue-600">
              {stockOnHand.toLocaleString()}
            </span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <ArrowUp className="h-3 w-3 text-red-600" />
              <span className="text-xs text-muted-foreground">Sold</span>
            </div>
            <span className="text-sm font-medium text-red-600">
              {totalSold.toLocaleString()}
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
