
import { Employee } from "@/data/employeeData";
import { NewPaymentDialog } from "./NewPaymentDialog";

interface PaymentsHeaderProps {
  employees: Employee[];
}

export const PaymentsHeader = ({ employees }: PaymentsHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h1 className="text-3xl font-bold text-slate-800">Payment Management</h1>
        <p className="text-slate-600">Manage payroll and employee compensation</p>
      </div>
      <NewPaymentDialog employees={employees} />
    </div>
  );
};
