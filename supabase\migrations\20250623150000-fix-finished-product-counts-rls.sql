-- Grant all rights to the finished_product_counts table
ALTER TABLE public.finished_product_counts ENABLE ROW LEVEL SECURITY;

-- Drop existing policies to avoid conflicts
DROP POLICY IF EXISTS "Users can insert finished product counts" ON public.finished_product_counts;
DROP POLICY IF EXISTS "Users can update finished product counts" ON public.finished_product_counts;
DROP POLICY IF EXISTS "Users can delete finished product counts" ON public.finished_product_counts;
DROP POLICY IF EXISTS "Users can view all finished product counts" ON public.finished_product_counts;

-- Create new policies that grant full access
CREATE POLICY "Allow full access to finished_product_counts"
ON public.finished_product_counts
FOR ALL
USING (true)
WITH CHECK (true);
