-- ========================================
-- HACKLINE COUNTS TABLE SETUP
-- ========================================
-- Copy and paste this ENTIRE script into your Supabase SQL Editor and click RUN

-- Step 1: Create the table
CREATE TABLE IF NOT EXISTS public.hackline_counts (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE NOT NULL,
  count_total INT NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id),
  notes TEXT
);

-- Step 2: Enable Row Level Security
ALTER TABLE public.hackline_counts ENABLE ROW LEVEL SECURITY;

-- Step 3: Create policies (drop existing ones first to avoid conflicts)
DROP POLICY IF EXISTS "Allow all users to view hackline counts" ON public.hackline_counts;
DROP POLICY IF EXISTS "Allow authenticated users to insert hackline counts" ON public.hackline_counts;
DROP POLICY IF EXISTS "Allow authenticated users to update hackline counts" ON public.hackline_counts;

-- Create new policies
CREATE POLICY "Allow all users to view hackline counts" ON public.hackline_counts
  FOR SELECT USING (true);

CREATE POLICY "Allow authenticated users to insert hackline counts" ON public.hackline_counts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Allow authenticated users to update hackline counts" ON public.hackline_counts
  FOR UPDATE USING (true);

-- Step 4: Verify the table was created
SELECT 'Table created successfully!' as status;
SELECT * FROM public.hackline_counts LIMIT 1;
