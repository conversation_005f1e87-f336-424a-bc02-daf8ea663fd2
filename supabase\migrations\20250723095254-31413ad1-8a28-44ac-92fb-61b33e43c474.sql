
-- Create table for hourly rate records
CREATE TABLE public.hourly_rate_records (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id integer NOT NULL,
  team_id text NOT NULL,
  date date NOT NULL,
  start_time time NOT NULL,
  stop_time time NOT NULL,
  hours_worked numeric NOT NULL,
  hourly_rate numeric NOT NULL DEFAULT 28.79,
  total_pay numeric NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Create table for strapping records
CREATE TABLE public.strapping_records (
  id uuid NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  employee_id integer NOT NULL,
  date date NOT NULL,
  pallet_count integer NOT NULL,
  rate_per_pallet numeric NOT NULL DEFAULT 1.50,
  total_pay numeric NOT NULL,
  created_at timestamp with time zone NOT NULL DEFAULT now(),
  updated_at timestamp with time zone NOT NULL DEFAULT now()
);

-- Add Row Level Security (RLS) policies
ALTER TABLE public.hourly_rate_records ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.strapping_records ENABLE ROW LEVEL SECURITY;

-- Create policies for hourly_rate_records
CREATE POLICY "Allow public access to hourly_rate_records" 
  ON public.hourly_rate_records 
  FOR ALL 
  USING (true)
  WITH CHECK (true);

-- Create policies for strapping_records
CREATE POLICY "Allow public access to strapping_records" 
  ON public.strapping_records 
  FOR ALL 
  USING (true)
  WITH CHECK (true);

-- Add triggers for updated_at columns
CREATE TRIGGER update_hourly_rate_records_updated_at
  BEFORE UPDATE ON public.hourly_rate_records
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_strapping_records_updated_at
  BEFORE UPDATE ON public.strapping_records
  FOR EACH ROW
  EXECUTE FUNCTION public.update_updated_at_column();
