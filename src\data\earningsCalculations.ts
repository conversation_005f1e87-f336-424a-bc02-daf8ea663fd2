
import { supabase } from '@/integrations/supabase/client';

export interface PendingEarnings {
  employeeId: number;
  employeeName: string;
  total: number;
  breakdown: Array<{
    date: string;
    type: string;
    details: string;
    amount: number;
    sourceId: string;
  }>;
  lastPaymentDate?: string;
}

export interface GrossEarnings {
  employeeId: number;
  employeeName: string;
  settingTotal: number;
  dehackingTotal: number;
  hourlyRateTotal: number;
  strappingTotal: number;
  total: number;
}

export const calculateGrossEarningsForPeriod = async ({ from, to }: { from: string, to: string }): Promise<GrossEarnings[]> => {
  try {
    // Get all employees
    const { data: employees, error: employeesError } = await supabase
      .from('employees')
      .select('id, name')
      .eq('status', 'Active');

    if (employeesError) {
      console.error('Error fetching employees:', employeesError);
      return [];
    }

    // Get hourly rate records for the period
    const { data: hourlyRateRecords } = await supabase
      .from('hourly_rate_records')
      .select('employee_id, total_pay, date')
      .gte('date', from)
      .lte('date', to);

    // Get strapping records for the period
    const { data: strappingRecords } = await supabase
      .from('strapping_records')
      .select('employee_id, total_pay, date')
      .gte('date', from)
      .lte('date', to);

    const grossEarnings: GrossEarnings[] = [];

    for (const employee of employees || []) {
      const hourlyRateTotal = hourlyRateRecords
        ?.filter(record => record.employee_id === employee.id)
        .reduce((sum, record) => sum + (record.total_pay || 0), 0) || 0;

      const strappingTotal = strappingRecords
        ?.filter(record => record.employee_id === employee.id)
        .reduce((sum, record) => sum + (record.total_pay || 0), 0) || 0;

      const total = hourlyRateTotal + strappingTotal;

      if (total > 0) {
        grossEarnings.push({
          employeeId: employee.id,
          employeeName: employee.name,
          settingTotal: 0, // Legacy field
          dehackingTotal: 0, // Legacy field
          hourlyRateTotal,
          strappingTotal,
          total,
        });
      }
    }

    return grossEarnings.sort((a, b) => b.total - a.total);
  } catch (error) {
    console.error('Error calculating gross earnings:', error);
    return [];
  }
};

export const calculateAllPendingEarnings = async (): Promise<PendingEarnings[]> => {
  try {
    // Get all employees
    const { data: employees, error: employeesError } = await supabase
      .from('employees')
      .select('id, name')
      .eq('status', 'Active');

    if (employeesError) {
      console.error('Error fetching employees:', employeesError);
      return [];
    }

    // Get all payments to determine last payment dates
    const { data: payments } = await supabase
      .from('payments')
      .select('employee_id, payment_date')
      .eq('status', 'Paid')
      .order('payment_date', { ascending: false });

    // Get hourly rate records
    const { data: hourlyRateRecords } = await supabase
      .from('hourly_rate_records')
      .select('employee_id, total_pay, date, hours_worked, hourly_rate');

    // Get strapping records
    const { data: strappingRecords } = await supabase
      .from('strapping_records')
      .select('employee_id, total_pay, date, pallet_count, rate_per_pallet');

    const pendingEarnings: PendingEarnings[] = [];

    for (const employee of employees || []) {
      const lastPayment = payments?.find(p => p.employee_id === employee.id);
      const lastPaymentDate = lastPayment?.payment_date;

      const breakdown: Array<{
        date: string;
        type: string;
        details: string;
        amount: number;
        sourceId: string;
      }> = [];

      // Filter and process hourly rate records
      const relevantHourlyRateRecords = hourlyRateRecords
        ?.filter(record => {
          if (record.employee_id !== employee.id) return false;
          if (lastPaymentDate && record.date <= lastPaymentDate) return false;
          return true;
        }) || [];

      relevantHourlyRateRecords.forEach(record => {
        breakdown.push({
          date: record.date,
          type: 'hourlyRate',
          details: `${record.hours_worked} hours @ R${record.hourly_rate}/hour`,
          amount: record.total_pay || 0,
          sourceId: `hourly-${record.employee_id}-${record.date}`,
        });
      });

      // Filter and process strapping records
      const relevantStrappingRecords = strappingRecords
        ?.filter(record => {
          if (record.employee_id !== employee.id) return false;
          if (lastPaymentDate && record.date <= lastPaymentDate) return false;
          return true;
        }) || [];

      relevantStrappingRecords.forEach(record => {
        breakdown.push({
          date: record.date,
          type: 'strapping',
          details: `${record.pallet_count} pallets @ R${record.rate_per_pallet}/pallet`,
          amount: record.total_pay || 0,
          sourceId: `strapping-${record.employee_id}-${record.date}`,
        });
      });

      const total = breakdown.reduce((sum, item) => sum + item.amount, 0);

      if (total > 0) {
        pendingEarnings.push({
          employeeId: employee.id,
          employeeName: employee.name,
          total,
          breakdown: breakdown.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime()),
          lastPaymentDate,
        });
      }
    }

    return pendingEarnings.sort((a, b) => b.total - a.total);
  } catch (error) {
    console.error('Error calculating pending earnings:', error);
    return [];
  }
};
