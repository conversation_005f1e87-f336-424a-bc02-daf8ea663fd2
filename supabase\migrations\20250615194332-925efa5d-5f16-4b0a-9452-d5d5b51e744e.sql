
-- Drop existing restrictive policies on the teams table if they exist
DROP POLICY IF EXISTS "Allow authenticated users to read teams" ON public.teams;
DROP POLICY IF EXISTS "Allow public read access to teams" ON public.teams;

-- Create a new policy to allow all users (including anonymous) to read from the teams table.
CREATE POLICY "Allow public read access to teams"
ON public.teams
FOR SELECT
USING (true);

-- Drop existing restrictive policies on the fires table if they exist
DROP POLICY IF EXISTS "Allow authenticated users to read fires" ON public.fires;
DROP POLICY IF EXISTS "Allow public read access to fires" ON public.fires;

-- Create a new policy to allow all users (including anonymous) to read from the fires table.
CREATE POLICY "Allow public read access to fires"
ON public.fires
FOR SELECT
USING (true);
