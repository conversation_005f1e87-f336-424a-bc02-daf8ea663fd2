
import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>alog<PERSON>eader, DialogTitle, DialogDescription, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { logFuelTransaction } from "@/data/fuelTransactionsStore";
import { calculateLitresPerHour, calculateKmPerLitre } from "@/utils/fuelCalculations";
import { calculateAutomaticFuelUsage } from "@/utils/fuelUsageCalculations";
import { <PERSON>rollArea } from "@/components/ui/scroll-area";

interface RecordFuelDispensingDialogProps {
  isOpen: boolean;
  onClose: () => void;
  bunkers: { id: string; name: string }[];
  assets: { id: string; name: string }[];
}

// Asset type detection for demo - in real use, use asset metadata!
function detectAssetType(assetName: string) {
  const lower = assetName.toLowerCase();
  if (lower.includes("kiln") || lower.includes("generator") || lower.includes("forklift")) return "machinery";
  if (lower.includes("truck") || lower.includes("vehicle") || lower.includes("car")) return "vehicle";
  return "machinery"; // fallback
}

export const RecordFuelDispensingDialog = ({
  isOpen,
  onClose,
  bunkers,
  assets,
}: RecordFuelDispensingDialogProps) => {
  const [selectedBunker, setSelectedBunker] = useState<string>("");
  const [selectedAsset, setSelectedAsset] = useState<string>("");
  const [currentReading, setCurrentReading] = useState(""); // Current reading only
  const [litresFilled, setLitresFilled] = useState("");
  const [date, setDate] = useState<Date | undefined>(new Date());

  // Automatic calculation state
  const [usageCalculation, setUsageCalculation] = useState<{
    lastReading: number | null;
    usage: { usage: number; unit: string } | null;
    readingType: "hourly" | "km";
    assetInfo: { name: string; type: string } | null;
  } | null>(null);
  const [calculationLoading, setCalculationLoading] = useState(false);

  // Function to calculate usage automatically
  const handleCalculateUsage = async () => {
    if (!selectedAsset || !currentReading || !litresFilled) {
      setUsageCalculation(null);
      return;
    }

    setCalculationLoading(true);
    try {
      const result = await calculateAutomaticFuelUsage(
        selectedAsset,
        Number(currentReading),
        Number(litresFilled)
      );
      setUsageCalculation(result);
    } catch (error) {
      console.error('Error calculating usage:', error);
      setUsageCalculation(null);
    }
    setCalculationLoading(false);
  };

  // Effect to trigger calculation when relevant fields change
  useEffect(() => {
    if (selectedAsset && currentReading && litresFilled) {
      handleCalculateUsage();
    } else {
      setUsageCalculation(null);
    }
  }, [selectedAsset, currentReading, litresFilled]);

  const handleSubmit = () => {
    logFuelTransaction({
      id: Date.now().toString(),
      type: "dispensing",
      date: date || new Date(),
      bunkerId: selectedBunker,
      assetId: selectedAsset,
      assetType: usageCalculation?.readingType === "km" ? "vehicle" : "machinery",
      startReading: Number(currentReading), // Store current reading
      endReading: undefined, // No longer used
      litresFilled: Number(litresFilled),
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg max-h-[90vh] flex flex-col">
        <DialogHeader className="flex-shrink-0">
          <DialogTitle>Record Fuel Transaction</DialogTitle>
          <DialogDescription>Enter fuel dispensing details</DialogDescription>
        </DialogHeader>
        <ScrollArea className="flex-1 pr-4">
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="fuelBunker">Fuel Bunker</Label>
                <Select value={selectedBunker} onValueChange={setSelectedBunker}>
                  <SelectTrigger id="fuelBunker">
                    <SelectValue placeholder="Select bunker" />
                  </SelectTrigger>
                  <SelectContent>
                    {bunkers.map((bunker) => (
                      <SelectItem key={bunker.id} value={bunker.id}>
                        {bunker.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div>
                <Label htmlFor="asset">Asset</Label>
                <Select value={selectedAsset} onValueChange={setSelectedAsset}>
                  <SelectTrigger id="asset">
                    <SelectValue placeholder="Select asset" />
                  </SelectTrigger>
                  <SelectContent>
                    {assets.map((asset) => (
                      <SelectItem key={asset.id} value={asset.id}>
                        {asset.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
            {/* Current Reading Field */}
            {selectedAsset && (
              <div>
                <Label htmlFor="currentReading">
                  Current Reading {usageCalculation?.readingType === "km" ? "(KM)" : "(Hours)"}
                </Label>
                <Input
                  id="currentReading"
                  type="number"
                  value={currentReading}
                  onChange={e => setCurrentReading(e.target.value)}
                  placeholder={usageCalculation?.readingType === "km" ? "e.g., 32400" : "e.g., 1248.5"}
                />
              </div>
            )}
            <div>
              <Label htmlFor="litresFilled">Amount of Litres Filled</Label>
              <Input
                id="litresFilled"
                type="number"
                value={litresFilled}
                onChange={e => setLitresFilled(e.target.value)}
                placeholder="e.g., 45"
              />
            </div>
            <div>
              <Label htmlFor="date">Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !date && "text-muted-foreground"
                    )}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {date ? format(date, "yyyy/MM/dd") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0 pointer-events-auto" align="start">
                  <Calendar
                    mode="single"
                    selected={date}
                    onSelect={setDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
            {/* Usage Calculation Display */}
            {(usageCalculation || calculationLoading) && (
              <div className="p-3 bg-slate-50 rounded-lg border">
                <h4 className="text-sm font-medium text-slate-700 mb-2">Automatic Usage Calculation</h4>
                {calculationLoading ? (
                  <p className="text-sm text-slate-600">Calculating...</p>
                ) : usageCalculation ? (
                  <div className="space-y-1 text-sm">
                    {usageCalculation.lastReading !== null ? (
                      <>
                        <p className="text-slate-600">
                          Last Reading: <span className="font-medium">{usageCalculation.lastReading.toLocaleString()}</span>
                        </p>
                        <p className="text-slate-600">
                          Current Reading: <span className="font-medium">{Number(currentReading).toLocaleString()}</span>
                        </p>
                        {usageCalculation.usage ? (
                          <p className="text-green-700 font-medium">
                            Usage: {usageCalculation.usage.usage.toFixed(2)} {usageCalculation.usage.unit}
                          </p>
                        ) : (
                          <p className="text-amber-600">Unable to calculate usage (invalid readings)</p>
                        )}
                      </>
                    ) : (
                      <p className="text-slate-600">No previous reading found for this asset</p>
                    )}
                  </div>
                ) : null}
              </div>
            )}
          </div>
        </ScrollArea>
        <DialogFooter className="flex-shrink-0">
          <Button variant="outline" onClick={onClose}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={
            !selectedBunker ||
            !selectedAsset ||
            !currentReading ||
            !litresFilled
          }>Record Transaction</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
