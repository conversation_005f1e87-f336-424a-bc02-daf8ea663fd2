
import React, { createContext, useContext } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import type { UserRole } from '@/hooks/useUsers';

interface UserContextType {
  currentUser: any;
  setCurrentUser: (user: any) => void;
  userRole: UserRole | null;
  hasAccess: (requiredRoles: UserRole[]) => boolean;
  canAccessMenuItem: (menuItem: string) => boolean;
  canAccessDashboardCard: (cardType: string) => boolean;
  canAccessReportType: (reportType: string) => boolean;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

export const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const { currentUser } = useAuth();

  const userRole = currentUser?.role || null;

  const hasAccess = (requiredRoles: UserRole[]): boolean => {
    if (!userRole) return false;
    return requiredRoles.includes(userRole);
  };

  const canAccessMenuItem = (menuItem: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return ![
          'settings',
          'dehacking',
          'brick-types',
          'carbon-spiral-tracker',
          'breakdowns'
        ].includes(menuItem);

      case 'factory_supervisor':
        return ['dashboard', 'people-management', 'production-tracking', 'carbon-spiral-tracker', 'breakdowns', 'asset-management'].includes(menuItem);

      case 'yard_supervisor':
        return ['dashboard', 'people-management', 'production-tracking', 'kilns', 'breakdowns', 'load-planning', 'stock-yard'].includes(menuItem);

      case 'office_admin':
        return ![
          'financial-management',
          'settings',
          'dehacking',
          'brick-types',
          'carbon-spiral-tracker',
          'breakdowns'
        ].includes(menuItem);

      case 'manager':
        return ![
          'settings'
        ].includes(menuItem);

      case 'admin':
        return true;

      default:
        return false;
    }
  };

  const canAccessDashboardCard = (cardType: string): boolean => {
    if (!userRole) return false;

    switch (userRole) {
      case 'finance':
        return false;
      
      case 'factory_supervisor':
        return ['factory-output', 'fuel-management'].includes(cardType);
      
      case 'yard_supervisor':
        return ['setting-teams', 'dehacking', 'fuel-management', 'pallet-tracking', 'load-planning', 'stock-movement'].includes(cardType);
      
      case 'office_admin':
        return !['employee-earnings', 'financial-stats'].includes(cardType);
      
      case 'manager':
        return true;
      
      case 'admin':
        return true;
      
      default:
        return false;
    }
  };

  const canAccessReportType = (reportType: string): boolean => {
    if (!userRole) return false;

    if (reportType === 'user-activity') {
      return ['manager', 'admin'].includes(userRole);
    }

    switch (userRole) {
      case 'finance':
        return ['employee-hours'].includes(reportType);
      
      case 'factory_supervisor':
        return ['factory-output', 'setting-teams', 'fuel-management'].includes(reportType);
      
      case 'yard_supervisor':
        return ['factory-output', 'setting-teams', 'dehacking'].includes(reportType);
      
      case 'office_admin':
        return !['employee-earnings', 'financial-reports'].includes(reportType);
      
      case 'manager':
        return true;
      
      case 'admin':
        return true;
      
      default:
        return false;
    }
  };

  const setCurrentUser = () => {
    console.warn('setCurrentUser is deprecated. Use AuthContext for authentication.');
  };

  const contextValue: UserContextType = {
    currentUser,
    setCurrentUser,
    userRole,
    hasAccess,
    canAccessMenuItem,
    canAccessDashboardCard,
    canAccessReportType,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export const useUser = (): UserContextType => {
  const context = useContext(UserContext);
  if (context === undefined) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};
