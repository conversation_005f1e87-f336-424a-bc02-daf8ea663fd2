
import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { format, eachDayOfInterval, startOfWeek, endOfWeek } from "date-fns";
import { CheckCircle, Star, Edit } from "lucide-react";
import { useLoadPlanning } from "@/hooks/useLoadPlanning";
import { EditLoadDialog } from "./EditLoadDialog";
import { LoadPlanningEntry } from "@/types/loadPlanning";

export const WeeklyOverview = () => {
  const { loads, isLoading, markAsDispached, markAsReady, isMarkingDispatched, isMarkingReady } = useLoadPlanning();

  // Get current week
  const now = new Date();
  const weekStart = startOfWeek(now, { weekStartsOn: 1 }); // Monday
  const weekEnd = endOfWeek(now, { weekStartsOn: 1 }); // Sunday
  const [editingLoad, setEditingLoad] = useState<LoadPlanningEntry | null>(null);
  const weekDays = eachDayOfInterval({ start: weekStart, end: weekEnd });

  const getLoadsForDay = (day: Date) => {
    const dayLoads = loads.filter(load => 
      format(new Date(load.date), 'yyyy-MM-dd') === format(day, 'yyyy-MM-dd')
    );
    // Sort by rank (1 = highest priority first)
    return dayLoads.sort((a, b) => (a.rank || 1) - (b.rank || 1));
  };

  const getTotalBricksForWeek = () => {
    const weekLoads = loads.filter(load => {
      const loadDate = new Date(load.date);
      return loadDate >= weekStart && loadDate <= weekEnd;
    });
    return weekLoads.reduce((total, load) => total + load.brick_count, 0);
  };

  const handleStatusChange = (loadId: string, status: string) => {
    if (status === "ready") {
      markAsReady(loadId);
    } else if (status === "dispatched") {
      markAsDispached(loadId);
    }
  };

  const getLoadStatus = (load: LoadPlanningEntry) => {
    if (load.dispatched) return "dispatched";
    if (load.ready) return "ready";
    return "pending";
  };

  const getPriorityColor = (load: LoadPlanningEntry) => {
    if (load.dispatched) return "bg-green-50 border-green-200";
    if (load.ready) return "bg-blue-50 border-blue-200";
    if (!load.rank) return "bg-slate-100";
    if (load.rank <= 5) return "bg-red-50 border-red-200";
    if (load.rank <= 10) return "bg-orange-50 border-orange-200";
    if (load.rank <= 15) return "bg-yellow-50 border-yellow-200";
    return "bg-green-50 border-green-200";
  };

  const getPriorityBadgeColor = (rank?: number) => {
    if (!rank) return "secondary";
    if (rank <= 5) return "destructive";
    if (rank <= 10) return "default";
    return "secondary";
  };

  const getStatusText = (load: LoadPlanningEntry) => {
    if (load.dispatched) return "Dispatched";
    if (load.ready) return "Ready";
    return "Pending";
  };

  return (
    <>
      <div className="space-y-6">
        {/* Week Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              <span>Week of {format(weekStart, 'MMMM dd')} - {format(weekEnd, 'MMMM dd, yyyy')}</span>
              <Badge variant="outline" className="text-lg px-3 py-1">
                {getTotalBricksForWeek().toLocaleString()} total bricks
              </Badge>
            </CardTitle>
          </CardHeader>
        </Card>

        {/* Daily Breakdown */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {weekDays.map((day) => {
            const dayLoads = getLoadsForDay(day);
            const dayBricks = dayLoads.reduce((total, load) => total + load.brick_count, 0);
            const dispatchedCount = dayLoads.filter(load => load.dispatched).length;
            const readyCount = dayLoads.filter(load => load.ready && !load.dispatched).length;
            
            return (
              <Card key={day.toISOString()} className="h-fit">
                <CardHeader className="pb-3">
                  <CardTitle className="text-base flex justify-between items-center">
                    <span>{format(day, 'EEE, MMM dd')}</span>
                    <div className="flex gap-1">
                      <Badge variant={dayLoads.length > 0 ? "default" : "secondary"}>
                        {dayLoads.length} loads
                      </Badge>
                      {readyCount > 0 && (
                        <Badge variant="outline" className="text-blue-600 border-blue-600">
                          {readyCount} ready
                        </Badge>
                      )}
                      {dispatchedCount > 0 && (
                        <Badge variant="outline" className="text-green-600 border-green-600">
                          {dispatchedCount} dispatched
                        </Badge>
                      )}
                    </div>
                  </CardTitle>
                  {dayBricks > 0 && (
                    <p className="text-sm text-slate-600">{dayBricks.toLocaleString()} bricks</p>
                  )}
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="space-y-2 max-h-60 overflow-y-auto">
                    {dayLoads.length > 0 ? (
                      dayLoads.map((load) => {
                        const status = getLoadStatus(load);
                        
                        return (
                          <div key={load.id} className={`p-3 rounded text-sm border ${getPriorityColor(load)}`}>
                            <div className="flex items-center justify-between mb-2">
                              <div className="font-medium text-slate-900">{load.client_name}</div>
                              <div className="flex items-center gap-1">
                                {load.rank && (
                                  <Badge variant={getPriorityBadgeColor(load.rank)} className="text-xs">
                                    <Star className="h-3 w-3 mr-1" />
                                    {load.rank}
                                  </Badge>
                                )}
                                {load.dispatched && <CheckCircle className="h-4 w-4 text-green-600" />}
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => setEditingLoad(load)}
                                  className="h-6 w-6 p-0"
                                >
                                  <Edit className="h-3 w-3" />
                                </Button>
                              </div>
                            </div>
                            <div className="text-slate-600 mb-2">{load.transporter}</div>
                            <div className="text-xs text-slate-500 mb-2">
                              Status: {getStatusText(load)}
                            </div>
                            <div className="flex items-center justify-between">
                              {load.load_description && (
                                <Badge variant="outline" className="text-xs">
                                  {load.load_description}
                                </Badge>
                              )}
                              <div className="flex gap-1">
                                {status === "pending" && (
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleStatusChange(load.id, "ready")}
                                    disabled={isMarkingReady}
                                    className="text-xs px-2 py-1 h-6"
                                  >
                                    Ready
                                  </Button>
                                )}
                                {status === "ready" && (
                                  <Button
                                    variant="default"
                                    size="sm"
                                    onClick={() => handleStatusChange(load.id, "dispatched")}
                                    disabled={isMarkingDispatched}
                                    className="text-xs px-2 py-1 h-6"
                                  >
                                    Dispatch
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        );
                      })
                    ) : (
                      <p className="text-slate-500 text-sm">No loads scheduled</p>
                    )}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>

      {editingLoad && (
        <EditLoadDialog
          load={editingLoad}
          open={!!editingLoad}
          onOpenChange={(open) => !open && setEditingLoad(null)}
        />
      )}
    </>
  );
};
