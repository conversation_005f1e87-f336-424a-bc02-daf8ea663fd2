
import { Card, CardContent } from "@/components/ui/card";
import { Employee } from "@/data/employeeData";
import { PaymentWithEmployeeName } from "@/data/paymentsData";

interface PaymentsStatsCardsProps {
    payments: PaymentWithEmployeeName[];
    employees: Employee[];
}

export const PaymentsStatsCards = ({ payments, employees }: PaymentsStatsCardsProps) => {
    return (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
                <CardContent className="p-6">
                    <div className="text-2xl font-bold text-slate-800">{payments.length}</div>
                    <p className="text-slate-600">Total Transactions</p>
                </CardContent>
            </Card>
            <Card>
                <CardContent className="p-6">
                    <div className="text-2xl font-bold text-blue-600">{employees.length}</div>
                    <p className="text-slate-600">Employees</p>
                    {/* TODO: Show supervisor bonus calculation here once implemented */}
                </CardContent>
            </Card>
        </div>
    );
};
