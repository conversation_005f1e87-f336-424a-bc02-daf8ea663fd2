
import { Card, CardContent } from "@/components/ui/card";

interface DashboardStatsProps {
  stats: {
    total: number;
    inTransit: number;
    delivered: number;
    pendingReturn: number;
  };
}

export const DashboardStats = ({ stats }: DashboardStatsProps) => (
  <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
    <Card>
      <CardContent className="p-6">
        <div className="text-2xl font-bold text-slate-800">{stats.total}</div>
        <p className="text-slate-600">Total Pallets</p>
      </CardContent>
    </Card>
    <Card>
      <CardContent className="p-6">
        <div className="text-2xl font-bold text-blue-600">{stats.inTransit}</div>
        <p className="text-slate-600">In Transit</p>
      </CardContent>
    </Card>
    <Card>
      <CardContent className="p-6">
        <div className="text-2xl font-bold text-green-600">{stats.delivered}</div>
        <p className="text-slate-600">Delivered</p>
      </CardContent>
    </Card>
    <Card>
      <CardContent className="p-6">
        <div className="text-2xl font-bold text-yellow-600">{stats.pendingReturn}</div>
        <p className="text-slate-600">Pending Return</p>
      </CardContent>
    </Card>
  </div>
);
