export type ViewMode = 'daily' | 'weekly' | 'monthly';

export interface DateNavigationState {
  currentDate: Date;
  viewMode: ViewMode;
}

export interface LoadPlanningEntry {
  id: string;
  date: string;
  client_name: string;
  load_description?: string;
  transporter: string;
  load_type: "Strapped" | "Pallets";
  brick_count: number;
  brick_type_id?: string;
  rank?: number;
  created_at: string;
  updated_at: string;
  user_id: string;
  dispatched: boolean;
  dispatched_at?: string;
  dispatched_by?: string;
  ready: boolean;
  management_brick_types?: {
    name: string;
    category: string;
  };
}
