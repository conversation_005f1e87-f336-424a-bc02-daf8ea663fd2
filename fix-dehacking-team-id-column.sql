-- Fix for dehacking_entries team_id column issue
-- This script ensures the team_id column exists and refreshes the schema cache

-- First, check if the column exists and add it if it doesn't
DO $$
BEGIN
    -- Check if team_id column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'dehacking_entries' 
        AND column_name = 'team_id'
        AND table_schema = 'public'
    ) THEN
        -- Add the team_id column if it doesn't exist
        ALTER TABLE public.dehacking_entries 
        ADD COLUMN team_id TEXT REFERENCES public.teams(id);
        
        -- Add comment to document the purpose
        COMMENT ON COLUMN public.dehacking_entries.team_id IS 'References the team assigned to this dehacking entry';
        
        RAISE NOTICE 'Added team_id column to dehacking_entries table';
    ELSE
        RAISE NOTICE 'team_id column already exists in dehacking_entries table';
    END IF;
END $$;

-- Refresh the schema cache by updating table statistics
ANALYZE public.dehacking_entries;

-- Verify the column exists
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'dehacking_entries' 
AND table_schema = 'public'
ORDER BY ordinal_position;
