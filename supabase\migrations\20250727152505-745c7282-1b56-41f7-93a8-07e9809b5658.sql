
-- Fix critical security issues

-- 1. Create proper user profiles table linked to <PERSON>pa<PERSON> Auth
CREATE TABLE IF NOT EXISTS public.profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  username TEXT UNIQUE,
  full_name TEXT,
  email TEXT,
  role user_role NOT NULL DEFAULT 'admin',
  active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS on profiles
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- 2. Add RLS policies to tables that are missing them
ALTER TABLE public.fuel_dispensing_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.employee_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.forklift_allocations ENABLE ROW LEVEL SECURITY;

-- 3. Create security definer functions to avoid RLS recursion
CREATE OR REPLACE FUNCTION public.get_current_user_role()
RETURNS user_role
LANGUAGE SQL
SECURITY DEFINER
STABLE
SET search_path = public
AS $$
  SELECT role FROM public.profiles WHERE id = auth.uid();
$$;

CREATE OR REPLACE FUNCTION public.is_authenticated_user()
RETURNS BOOLEAN
LANGUAGE SQL
SECURITY DEFINER
STABLE
SET search_path = public
AS $$
  SELECT auth.uid() IS NOT NULL;
$$;

-- 4. Fix RLS policies to use proper authentication
DROP POLICY IF EXISTS "Users can insert carbon tests" ON public.carbon_tests;
CREATE POLICY "Authenticated users can insert carbon tests"
ON public.carbon_tests
FOR INSERT
TO authenticated
WITH CHECK (is_authenticated_user());

DROP POLICY IF EXISTS "Users can insert spiral loads" ON public.spiral_loads;
CREATE POLICY "Authenticated users can insert spiral loads"
ON public.spiral_loads
FOR INSERT
TO authenticated
WITH CHECK (is_authenticated_user());

DROP POLICY IF EXISTS "Users can insert breakdowns" ON public.breakdowns;
CREATE POLICY "Authenticated users can insert breakdowns"
ON public.breakdowns
FOR INSERT
TO authenticated
WITH CHECK (is_authenticated_user());

-- 5. Add RLS policies for missing tables
CREATE POLICY "Authenticated users can view fuel dispensing transactions"
ON public.fuel_dispensing_transactions
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Authenticated users can insert fuel dispensing transactions"
ON public.fuel_dispensing_transactions
FOR INSERT
TO authenticated
WITH CHECK (is_authenticated_user());

CREATE POLICY "Authenticated users can update fuel dispensing transactions"
ON public.fuel_dispensing_transactions
FOR UPDATE
TO authenticated
USING (is_authenticated_user());

-- Employee roles policies
CREATE POLICY "Authenticated users can view employee roles"
ON public.employee_roles
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Admins can manage employee roles"
ON public.employee_roles
FOR ALL
TO authenticated
USING (get_current_user_role() = 'admin');

-- Forklift allocations policies
CREATE POLICY "Authenticated users can view forklift allocations"
ON public.forklift_allocations
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Supervisors can manage forklift allocations"
ON public.forklift_allocations
FOR ALL
TO authenticated
USING (get_current_user_role() IN ('admin', 'manager', 'factory_supervisor', 'yard_supervisor'));

-- 6. Add profiles RLS policies
CREATE POLICY "Users can view their own profile"
ON public.profiles
FOR SELECT
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Users can update their own profile"
ON public.profiles
FOR UPDATE
TO authenticated
USING (auth.uid() = id);

CREATE POLICY "Admins can view all profiles"
ON public.profiles
FOR SELECT
TO authenticated
USING (get_current_user_role() = 'admin');

CREATE POLICY "Admins can manage all profiles"
ON public.profiles
FOR ALL
TO authenticated
USING (get_current_user_role() = 'admin');

-- 7. Create trigger to automatically create profile on user signup
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
  INSERT INTO public.profiles (id, username, full_name, email, role)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'username', NEW.email),
    COALESCE(NEW.raw_user_meta_data->>'full_name', ''),
    NEW.email,
    COALESCE((NEW.raw_user_meta_data->>'role')::user_role, 'admin')
  );
  RETURN NEW;
END;
$$;

-- Create the trigger
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 8. Update system_config policies to use proper auth
DROP POLICY IF EXISTS "Administrators can manage system config" ON public.system_config;
DROP POLICY IF EXISTS "All users can view system config" ON public.system_config;

CREATE POLICY "Authenticated users can view system config"
ON public.system_config
FOR SELECT
TO authenticated
USING (true);

CREATE POLICY "Admins can manage system config"
ON public.system_config
FOR ALL
TO authenticated
USING (get_current_user_role() = 'admin');

-- 9. Update users table policies
DROP POLICY IF EXISTS "Administrators can manage all users" ON public.users;
DROP POLICY IF EXISTS "Administrators can view all users" ON public.users;
DROP POLICY IF EXISTS "Users can update their own profile" ON public.users;
DROP POLICY IF EXISTS "Users can view their own profile" ON public.users;

-- Users table is now deprecated in favor of profiles table
CREATE POLICY "Admins can view legacy users table"
ON public.users
FOR SELECT
TO authenticated
USING (get_current_user_role() = 'admin');
