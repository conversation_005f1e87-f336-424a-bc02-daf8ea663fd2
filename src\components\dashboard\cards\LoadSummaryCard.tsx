
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Truck, Calendar, Package } from "lucide-react";
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { useLoadPlanning } from "@/hooks/useLoadPlanning";
import { TimeRange } from "@/components/dashboard/DashboardContent";

interface LoadSummaryCardProps {
  timeRange: TimeRange;
}

export const LoadSummaryCard = ({ timeRange }: LoadSummaryCardProps) => {
  const { loads, isLoading } = useLoadPlanning();
  const today = new Date();

  // Get date range based on timeRange
  const getDateRange = () => {
    switch (timeRange) {
      case 'today':
        return { start: startOfDay(today), end: endOfDay(today) };
      case 'week':
        return { start: startOfWeek(today, { weekStartsOn: 1 }), end: endOfWeek(today, { weekStartsOn: 1 }) };
      case 'month':
        return { start: startOfMonth(today), end: endOfMonth(today) };
      case 'year':
        return { start: new Date(today.getFullYear(), 0, 1), end: new Date(today.getFullYear(), 11, 31) };
      default:
        return { start: startOfDay(today), end: endOfDay(today) };
    }
  };

  const { start, end } = getDateRange();

  // Filter loads for the selected period
  const periodLoads = loads.filter(load => {
    const loadDate = new Date(load.date);
    return loadDate >= start && loadDate <= end;
  });

  // Calculate statistics
  const totalLoads = periodLoads.length;
  const dispatchedLoads = periodLoads.filter(load => load.dispatched).length;
  const readyLoads = periodLoads.filter(load => load.ready && !load.dispatched).length;
  
  // Calculate total bricks including dispatched bricks
  const totalBricks = periodLoads.reduce((sum, load) => sum + (load.brick_count || 0), 0);
  const dispatchedBricks = periodLoads.filter(load => load.dispatched).reduce((sum, load) => sum + (load.brick_count || 0), 0);
  
  // Calculate planned bricks by type (Imperial vs Maxi)
  const plannedImperialBricks = periodLoads.reduce((sum, load) => {
    if (load.management_brick_types?.category?.toLowerCase().includes('imperial')) {
      return sum + (load.brick_count || 0);
    }
    return sum;
  }, 0);
  
  const plannedMaxiBricks = periodLoads.reduce((sum, load) => {
    if (load.management_brick_types?.category?.toLowerCase().includes('maxi')) {
      return sum + (load.brick_count || 0);
    }
    return sum;
  }, 0);

  // Get top 5 loads for display
  const displayLoads = periodLoads
    .sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime())
    .slice(0, 5);

  const getStatusBadge = (load: any) => {
    if (load.dispatched) return <Badge variant="default" className="bg-green-100 text-green-800">Dispatched</Badge>;
    if (load.ready) return <Badge variant="default" className="bg-blue-100 text-blue-800">Ready</Badge>;
    return <Badge variant="secondary">Pending</Badge>;
  };

  const getTimeRangeLabel = () => {
    switch (timeRange) {
      case 'today': return 'Today';
      case 'week': return 'This Week';
      case 'month': return 'This Month';
      case 'year': return 'This Year';
      default: return 'Today';
    }
  };

  if (isLoading) {
    return (
      <Card className="h-full">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Load Summary ({getTimeRangeLabel()})</CardTitle>
          <Truck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-3">
            <div className="grid grid-cols-3 gap-4">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="text-center">
                  <div className="h-6 bg-slate-200 rounded w-full mb-1"></div>
                  <div className="h-3 bg-slate-200 rounded w-3/4 mx-auto"></div>
                </div>
              ))}
            </div>
            <div className="space-y-2">
              {[...Array(3)].map((_, i) => (
                <div key={i} className="h-8 bg-slate-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="h-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Load Summary ({getTimeRangeLabel()})</CardTitle>
        <Truck className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {/* Summary Stats */}
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-bold text-slate-800">{totalLoads}</div>
              <div className="text-xs text-muted-foreground">Total Loads</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-green-600">{dispatchedLoads}</div>
              <div className="text-xs text-muted-foreground">Dispatched</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{readyLoads}</div>
              <div className="text-xs text-muted-foreground">Ready</div>
            </div>
          </div>

          {/* Brick Statistics */}
          <div className="space-y-3 border-t pt-3">
            <div className="text-center">
              <div className="text-lg font-bold text-slate-800">{totalBricks.toLocaleString()}</div>
              <div className="text-xs text-muted-foreground">Total Bricks</div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-sm font-bold text-green-600">{dispatchedBricks.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Dispatched Bricks</div>
              </div>
              <div className="text-center">
                <div className="text-sm font-bold text-blue-600">{(totalBricks - dispatchedBricks).toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Pending Bricks</div>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center">
                <div className="text-sm font-bold text-purple-600">{plannedImperialBricks.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Imperial Bricks</div>
              </div>
              <div className="text-center">
                <div className="text-sm font-bold text-orange-600">{plannedMaxiBricks.toLocaleString()}</div>
                <div className="text-xs text-muted-foreground">Maxi Bricks</div>
              </div>
            </div>
          </div>

          {/* Recent Loads Table */}
          {displayLoads.length > 0 && (
            <div className="space-y-2">
              <div className="text-xs font-medium text-muted-foreground">Recent Loads:</div>
              <div className="max-h-48 overflow-y-auto">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead className="text-xs">Client</TableHead>
                      <TableHead className="text-xs">Date</TableHead>
                      <TableHead className="text-xs">Status</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {displayLoads.map((load) => (
                      <TableRow key={load.id}>
                        <TableCell className="text-xs font-medium">{load.client_name}</TableCell>
                        <TableCell className="text-xs">{format(new Date(load.date), 'MMM dd')}</TableCell>
                        <TableCell className="text-xs">{getStatusBadge(load)}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}

          {displayLoads.length === 0 && (
            <div className="text-center py-4 text-muted-foreground">
              <Package className="h-8 w-8 mx-auto mb-2 text-slate-300" />
              <p className="text-xs">No loads for {getTimeRangeLabel().toLowerCase()}</p>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
