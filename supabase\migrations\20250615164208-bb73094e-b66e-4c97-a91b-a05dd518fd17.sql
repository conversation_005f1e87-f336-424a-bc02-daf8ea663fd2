
-- Drop existing restrictive policies on the employees table
DROP POLICY IF EXISTS "Allow authenticated users full access on employees" ON public.employees;
DROP POLICY IF EXISTS "Allow public read access to employees" ON public.employees;

-- Create a new policy to allow all users (including anonymous) to perform all actions.
CREATE POLICY "Allow public access to employees"
ON public.employees
FOR ALL
USING (true)
WITH CHECK (true);
