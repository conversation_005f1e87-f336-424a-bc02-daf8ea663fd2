
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase, supabaseAdmin } from "@/integrations/supabase/client";
import { useAuth } from "@/contexts/AuthContext";

export interface FinishedProductCount {
  id: number;
  created_at: string;
  date: string;
  pallet_count: number;
  product_type: string;
  user_id: string;
  notes?: string;
}

export interface NewFinishedProductCount {
  date: string;
  pallet_count: number;
  product_type: string;
  notes?: string;
}

export function useFinishedProductCounts() {
  return useQuery({
    queryKey: ["finishedProductCounts"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("finished_product_counts")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) {
        console.error("Error fetching finished product counts:", error.message);
        throw new Error(error.message);
      }

      return data || [];
    },
  });
}

export function useAddFinishedProductCount() {
  const queryClient = useQueryClient();
  const { currentUser } = useAuth();

  return useMutation({
    mutationFn: async (newCount: NewFinishedProductCount) => {
      if (!currentUser?.id) {
        throw new Error("User not authenticated");
      }

      const { data, error } = await supabase
        .from("finished_product_counts")
        .insert({
          ...newCount,
          user_id: currentUser.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error adding finished product count:", error.message);
        throw new Error(error.message);
      }

      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["finishedProductCounts"] });
    },
  });
}
