-- Remove duplicate entries, keeping only the most recent one for each kiln_id, chamber_number combination
DELETE FROM public.chamber_fire_status
WHERE id NOT IN (
  SELECT DISTINCT ON (kiln_id, chamber_number) id
  FROM public.chamber_fire_status
  ORDER BY kiln_id, chamber_number, updated_at DESC
);

-- Now add the unique constraint
ALTER TABLE public.chamber_fire_status 
ADD CONSTRAINT chamber_fire_status_kiln_chamber_unique 
UNIQUE (kiln_id, chamber_number);