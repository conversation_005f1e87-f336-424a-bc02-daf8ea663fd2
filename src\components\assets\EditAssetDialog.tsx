
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";

interface EditAssetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  asset: {
    id: string;
    name: string;
    type: string;
    status: "active" | "out_of_service";
  } | null;
  isLoading?: boolean;
  onEditAsset: (asset: { id: string; name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => void;
}

export function EditAssetDialog({ isOpen, onClose, asset, onEditAsset, isLoading = false }: EditAssetDialogProps) {
  const [name, setName] = useState("");
  const [type, setType] = useState("");
  const [identifier, setIdentifier] = useState("");
  const [status, setStatus] = useState<"active" | "out_of_service">("active");
  const { toast } = useToast();

  useEffect(() => {
    if (asset && isOpen) {
      const match = asset.name.match(/^(.*) \((.+)\)$/);
      if (match) {
        setName(match[1]);
        setIdentifier(match[2]);
      } else {
        setName(asset.name);
        setIdentifier("");
      }
      setType(asset.type);
      setStatus(asset.status ?? "active");
    }
    if (!isOpen) {
      setName("");
      setType("");
      setIdentifier("");
      setStatus("active");
    }
  }, [asset, isOpen]);

  const handleEdit = () => {
    if (!name || !type) {
      toast({
        title: "Missing Information",
        description: "Please fill out all fields to edit the asset.",
        variant: "destructive",
      });
      return;
    }
    onEditAsset({ id: asset!.id, name, type, identifier, status });
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Asset</DialogTitle>
          <DialogDescription>
            Update the details for this asset.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Asset Name</Label>
            <Input
              id="name"
              value={name}
              onChange={e => setName(e.target.value)}
              placeholder="e.g., Main Generator"
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="type">Asset Type</Label>
            <Select value={type} onValueChange={setType} disabled={isLoading}>
              <SelectTrigger id="type">
                <SelectValue placeholder="Select asset type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Truck">Truck</SelectItem>
                <SelectItem value="Generator">Generator</SelectItem>
                <SelectItem value="Vehicle">Vehicle</SelectItem>
                <SelectItem value="Machinery">Machinery</SelectItem>
                <SelectItem value="Heavy Equipment">Heavy Equipment</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="identifier">Identifier</Label>
            <Input
              id="identifier"
              value={identifier}
              onChange={e => setIdentifier(e.target.value)}
              placeholder="e.g., License Plate, Serial No."
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={v => setStatus(v as "active" | "out_of_service")} disabled={isLoading}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="out_of_service">Out of Service</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleEdit} disabled={isLoading}>
            {isLoading ? "Saving..." : "Save"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
