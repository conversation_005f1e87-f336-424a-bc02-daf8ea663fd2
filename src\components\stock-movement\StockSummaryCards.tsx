
import React from "react";
import { <PERSON>, <PERSON><PERSON>eader, <PERSON><PERSON><PERSON>le, CardContent } from "@/components/ui/card";
import { ArrowDown, ArrowUp, Package } from "lucide-react";
import { InboundMovement, OutboundMovement } from "@/hooks/useStockMovement";

interface StockSummaryCardsProps {
  inboundMovements: InboundMovement[];
  outboundMovements: OutboundMovement[];
}

export const StockSummaryCards: React.FC<StockSummaryCardsProps> = ({
  inboundMovements,
  outboundMovements
}) => {
  // Calculate totals
  const totalReceived = inboundMovements
    .filter(movement => movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalPending = inboundMovements
    .filter(movement => !movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalSold = outboundMovements
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const stockOnHand = totalReceived - totalSold;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Bricks Received</CardTitle>
          <ArrowDown className="h-4 w-4 text-green-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-green-600">
            {totalReceived.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Total bricks received at stock yard
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Pending Deliveries</CardTitle>
          <Package className="h-4 w-4 text-orange-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-orange-600">
            {totalPending.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Bricks awaiting receipt confirmation
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Stock on Hand</CardTitle>
          <Package className="h-4 w-4 text-blue-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-blue-600">
            {stockOnHand.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Current stock available
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Bricks Sold</CardTitle>
          <ArrowUp className="h-4 w-4 text-red-600" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold text-red-600">
            {totalSold.toLocaleString()}
          </div>
          <p className="text-xs text-muted-foreground">
            Total bricks sold from stock yard
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
