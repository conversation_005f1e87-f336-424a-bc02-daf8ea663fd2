import React from 'react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ChevronLeft, ChevronRight, CalendarIcon } from 'lucide-react';
import { format } from 'date-fns';
import { cn } from '@/lib/utils';
import { ViewMode } from '@/types/loadPlanning';
import { 
  getPeriodTitle, 
  getNextPeriod, 
  getPreviousPeriod, 
  isToday 
} from '@/utils/loadPlanningDateUtils';

interface DateNavigationProps {
  currentDate: Date;
  viewMode: ViewMode;
  onDateChange: (date: Date) => void;
  onPreviousPeriod: () => void;
  onNextPeriod: () => void;
  onTodayClick: () => void;
}

export const DateNavigation: React.FC<DateNavigationProps> = ({
  currentDate,
  viewMode,
  onDateChange,
  onPreviousPeriod,
  onNextPeriod,
  onTodayClick
}) => {
  const periodTitle = getPeriodTitle(currentDate, viewMode);
  const isTodaySelected = isToday(currentDate);

  return (
    <div className="flex items-center justify-between gap-4 p-4 bg-white rounded-lg border">
      <div className="flex items-center gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={onPreviousPeriod}
          className="h-8 w-8 p-0"
        >
          <ChevronLeft className="h-4 w-4" />
        </Button>
        
        <Button
          variant="outline"
          size="sm"
          onClick={onNextPeriod}
          className="h-8 w-8 p-0"
        >
          <ChevronRight className="h-4 w-4" />
        </Button>
      </div>

      <div className="flex-1 text-center">
        <h2 className="text-lg font-semibold text-slate-800">
          {periodTitle}
        </h2>
      </div>

      <div className="flex items-center gap-2">
        <Button
          variant={isTodaySelected ? "default" : "outline"}
          size="sm"
          onClick={onTodayClick}
          className="text-sm"
        >
          Today
        </Button>

        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "justify-start text-left font-normal",
                "h-8 px-3"
              )}
            >
              <CalendarIcon className="mr-2 h-4 w-4" />
              {format(currentDate, "MMM d")}
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar
              mode="single"
              selected={currentDate}
              onSelect={(date) => date && onDateChange(date)}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    </div>
  );
};
