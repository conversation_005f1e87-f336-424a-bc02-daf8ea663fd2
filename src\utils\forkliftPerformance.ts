
import { ForkliftAllocation } from "@/data/forkliftAllocations";
import { Employee } from "@/data/employeeData";

// For performance calculation, you'll need mapping of team to production output (via settingProductionEntries) and dehacker to pallet count (via dehacking)
import { getSettingProductionEntries } from "@/data/settingProductionStore";
import { getDehackingSummary } from "@/data/dehackingStore";
import { ManagementBrickType } from "@/data/managementBrickTypes";

/** 
 * Calculates total pallets for each team.
 */
export function getTeamPalletOutput(teamIds: string[]) {
  // Sum pallets from all entries for these teams from the settingProductionEntries store
  const settingProductionEntries = getSettingProductionEntries();
  let total = 0;
  for (const entry of settingProductionEntries) {
    if (teamIds.includes(entry.team_id)) {
      total += entry.pallet_count;
    }
  }
  return total;
}

/**
 * Calculates total pallets for each dehacking employee (allocated_teams as dehacker employee ids).
 */
export function getDehackerPalletOutput(employeeIds: string[], allEntries = getDehackingSummary(() => true)) {
  let total = 0;
  for (const entry of allEntries) {
    if (employeeIds.includes(String(entry.employeeId))) {
      total += entry.palletCount;
    }
  }
  return total;
}

/**
 * Computes the forklift driver performance map.
 * Returns an array of entries: { forklift_driver_name, allocation_type, allocated_names, total_pallets }
 */
export function computeForkliftPerformances({
  allocations,
  employees,
  teams,
}: {
  allocations: ForkliftAllocation[];
  employees: Employee[];
  teams: { id: string; name: string }[];
}) {
  // Compose a lookup for id->name for both employees and teams
  const employeeMap: Record<number, string> = Object.fromEntries(employees.map(e => [e.id, e.name]));
  const teamMap: Record<string, string> = Object.fromEntries(teams.map(t => [t.id, t.name]));
  const allDehackingEntries = getDehackingSummary(() => true);
  
  return allocations.map(alloc => {
    const driverName = employeeMap[alloc.forklift_driver_id] || "Unknown";
    let allocatedNames: string[] = [];
    let totalPallets = 0;

    if (alloc.allocation_type === "setting") {
      allocatedNames = alloc.allocated_teams.map(id => teamMap[id] || id);
      totalPallets = getTeamPalletOutput(alloc.allocated_teams);
    } else {
      allocatedNames = alloc.allocated_teams.map(id =>
        employees.find(e => String(e.id) === id)?.name || id
      );
      totalPallets = getDehackerPalletOutput(alloc.allocated_teams, allDehackingEntries);
    }
    return {
      driverName,
      allocationType: alloc.allocation_type,
      allocatedNames,
      totalPallets,
    };
  });
}
