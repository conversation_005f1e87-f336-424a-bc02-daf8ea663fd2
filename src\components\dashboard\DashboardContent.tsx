
import { QuickAccessCards } from "./QuickAccessCards";
import { TimeRangeSelector } from "./TimeRangeSelector";
import { KeyMetrics } from "./KeyMetrics";
import { AnalyticsCharts } from "./AnalyticsCharts";
import { FuelBunkersDashboard } from "./FuelBunkersDashboard";
import { ProductionLoss } from "./ProductionLoss";
import { DailyActivitySummary } from "./DailyActivitySummary";
import { ProductionBreakdownTable } from "./ProductionBreakdownTable";
import { useState } from "react";
import { useUser } from "@/contexts/UserContext";
import { useDashboardLayout } from "@/contexts/DashboardLayoutContext";
import { HacklineCountSummaryCard } from "./cards/HacklineCountSummaryCard";
import { FinishedStockSummaryCard } from "./cards/FinishedStockSummaryCard";
import { StockMovementSummaryCard } from "./cards/StockMovementSummaryCard";
import { LoadSummaryCard } from "./cards/LoadSummaryCard";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AlertTriangle } from "lucide-react";

export type TimeRange = "today" | "week" | "month" | "year" | "custom";

export interface CustomDateRange {
  from: string;
  to: string;
}

interface DashboardContentProps {
  onNavigateToStockMovement?: () => void;
}

export const DashboardContent = ({ onNavigateToStockMovement }: DashboardContentProps = {}) => {
  const [selectedTimeRange, setSelectedTimeRange] = useState<TimeRange>("today");
  const { userRole } = useUser();
  const { getGridClassName } = useDashboardLayout();

  // Query for total downtime hours
  const { data: totalDowntimeHours = 0 } = useQuery({
    queryKey: ['total-downtime', selectedTimeRange],
    queryFn: async () => {
      const now = new Date();
      let from: string, to: string;

      switch (selectedTimeRange) {
        case 'today':
          from = to = now.toISOString().split('T')[0];
          break;
        case 'week':
          const weekStart = new Date(now);
          weekStart.setDate(now.getDate() - now.getDay() + 1);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);
          from = weekStart.toISOString().split('T')[0];
          to = weekEnd.toISOString().split('T')[0];
          break;
        case 'month':
          from = new Date(now.getFullYear(), now.getMonth(), 1).toISOString().split('T')[0];
          to = new Date(now.getFullYear(), now.getMonth() + 1, 0).toISOString().split('T')[0];
          break;
        default:
          from = to = now.toISOString().split('T')[0];
      }

      const { data: breakdowns } = await supabase
        .from('breakdowns')
        .select('start_time, stop_time, date')
        .gte('date', from)
        .lte('date', to);

      const totalHours = breakdowns?.reduce((total, breakdown) => {
        if (breakdown.start_time && breakdown.start_time.trim() !== "" && breakdown.stop_time) {
          const start = new Date(`${breakdown.date}T${breakdown.start_time}`);
          const stop = new Date(`${breakdown.date}T${breakdown.stop_time}`);
          const hours = Math.abs(stop.getTime() - start.getTime()) / (1000 * 60 * 60);
          return total + hours;
        }
        return total;
      }, 0) || 0;

      return Math.round(totalHours * 100) / 100;
    }
  });

  // Finance role users should see a message directing them to Reports and Payments
  if (userRole === 'finance') {
    return (
      <div className="space-y-4 sm:space-y-6">
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 sm:p-6 text-center">
          <h2 className="text-lg sm:text-xl font-semibold text-blue-800 mb-2">Welcome to Finance Dashboard</h2>
          <p className="text-sm sm:text-base text-blue-600 mb-4">
            As a Finance user, you have access to Reports and Payments sections.
          </p>
          <p className="text-xs sm:text-sm text-blue-500">
            Use the navigation menu to access Reports and Payments.
          </p>
        </div>
      </div>
    );
  }

  // Check if user is a supervisor (factory or yard)
  const isSupervisor = userRole === 'factory_supervisor' || userRole === 'yard_supervisor';

  // Supervisors get a simplified dashboard with only their cards and activity summary
  if (isSupervisor) {
    return (
      <div className="space-y-4 sm:space-y-6">
        <QuickAccessCards onNavigateToStockMovement={onNavigateToStockMovement} />
        <DailyActivitySummary />
      </div>
    );
  }

  // Full dashboard for admins and managers
  const isManager = userRole === 'manager';

  const statsComponents = (
    <>
      <div className="flex justify-center">
        <TimeRangeSelector
          selectedRange={selectedTimeRange}
          onRangeChange={setSelectedTimeRange}
        />
      </div>
      <KeyMetrics timeRange={selectedTimeRange} />
      
      {/* Total Downtime Hours Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 text-red-700">
            <AlertTriangle size={20} />
            Total Downtime Hours
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-3xl font-bold text-red-800">
            {totalDowntimeHours}h
          </div>
          <p className="text-sm text-red-600">
            For selected period: {selectedTimeRange}
          </p>
        </CardContent>
      </Card>

      <div className={`grid ${getGridClassName()}`}>
        <FinishedStockSummaryCard />
        <HacklineCountSummaryCard />
        <LoadSummaryCard timeRange={selectedTimeRange} />
        <StockMovementSummaryCard />
      </div>
      
      {/* Replace AnalyticsCharts with ProductionBreakdownTable */}
      <ProductionBreakdownTable timeRange={selectedTimeRange} />
      
      <div className={`grid ${getGridClassName()}`}>
        <ProductionLoss />
        <FuelBunkersDashboard />
      </div>
    </>
  );

  return (
    <div className="space-y-4 sm:space-y-6">
      {isManager ? (
        <>
          {statsComponents}
          <QuickAccessCards onNavigateToStockMovement={onNavigateToStockMovement} />
        </>
      ) : (
        <>
          <QuickAccessCards onNavigateToStockMovement={onNavigateToStockMovement} />
          {statsComponents}
        </>
      )}
    </div>
  );
};
