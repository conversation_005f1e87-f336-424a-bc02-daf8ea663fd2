
-- Create table for kiln measurement actions
CREATE TABLE IF NOT EXISTS public.kiln_measurement_actions (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  measurement_id UUID NOT NULL,
  parameter_name TEXT NOT NULL,
  reading_value NUMERIC NOT NULL,
  is_out_of_norm BOOLEAN NOT NULL DEFAULT false,
  reasoning TEXT,
  action_required TEXT,
  action_taken TEXT,
  action_date TIMESTAMP WITH TIME ZONE,
  action_by UUI<PERSON>,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add RLS policies for kiln measurement actions
ALTER TABLE public.kiln_measurement_actions ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view all measurement actions" 
  ON public.kiln_measurement_actions 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can create measurement actions" 
  ON public.kiln_measurement_actions 
  FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Users can update measurement actions" 
  ON public.kiln_measurement_actions 
  FOR UPDATE 
  USING (true);

CREATE POLICY "Users can delete measurement actions" 
  ON public.kiln_measurement_actions 
  FOR DELETE 
  USING (true);

-- Add enhanced fields to kiln_parameter_norms table
ALTER TABLE public.kiln_parameter_norms 
ADD COLUMN IF NOT EXISTS reasoning TEXT,
ADD COLUMN IF NOT EXISTS action_required TEXT,
ADD COLUMN IF NOT EXISTS last_action_taken TEXT,
ADD COLUMN IF NOT EXISTS last_action_date TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS last_action_by UUID;
