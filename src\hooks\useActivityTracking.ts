import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';

export interface Activity {
  id: string;
  userId: string;
  userName: string;
  action: string;
  details: string;
  timestamp: Date;
  category: 'production' | 'team_management' | 'fuel' | 'system';
}

interface ActivitySummary {
  totalActivities: number;
  productionEntries: number;
  teamManagementActions: number;
  fuelManagementActions: number;
  sessionDuration: string;
}

export const useActivityTracking = () => {
  const { currentUser, getSessionDuration } = useAuth();
  const [activities, setActivities] = useState<Activity[]>([]);

  // Load activities from localStorage when currentUser changes
  useEffect(() => {
    if (currentUser) {
      const savedActivities = localStorage.getItem(`activities_${currentUser.id}`);
      if (savedActivities) {
        try {
          const parsed = JSON.parse(savedActivities);
          // Convert timestamp strings back to Date objects
          const activitiesWithDates = parsed.map((activity: any) => ({
            ...activity,
            timestamp: new Date(activity.timestamp)
          }));
          setActivities(activitiesWithDates);
        } catch (error) {
          console.error('Error loading activities:', error);
          setActivities([]);
        }
      } else {
        // No saved activities for this user, start fresh
        setActivities([]);
      }
    } else {
      // No current user, clear activities
      setActivities([]);
    }
  }, [currentUser]);

  // Save activities to localStorage whenever activities change
  useEffect(() => {
    if (currentUser) {
      if (activities.length > 0) {
        localStorage.setItem(`activities_${currentUser.id}`, JSON.stringify(activities));
      } else {
        // Remove empty activities from localStorage
        localStorage.removeItem(`activities_${currentUser.id}`);
      }
    }
  }, [activities, currentUser]);

  const logActivity = (action: string, details: string, category: Activity['category'] = 'system') => {
    if (!currentUser) {
      console.warn('[ActivityTracking] Cannot log activity: no current user');
      return;
    }

    const newActivity: Activity = {
      id: `activity_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      userId: currentUser.id,
      userName: currentUser.full_name,
      action,
      details,
      timestamp: new Date(),
      category
    };

    console.log('[ActivityTracking] Logging activity:', newActivity);
    setActivities(prev => {
      const updated = [newActivity, ...prev];
      console.log('[ActivityTracking] Updated activities count:', updated.length);
      return updated;
    });
  };

  const getTodaysActivities = (): Activity[] => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    return activities.filter(activity => {
      const activityDate = new Date(activity.timestamp);
      activityDate.setHours(0, 0, 0, 0);
      return activityDate.getTime() === today.getTime();
    });
  };

  const getActivitySummary = (): ActivitySummary => {
    const todaysActivities = getTodaysActivities();

    const summary = {
      totalActivities: todaysActivities.length,
      productionEntries: todaysActivities.filter(a => a.category === 'production').length,
      teamManagementActions: todaysActivities.filter(a => a.category === 'team_management').length,
      fuelManagementActions: todaysActivities.filter(a => a.category === 'fuel').length,
      sessionDuration: getSessionDuration()
    };

    console.log('[ActivityTracking] Activity summary:', {
      totalActivities: activities.length,
      todaysActivities: todaysActivities.length,
      summary,
      currentUser: currentUser?.username
    });

    return summary;
  };

  const clearTodaysActivities = () => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const filteredActivities = activities.filter(activity => {
      const activityDate = new Date(activity.timestamp);
      activityDate.setHours(0, 0, 0, 0);
      return activityDate.getTime() !== today.getTime();
    });
    
    setActivities(filteredActivities);
  };

  return {
    activities,
    logActivity,
    getTodaysActivities,
    getActivitySummary,
    clearTodaysActivities
  };
};
