
-- Function to update fuel bunker level after a delivery
CREATE OR REPLACE FUNCTION public.handle_fuel_delivery()
RETURNS TRIGGER AS $$
BEGIN
  -- Add the delivered quantity to the bunker's current level
  UPDATE public.fuel_bunkers
  SET current_level = current_level + NEW.quantity
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to execute the function after a new delivery is inserted
CREATE TRIGGER on_fuel_delivery_insert
  AFTER INSERT ON public.fuel_deliveries
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_fuel_delivery();

-- Function to update fuel bunker level after dispensing
CREATE OR REPLACE FUNCTION public.handle_fuel_dispensing()
RETURNS TRIGGER AS $$
BEGIN
  -- Subtract the dispensed quantity from the bunker's current level
  UPDATE public.fuel_bunkers
  SET current_level = current_level - NEW.quantity_liters
  WHERE id = NEW.fuel_bunker_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to execute the function after a new dispensing transaction is inserted
CREATE TRIGGER on_fuel_dispensing_insert
  AFTER INSERT ON public.fuel_dispensing_transactions
  FOR EACH ROW
  EXECUTE FUNCTION public.handle_fuel_dispensing();
