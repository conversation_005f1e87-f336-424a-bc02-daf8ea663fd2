
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";
import { ManagementBrickType } from "@/data/managementBrickTypes";

interface BrickTypeSelectionProps {
  loading: boolean;
  brickTypes: ManagementBrickType[];
  error?: Error | null;
  value: string | undefined;
  onChange: (val: string) => void;
}

export const BrickTypeSelection = ({ loading, brickTypes, error, value, onChange }: BrickTypeSelectionProps) => (
  <div className="mb-6">
    <div className="text-lg font-bold mb-2">Step 3: Select Extruded Brick Type</div>
    {loading ? (
      <div>Loading brick types...</div>
    ) : error ? (
      <div className="text-red-500">Error loading bricks: {error.message}</div>
    ) : brickTypes.length > 0 ? (
      <RadioGroup 
        value={value} 
        onValueChange={onChange}
        className="flex flex-col gap-2"
      >
        {brickTypes.map((brickType) => (
          <div key={brickType.id} className="flex items-center gap-2">
            <RadioGroupItem value={brickType.id} id={brickType.id} />
            <Label htmlFor={brickType.id} className="cursor-pointer">{brickType.name}</Label>
          </div>
        ))}
      </RadioGroup>
    ) : (
      <div className="text-gray-500">No active extruded brick types found in database.</div>
    )}
  </div>
);
