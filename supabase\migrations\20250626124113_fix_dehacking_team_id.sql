-- Fix for dehacking_entries team_id column issue
-- Add team_id column to dehacking_entries table if it doesn't exist

-- First, check if the column exists and add it if it doesn't
DO $$
BEGIN
    -- Check if team_id column exists
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_name = 'dehacking_entries' 
        AND column_name = 'team_id'
        AND table_schema = 'public'
    ) THEN
        -- Add the team_id column if it doesn't exist
        ALTER TABLE public.dehacking_entries 
        ADD COLUMN team_id TEXT REFERENCES public.teams(id);
        
        -- Add comment to document the purpose
        COMMENT ON COLUMN public.dehacking_entries.team_id IS 'References the team assigned to this dehacking entry';
        
        RAISE NOTICE 'Added team_id column to dehacking_entries table';
    ELSE
        RAISE NOTICE 'team_id column already exists in dehacking_entries table';
    END IF;
END $$;

-- Ensure the dehacking teams exist
INSERT INTO public.teams (id, name) VALUES 
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO NOTHING;
