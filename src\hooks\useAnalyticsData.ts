
import { useQuery } from '@tanstack/react-query';
import { TimeRange } from '@/components/dashboard/DashboardContent';
import { getAllAnalyticsData } from '@/data/analyticsData';

export function useAnalyticsData(timeRange: TimeRange) {
  return useQuery({
    queryKey: ['analyticsData', timeRange],
    queryFn: () => getAllAnalyticsData(timeRange),
    refetchInterval: 30000, // Refetch every 30 seconds for real-time updates
    refetchOnWindowFocus: true, // Refetch when window gains focus
  });
}
