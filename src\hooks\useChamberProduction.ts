import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import {
  getChamberProductionData,
  getChamberProductionDataByKiln,
  upsertChamberProductionData,
  updateBricksSet,
  updateBricksDehacked,
  getProductionSummary,
  deleteChamberProductionData,
  getProductionDataFromEntries,
  ChamberProductionData,
  ChamberProductionInput,
} from "@/data/chamberProductionData";

// Hook to get chamber production data for a specific date
export const useChamberProductionData = (date: string) => {
  return useQuery({
    queryKey: ['chamber-production-data', date],
    queryFn: () => getChamberProductionData(date),
    enabled: !!date,
  });
};

// Hook to get chamber production data for a specific kiln and date
export const useChamberProductionDataByKiln = (date: string, kilnId: string) => {
  return useQuery({
    queryKey: ['chamber-production-data-by-kiln', date, kilnId],
    queryFn: () => getChamberProductionDataByKiln(date, kilnId),
    enabled: !!date && !!kilnId,
  });
};

// Hook to get production summary for a date
export const useProductionSummary = (date: string) => {
  return useQuery({
    queryKey: ['production-summary', date],
    queryFn: () => getProductionSummary(date),
    enabled: !!date,
  });
};

// Hook to get production data from existing entries
export const useProductionDataFromEntries = (date: string) => {
  return useQuery({
    queryKey: ['production-data-from-entries', date],
    queryFn: () => getProductionDataFromEntries(date),
    enabled: !!date,
  });
};

// Hook to upsert chamber production data
export const useUpsertChamberProductionData = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (input: ChamberProductionInput) => upsertChamberProductionData(input),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data'] });
      queryClient.invalidateQueries({ queryKey: ['production-summary'] });
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data-by-kiln'] });
      
      toast.success('Chamber production data updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update chamber production data: ${error.message}`);
    },
  });
};

// Hook to update bricks set
export const useUpdateBricksSet = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      date, 
      kilnId, 
      chamberNumber, 
      bricksSet 
    }: { 
      date: string; 
      kilnId: string; 
      chamberNumber: number; 
      bricksSet: number; 
    }) => updateBricksSet(date, kilnId, chamberNumber, bricksSet),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data'] });
      queryClient.invalidateQueries({ queryKey: ['production-summary'] });
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data-by-kiln'] });
      
      toast.success('Bricks set updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update bricks set: ${error.message}`);
    },
  });
};

// Hook to update bricks dehacked
export const useUpdateBricksDehacked = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ 
      date, 
      kilnId, 
      chamberNumber, 
      bricksDehacked 
    }: { 
      date: string; 
      kilnId: string; 
      chamberNumber: number; 
      bricksDehacked: number; 
    }) => updateBricksDehacked(date, kilnId, chamberNumber, bricksDehacked),
    onSuccess: (data) => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data'] });
      queryClient.invalidateQueries({ queryKey: ['production-summary'] });
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data-by-kiln'] });
      
      toast.success('Bricks dehacked updated successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to update bricks dehacked: ${error.message}`);
    },
  });
};

// Hook to delete chamber production data
export const useDeleteChamberProductionData = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteChamberProductionData(id),
    onSuccess: () => {
      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data'] });
      queryClient.invalidateQueries({ queryKey: ['production-summary'] });
      queryClient.invalidateQueries({ queryKey: ['chamber-production-data-by-kiln'] });
      
      toast.success('Chamber production data deleted successfully');
    },
    onError: (error: Error) => {
      toast.error(`Failed to delete chamber production data: ${error.message}`);
    },
  });
};
