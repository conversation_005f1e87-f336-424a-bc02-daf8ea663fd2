import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

export interface ChamberProductionData {
  id: number;
  date: string;
  kiln_id: string;
  chamber_number: number;
  bricks_set: number;
  bricks_dehacked: number;
  output: number;
  breakage: number;
  notes?: string;
  user_id?: string;
  created_at: string;
  updated_at: string;
}

export interface ChamberProductionInput {
  date: string;
  kiln_id: string;
  chamber_number: number;
  bricks_set?: number;
  bricks_dehacked?: number;
  notes?: string;
}

// Get chamber production data for a specific date
export const getChamberProductionData = async (date: string): Promise<ChamberProductionData[]> => {
  try {
    const { data, error } = await supabase
      .from('chamber_production_tracking')
      .select('*')
      .eq('date', date)
      .order('kiln_id', { ascending: true })
      .order('chamber_number', { ascending: true });

    if (error) {
      console.error('Error fetching chamber production data:', error);
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getChamberProductionData:', error);
    throw error;
  }
};

// Get chamber production data for a specific kiln and date
export const getChamberProductionDataByKiln = async (
  date: string, 
  kilnId: string
): Promise<ChamberProductionData[]> => {
  try {
    const { data, error } = await supabase
      .from('chamber_production_tracking')
      .select('*')
      .eq('date', date)
      .eq('kiln_id', kilnId)
      .order('chamber_number', { ascending: true });

    if (error) {
      console.error('Error fetching chamber production data by kiln:', error);
      throw new Error(error.message);
    }

    return data || [];
  } catch (error) {
    console.error('Error in getChamberProductionDataByKiln:', error);
    throw error;
  }
};

// Create or update chamber production data
export const upsertChamberProductionData = async (
  input: ChamberProductionInput
): Promise<ChamberProductionData> => {
  try {
    const { data, error } = await supabase
      .from('chamber_production_tracking')
      .upsert({
        date: input.date,
        kiln_id: input.kiln_id,
        chamber_number: input.chamber_number,
        bricks_set: input.bricks_set || 0,
        bricks_dehacked: input.bricks_dehacked || 0,
        notes: input.notes,
      }, {
        onConflict: 'date,kiln_id,chamber_number'
      })
      .select()
      .single();

    if (error) {
      console.error('Error upserting chamber production data:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in upsertChamberProductionData:', error);
    throw error;
  }
};

// Update bricks set for a chamber
export const updateBricksSet = async (
  date: string,
  kilnId: string,
  chamberNumber: number,
  bricksSet: number
): Promise<ChamberProductionData> => {
  try {
    const { data, error } = await supabase
      .from('chamber_production_tracking')
      .upsert({
        date,
        kiln_id: kilnId,
        chamber_number: chamberNumber,
        bricks_set: bricksSet,
      }, {
        onConflict: 'date,kiln_id,chamber_number'
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating bricks set:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in updateBricksSet:', error);
    throw error;
  }
};

// Update bricks dehacked for a chamber
export const updateBricksDehacked = async (
  date: string,
  kilnId: string,
  chamberNumber: number,
  bricksDehacked: number
): Promise<ChamberProductionData> => {
  try {
    const { data, error } = await supabase
      .from('chamber_production_tracking')
      .upsert({
        date,
        kiln_id: kilnId,
        chamber_number: chamberNumber,
        bricks_dehacked: bricksDehacked,
      }, {
        onConflict: 'date,kiln_id,chamber_number'
      })
      .select()
      .single();

    if (error) {
      console.error('Error updating bricks dehacked:', error);
      throw new Error(error.message);
    }

    return data;
  } catch (error) {
    console.error('Error in updateBricksDehacked:', error);
    throw error;
  }
};

// Calculate production summary for a date
export const getProductionSummary = async (date: string) => {
  try {
    const data = await getChamberProductionData(date);
    
    const summary = data.reduce((acc, chamber) => {
      acc.totalBricksSet += chamber.bricks_set;
      acc.totalBricksDehacked += chamber.bricks_dehacked;
      acc.totalOutput += chamber.output;
      acc.totalBreakage += chamber.breakage;
      return acc;
    }, {
      totalBricksSet: 0,
      totalBricksDehacked: 0,
      totalOutput: 0,
      totalBreakage: 0,
    });

    return {
      ...summary,
      breakagePercentage: summary.totalBricksSet > 0 
        ? (summary.totalBreakage / summary.totalBricksSet) * 100 
        : 0,
    };
  } catch (error) {
    console.error('Error calculating production summary:', error);
    throw error;
  }
};

// Delete chamber production data
export const deleteChamberProductionData = async (id: number): Promise<void> => {
  try {
    const { error } = await supabase
      .from('chamber_production_tracking')
      .delete()
      .eq('id', id);

    if (error) {
      console.error('Error deleting chamber production data:', error);
      throw new Error(error.message);
    }
  } catch (error) {
    console.error('Error in deleteChamberProductionData:', error);
    throw error;
  }
};

// Get production data from existing setting and dehacking entries
export const getProductionDataFromEntries = async (date: string) => {
  try {
    // Get setting production entries
    const { data: settingEntries, error: settingError } = await supabase
      .from('setting_production_entries')
      .select(`
        *,
        management_brick_types(name, bricks_per_pallet),
        fires(kiln_id)
      `)
      .eq('date', date);

    if (settingError) {
      console.error('Error fetching setting entries:', settingError);
      throw new Error(settingError.message);
    }

    // Get dehacking entries
    const { data: dehackingEntries, error: dehackingError } = await supabase
      .from('dehacking_entries')
      .select(`
        *,
        management_brick_types(name, bricks_per_pallet),
        fires(kiln_id)
      `)
      .eq('date', date);

    if (dehackingError) {
      console.error('Error fetching dehacking entries:', dehackingError);
      throw new Error(dehackingError.message);
    }

    return {
      settingEntries: settingEntries || [],
      dehackingEntries: dehackingEntries || [],
    };
  } catch (error) {
    console.error('Error in getProductionDataFromEntries:', error);
    throw error;
  }
};
