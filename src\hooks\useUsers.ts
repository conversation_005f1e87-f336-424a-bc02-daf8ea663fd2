
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import type { Database } from "@/integrations/supabase/types";
import bcrypt from 'bcryptjs';

export type UserRole = Database["public"]["Enums"]["user_role"];

export interface User {
  id: string;
  username?: string;
  full_name?: string;
  email?: string;
  role: UserRole;
  active: boolean;
  created_at: string;
  updated_at: string;
}

export interface CreateUserData {
  username: string;
  full_name: string;
  email?: string;
  password: string;
  role: UserRole;
}

export interface UpdateUserData {
  username?: string;
  full_name?: string;
  email?: string;
  role?: UserRole;
  active?: boolean;
}

export function useUsers() {
  return useQuery({
    queryKey: ["users"],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("users")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw new Error(error.message);
      return data as User[];
    }
  });
}

export function useCreateUser() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (userData: CreateUserData) => {
      try {
        // Hash the password
        const saltRounds = 12;
        const hashedPassword = await bcrypt.hash(userData.password, saltRounds);

        // Insert directly into users table
        const { data, error } = await supabase
          .from("users")
          .insert({
            username: userData.username,
            full_name: userData.full_name,
            email: userData.email,
            password_hash: hashedPassword,
            role: userData.role,
            active: true
          })
          .select()
          .single();

        if (error) {
          throw new Error(`Failed to create user: ${error.message}`);
        }

        return data;
      } catch (err) {
        console.error('User creation exception:', err);
        throw new Error(err instanceof Error ? err.message : 'Failed to create user');
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useUpdateUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateUserData }) => {
      const { data: result, error } = await supabase
        .from('users')
        .update({
          ...data,
          updated_at: new Date().toISOString()
        })
        .eq('id', id)
        .select()
        .single();
      
      if (error) {
        throw new Error(error.message);
      }
      
      return result;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useDeleteUser() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async (id: string) => {
      const { error } = await supabase
        .from('users')
        .delete()
        .eq('id', id);
      
      if (error) {
        throw new Error(error.message);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
    }
  });
}

export function useChangePassword() {
  return useMutation({
    mutationFn: async ({ userId, newPassword }: { 
      userId: string; 
      newPassword: string; 
    }) => {
      // Hash the new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);
      
      const { error } = await supabase
        .from('users')
        .update({ 
          password_hash: hashedPassword,
          updated_at: new Date().toISOString()
        })
        .eq('id', userId);
      
      if (error) {
        throw new Error(error.message);
      }
    }
  });
}
