import React, { createContext, useContext, useEffect, useState } from "react";
import { useSystemConfig, useUpdateSystemConfig } from "@/hooks/useSystemConfig";

export type DashboardLayout = "Compact" | "Standard" | "Detailed";

interface DashboardLayoutContextType {
  layout: DashboardLayout;
  setLayout: (layout: DashboardLayout) => Promise<void>;
  isLoading: boolean;
  getCardClassName: () => string;
  getGridClassName: () => string;
}

const DashboardLayoutContext = createContext<DashboardLayoutContextType | undefined>(undefined);

export const DashboardLayoutProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [layout, setLayoutState] = useState<DashboardLayout>("Standard");
  const { data: systemConfig } = useSystemConfig();
  const updateSystemConfigMutation = useUpdateSystemConfig();

  // Sync layout from system config
  useEffect(() => {
    if (systemConfig?.dashboard_layout) {
      setLayoutState(systemConfig.dashboard_layout as DashboardLayout);
    }
  }, [systemConfig?.dashboard_layout]);

  const setLayout = async (newLayout: DashboardLayout) => {
    try {
      setLayoutState(newLayout);
      await updateSystemConfigMutation.mutateAsync({ 
        dashboard_layout: newLayout 
      });
    } catch (error) {
      console.error("Failed to update dashboard layout:", error);
      // Revert on error
      if (systemConfig?.dashboard_layout) {
        setLayoutState(systemConfig.dashboard_layout as DashboardLayout);
      }
    }
  };

  const getCardClassName = () => {
    switch (layout) {
      case "Compact":
        return "p-3";
      case "Standard":
        return "p-4";
      case "Detailed":
        return "p-6";
      default:
        return "p-4";
    }
  };

  const getGridClassName = () => {
    switch (layout) {
      case "Compact":
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3";
      case "Standard":
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";
      case "Detailed":
        return "grid-cols-1 md:grid-cols-2 gap-6";
      default:
        return "grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4";
    }
  };

  const contextValue: DashboardLayoutContextType = {
    layout,
    setLayout,
    isLoading: updateSystemConfigMutation.isPending,
    getCardClassName,
    getGridClassName,
  };

  return (
    <DashboardLayoutContext.Provider value={contextValue}>
      {children}
    </DashboardLayoutContext.Provider>
  );
};

export const useDashboardLayout = (): DashboardLayoutContextType => {
  const context = useContext(DashboardLayoutContext);
  if (context === undefined) {
    throw new Error('useDashboardLayout must be used within a DashboardLayoutProvider');
  }
  return context;
};
