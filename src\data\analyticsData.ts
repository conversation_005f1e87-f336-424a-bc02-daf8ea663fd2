
import { TimeRange } from "@/components/dashboard/DashboardContent";
import { supabase } from "@/integrations/supabase/client";
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format, eachDayOfInterval, parseISO } from 'date-fns';

const getDateRange = (timeRange: TimeRange) => {
  const now = new Date();
  switch (timeRange) {
    case 'today':
      return { from: startOfDay(now), to: endOfDay(now) };
    case 'week':
      return { from: startOfWeek(now, { weekStartsOn: 1 }), to: endOfWeek(now, { weekStartsOn: 1 }) };
    case 'month':
      return { from: startOfMonth(now), to: endOfMonth(now) };
    case 'year':
      return { from: startOfYear(now), to: endOfYear(now) };
  }
};

export const getProductionOverTimeData = async (timeRange: TimeRange) => {
    const { from, to } = getDateRange(timeRange);
    const fromString = format(from, 'yyyy-MM-dd');
    const toString = format(to, 'yyyy-MM-dd');

    const [settingRes, dehackingRes, factoryRes] = await Promise.all([
        supabase.from('setting_production_entries').select('date, pallet_count').gte('date', fromString).lte('date', toString),
        supabase.from('dehacking_entries').select('date, pallet_count').gte('date', fromString).lte('date', toString),
        supabase.from('production_entries').select('date, pallet_count').gte('date', fromString).lte('date', toString)
    ]);

    if (settingRes.error) {
        console.error("Error fetching setting production entries", settingRes.error);
        throw settingRes.error;
    }
    if (dehackingRes.error) {
        console.error("Error fetching dehacking entries", dehackingRes.error);
        throw dehackingRes.error;
    }
    if (factoryRes.error) {
        console.error("Error fetching factory production entries", factoryRes.error);
        throw factoryRes.error;
    }

    const dailyData: { [key: string]: { setting: number; dehacking: number; factory: number; total: number } } = {};
    const intervalDays = eachDayOfInterval({ start: from, end: to });

    intervalDays.forEach(day => {
        const dateString = format(day, 'yyyy-MM-dd');
        dailyData[dateString] = { setting: 0, dehacking: 0, factory: 0, total: 0 };
    });

    (settingRes.data || []).forEach(entry => {
        if (entry.date && dailyData[entry.date]) {
            dailyData[entry.date].setting += entry.pallet_count;
            dailyData[entry.date].total += entry.pallet_count;
        }
    });

    (dehackingRes.data || []).forEach(entry => {
        if (entry.date && dailyData[entry.date]) {
            dailyData[entry.date].dehacking += entry.pallet_count;
            dailyData[entry.date].total += entry.pallet_count;
        }
    });

    // Add factory production data
    (factoryRes.data || []).forEach(entry => {
        if (entry.date && dailyData[entry.date]) {
            dailyData[entry.date].factory += entry.pallet_count;
            dailyData[entry.date].total += entry.pallet_count;
        }
    });

    const dayFormat = intervalDays.length > 8 ? 'dd/MM' : 'E';

    return Object.entries(dailyData).map(([date, values]) => ({
        name: format(parseISO(date), dayFormat),
        setting: values.setting,
        dehacking: values.dehacking,
        factory: values.factory,
        production: values.total,
    }));
}

export const getKilnProductionData = async (timeRange: TimeRange) => {
    const { from, to } = getDateRange(timeRange);
    const fromString = format(from, 'yyyy-MM-dd');
    const toString = format(to, 'yyyy-MM-dd');

    const [settingRes, firesRes, kilnsRes] = await Promise.all([
        supabase.from('setting_production_entries').select('fire_id, pallet_count').gte('date', fromString).lte('date', toString),
        supabase.from('fires').select('id, kiln_id'),
        supabase.from('kilns').select('id, name')
    ]);

    if (settingRes.error) throw settingRes.error;
    if (firesRes.error) throw firesRes.error;
    if (kilnsRes.error) throw kilnsRes.error;

    const fireToKilnMap = new Map((firesRes.data || []).map(f => [f.id, f.kiln_id]));
    const kilnIdToNameMap = new Map((kilnsRes.data || []).map(k => [k.id, k.name]));
    
    const kilnProduction: { [key: string]: number } = {};

    (settingRes.data || []).forEach(entry => {
        const kilnId = fireToKilnMap.get(entry.fire_id);
        if (kilnId) {
            kilnProduction[kilnId] = (kilnProduction[kilnId] || 0) + entry.pallet_count;
        }
    });
    
    const colors = ['#8884d8', '#82ca9d', '#ffc658', '#ff7300', '#0088FE', '#00C49F', '#FFBB28', '#FF8042'];

    return Object.entries(kilnProduction).map(([kilnId, count], index) => ({
        name: kilnIdToNameMap.get(kilnId) || `Kiln ${kilnId}`,
        value: count,
        color: colors[index % colors.length]
    }));
}

export const getAllAnalyticsData = async (timeRange: TimeRange) => {
    const [productionOverTime, kilnProduction] = await Promise.all([
        getProductionOverTimeData(timeRange),
        getKilnProductionData(timeRange)
    ]);
    return { productionOverTime, kilnProduction };
};

