
import { supabase } from "@/integrations/supabase/client";
import type { ReportData } from "./types";

export const getFuelManagementReport = async ({ from, to }: { from: string, to: string }): Promise<ReportData> => {
    const { data: deliveries, error: delError } = await supabase.from('fuel_deliveries').select('*, fuel_bunkers(name)').gte('delivery_date', from).lte('delivery_date', to);
    const { data: dispensed, error: dispError } = await supabase.from('fuel_dispensing_transactions').select('*, fuel_bunkers(name)').gte('transaction_date', from).lte('transaction_date', to);
    if (delError || dispError) throw delError || dispError;

    const byFuelType = (deliveries as any[]).reduce((acc, d) => {
        const bunkerName = d.fuel_bunkers.name;
        if (!acc[bunkerName]) acc[bunkerName] = { name: bunkerName, consumed: 0, delivered: 0, cost: 0 };
        acc[bunkerName].delivered += d.quantity;
        acc[bunkerName].cost += d.quantity * d.cost_per_liter;
        return acc;
    }, {} as Record<string, {name: string, consumed: number, delivered: number, cost: number}>);

    (dispensed as any[]).forEach(d => {
        const bunkerName = d.fuel_bunkers.name;
        if (!byFuelType[bunkerName]) byFuelType[bunkerName] = { name: bunkerName, consumed: 0, delivered: 0, cost: 0 };
        byFuelType[bunkerName].consumed += d.quantity_liters;
    });

    const secondary = (dispensed as any[]).map(d => ({
        asset: d.asset_id,
        fuel_type: d.fuel_bunkers.name,
        consumption: d.quantity_liters,
    }));

    return { main: Object.values(byFuelType), secondary };
};
