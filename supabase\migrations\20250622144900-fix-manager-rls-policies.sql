-- Fix RLS policies for hackline_counts, finished_product_counts, teams, team_memberships, and setting_production_entries tables
-- This migration removes the auth.uid() dependency and allows all operations
-- since the app uses custom authentication instead of Supabase Auth

-- Drop existing policies for hackline_counts
DROP POLICY IF EXISTS "Users can insert hackline counts" ON public.hackline_counts;

-- Create new policies for hackline_counts
CREATE POLICY "Users can insert hackline counts" ON public.hackline_counts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update hackline counts" ON public.hackline_counts
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete hackline counts" ON public.hackline_counts
  FOR DELETE USING (true);

-- Drop existing policies for finished_product_counts
DROP POLICY IF EXISTS "Users can insert finished product counts" ON public.finished_product_counts;

-- Create new policies for finished_product_counts
CREATE POLICY "Users can insert finished product counts" ON public.finished_product_counts
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update finished product counts" ON public.finished_product_counts
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete finished product counts" ON public.finished_product_counts
  FOR DELETE USING (true);

-- Drop existing policies for teams
DROP POLICY IF EXISTS "Users can insert teams" ON public.teams;

-- Create new policies for teams
CREATE POLICY "Users can insert teams" ON public.teams
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update teams" ON public.teams
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete teams" ON public.teams
  FOR DELETE USING (true);

-- Drop existing policies for team_memberships
DROP POLICY IF EXISTS "Users can insert team memberships" ON public.team_memberships;

-- Create new policies for team_memberships
CREATE POLICY "Users can insert team memberships" ON public.team_memberships
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update team memberships" ON public.team_memberships
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete team memberships" ON public.team_memberships
  FOR DELETE USING (true);

-- Drop existing policies for setting_production_entries
DROP POLICY IF EXISTS "Users can insert setting production entries" ON public.setting_production_entries;

-- Create new policies for setting_production_entries
CREATE POLICY "Users can insert setting production entries" ON public.setting_production_entries
  FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update setting production entries" ON public.setting_production_entries
  FOR UPDATE USING (true);

CREATE POLICY "Users can delete setting production entries" ON public.setting_production_entries
  FOR DELETE USING (true);

-- Drop existing policies for chamber_fire_status (if table exists)
DROP POLICY IF EXISTS "Users can insert chamber fire status" ON public.chamber_fire_status;

-- Create new policies for chamber_fire_status (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT FROM pg_tables WHERE schemaname = 'public' AND tablename = 'chamber_fire_status') THEN
    EXECUTE 'CREATE POLICY "Users can insert chamber fire status" ON public.chamber_fire_status
      FOR INSERT WITH CHECK (true);';
    
    EXECUTE 'CREATE POLICY "Users can update chamber fire status" ON public.chamber_fire_status
      FOR UPDATE USING (true);';
    
    EXECUTE 'CREATE POLICY "Users can delete chamber fire status" ON public.chamber_fire_status
      FOR DELETE USING (true);';
  END IF;
END
$$;
