import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Package, Truck, Edit, CheckCircle, Star, RotateCcw, X } from 'lucide-react';
import { format } from 'date-fns';
import { LoadPlanningEntry } from '@/types/loadPlanning';
import { filterLoadsByDateRange } from '@/utils/loadPlanningDateUtils';

interface DailyViewProps {
  loads: LoadPlanningEntry[];
  currentDate: Date;
  isLoading: boolean;
  onEditLoad: (load: LoadPlanningEntry) => void;
  onRescheduleLoad: (load: LoadPlanningEntry) => void;
  onMarkAsReady: (loadId: string) => void;
  onMarkAsDispatched: (loadId: string) => void;
  onCancelLoad: (loadId: string) => void;
  isMarkingReady: boolean;
  isMarkingDispatched: boolean;
}

export const DailyView: React.FC<DailyViewProps> = ({
  loads,
  currentDate,
  isLoading,
  onEditLoad,
  onRescheduleLoad,
  onMarkAsReady,
  onMarkAsDispatched,
  onCancelLoad,
  isMarkingReady,
  isMarkingDispatched
}) => {
  const dailyLoads = filterLoadsByDateRange(loads, currentDate, 'daily');
  
  // Sort loads by rank (priority)
  const sortedLoads = dailyLoads.sort((a, b) => (a.rank || 1) - (b.rank || 1));

  const getStatusBadge = (load: LoadPlanningEntry) => {
    if (load.dispatched) {
      return <Badge variant="default" className="bg-green-500">Dispatched</Badge>;
    }
    if (load.ready) {
      return <Badge variant="secondary" className="bg-blue-500 text-white">Ready</Badge>;
    }
    return <Badge variant="outline">Scheduled</Badge>;
  };

  const getRankBadge = (rank?: number) => {
    if (!rank) return null;
    
    const colors = {
      1: "bg-red-500 text-white",
      2: "bg-orange-500 text-white", 
      3: "bg-yellow-500 text-white",
    };
    
    const colorClass = colors[rank as keyof typeof colors] || "bg-gray-500 text-white";
    
    return (
      <Badge className={`${colorClass} flex items-center gap-1`}>
        <Star className="h-3 w-3" />
        {rank}
      </Badge>
    );
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="animate-pulse">
            <CardContent className="p-6">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2"></div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (sortedLoads.length === 0) {
    return (
      <Card>
        <CardContent className="p-8 text-center">
          <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">No loads scheduled</h3>
          <p className="text-gray-500">
            No loads are scheduled for {format(currentDate, 'EEEE, MMMM d, yyyy')}
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-slate-800">
          {sortedLoads.length} load{sortedLoads.length !== 1 ? 's' : ''} scheduled
        </h3>
        <div className="text-sm text-slate-600">
          {format(currentDate, 'EEEE, MMMM d, yyyy')}
        </div>
      </div>

      {sortedLoads.map((load) => (
        <Card key={load.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-3">
                  <h4 className="font-semibold text-slate-800">{load.client_name}</h4>
                  {getRankBadge(load.rank)}
                  {getStatusBadge(load)}
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm text-slate-600">
                  <div className="flex items-center gap-2">
                    <Truck className="h-4 w-4" />
                    <span>{load.transporter}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    <span>{load.brick_count} {load.load_type}</span>
                  </div>
                  {load.management_brick_types && (
                    <div className="col-span-2">
                      <span className="font-medium">Brick Type:</span> {load.management_brick_types.name}
                    </div>
                  )}
                  {load.load_description && (
                    <div className="col-span-2">
                      <span className="font-medium">Description:</span> {load.load_description}
                    </div>
                  )}
                </div>
              </div>

              <div className="flex items-center gap-2 ml-4">
                {!load.dispatched && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onEditLoad(load)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onRescheduleLoad(load)}
                    >
                      <RotateCcw className="h-4 w-4" />
                    </Button>

                    {!load.ready ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onMarkAsReady(load.id)}
                        disabled={isMarkingReady}
                        className="text-blue-600 border-blue-600 hover:bg-blue-50"
                      >
                        <Star className="h-4 w-4" />
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => onMarkAsDispatched(load.id)}
                        disabled={isMarkingDispatched}
                        className="text-green-600 border-green-600 hover:bg-green-50"
                      >
                        <CheckCircle className="h-4 w-4" />
                      </Button>
                    )}

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => onCancelLoad(load.id)}
                      className="text-red-600 border-red-600 hover:bg-red-50"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};
