
-- Drop types if they exist to avoid errors on re-run
DROP TYPE IF EXISTS public.payment_status CASCADE;
DROP TYPE IF EXISTS public.payment_type CASCADE;

-- Create ENUM types for status and type
CREATE TYPE public.payment_status AS ENUM ('Paid', 'Pending', 'Processing', 'Failed');
CREATE TYPE public.payment_type AS ENUM ('Salary', 'Bonus', 'Overtime', 'Dehacking', 'Setting', 'Adjustment');

-- Create the payments table
CREATE TABLE public.payments (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    employee_id INTEGER NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    amount NUMERIC(10, 2) NOT NULL,
    payment_type public.payment_type NOT NULL,
    payment_date DATE NOT NULL,
    status public.payment_status NOT NULL DEFAULT 'Pending',
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.payments ENABLE ROW LEVEL SECURITY;

-- Allow full access for now, can be restricted later if needed
CREATE POLICY "Allow public read on payments" ON public.payments FOR SELECT USING (true);
CREATE POLICY "Allow public modification of payments" ON public.payments FOR ALL USING (true);
