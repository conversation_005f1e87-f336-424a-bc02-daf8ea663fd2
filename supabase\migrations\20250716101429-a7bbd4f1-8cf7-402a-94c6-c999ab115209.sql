
-- Update the calculate_brick_count function to use values from management_brick_types table
CREATE OR REPLACE FUNCTION calculate_brick_count()
RETURNS TRIGGER AS $$
DECLARE
  bricks_per_pallet_value INTEGER;
BEGIN
  -- Get the correct bricks_per_pallet value from management_brick_types table
  IF NEW.pallet_type = 'Imperial' THEN
    SELECT bricks_per_pallet INTO bricks_per_pallet_value
    FROM management_brick_types 
    WHERE id = 'imperial_extruded';
  ELSIF NEW.pallet_type = 'Maxi' THEN
    SELECT bricks_per_pallet INTO bricks_per_pallet_value
    FROM management_brick_types 
    WHERE id = 'maxi_extruded';
  END IF;
  
  -- Calculate the total using the value from the table
  IF bricks_per_pallet_value IS NOT NULL THEN
    NEW.count_total = NEW.pallet_count * bricks_per_pallet_value;
  ELSE
    -- Fallback to old values if records not found
    IF NEW.pallet_type = 'Imperial' THEN
      NEW.count_total = NEW.pallet_count * 320;
    ELSIF NEW.pallet_type = 'Maxi' THEN
      NEW.count_total = NEW.pallet_count * 280;
    END IF;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
