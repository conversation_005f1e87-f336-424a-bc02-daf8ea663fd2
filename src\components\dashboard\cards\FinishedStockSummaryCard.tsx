
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Package, Loader2 } from "lucide-react";
import { useFinishedProductCounts } from "@/hooks/useFinishedProductCounts";

export const FinishedStockSummaryCard = () => {
  const { data: finishedProductCounts = [], isLoading } = useFinishedProductCounts();

  // Filter to only show current day's data
  const today = new Date().toISOString().split('T')[0]; // Get current date in YYYY-MM-DD format
  const todayCounts = finishedProductCounts.filter(count => count.date.split('T')[0] === today);
  
  // Aggregate data for today only
  const aggregatedData = todayCounts.reduce((acc, curr) => {
    if (!acc[curr.product_type]) {
      acc[curr.product_type] = 0;
    }
    acc[curr.product_type] += curr.pallet_count;
    return acc;
  }, {} as Record<string, number>);

  const typeBreakdown = Object.entries(aggregatedData).map(([name, pallets]) => ({
    name,
    pallets,
  })).sort((a, b) => b.pallets - a.pallets);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">Finished Stock</CardTitle>
        <p className="text-sm text-slate-600">Today's stock levels by brick type</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
          </div>
        ) : (
          <div>
            <h4 className="flex items-center gap-2 font-semibold text-slate-700 mb-3">
              <Package size={16} /> 
              By Brick Type
            </h4>
            {typeBreakdown.length > 0 ? (
              <div className="space-y-2">
                {typeBreakdown.map(type => (
                  <div key={type.name} className="flex justify-between items-center py-2 px-3 bg-slate-50 rounded-md">
                    <span className="text-slate-700 font-medium">{type.name}</span>
                    <span className="font-semibold text-slate-800">{type.pallets.toLocaleString()} pallets</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <Package className="h-12 w-12 text-slate-300 mx-auto mb-3" />
                <p className="text-slate-500 font-medium">No finished stock data</p>
                <p className="text-sm text-slate-400">available</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
