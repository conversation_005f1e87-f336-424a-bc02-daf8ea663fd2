
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getPayments, addPayment, PaymentWithEmployeeName, NewPayment } from "@/data/paymentsData";
import { useToast } from "@/hooks/use-toast";

export function usePayments() {
  return useQuery<PaymentWithEmployeeName[], Error>({
    queryKey: ['payments'],
    queryFn: getPayments,
  });
}

export function useAddPayment() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  return useMutation({
    mutationFn: (payment: NewPayment | NewPayment[]) => addPayment(payment),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['payments'] });
      queryClient.invalidateQueries({ queryKey: ['pendingEarnings'] });
      toast({ title: "Success", description: "Payment added successfully." });
    },
    onError: (error: Error) => {
      toast({ title: "Error", description: error.message, variant: "destructive" });
    }
  });
}
