
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Fuel, Loader2 } from "lucide-react";
import { useQuery } from "@tanstack/react-query";
import { getFuelBunkers } from "@/data/fuelBunkersData";

interface FuelManagementCardProps {
  onManageFuel: () => void;
}

export const FuelManagementCard = ({ onManageFuel }: FuelManagementCardProps) => {
  const { data: bunkers = [], isLoading } = useQuery({
      queryKey: ['fuelBunkers'],
      queryFn: getFuelBunkers,
  });

  const mainBunker = bunkers.find(b => b.name === 'Main Bunker');
  const mainBunkerLevel = mainBunker ? mainBunker.current_level : 0;

  return (
    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
          <Fuel size={20} />
          Fuel Management
        </CardTitle>
        <p className="text-sm text-blue-600">Fuel Level Monitoring</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div>
            <p className="text-sm text-blue-600">Main Bunker</p>
            {isLoading ? (
                <Loader2 className="h-6 w-6 animate-spin text-blue-800" />
            ) : (
                <p className="text-2xl font-bold text-blue-800">{mainBunkerLevel.toLocaleString()} L</p>
            )}
          </div>
          <div className="flex justify-between text-sm text-blue-600">
            <span>Delivery</span>
            <span>Dispensed</span>
          </div>
          <Button 
            className="w-full bg-blue-500 hover:bg-blue-600"
            onClick={onManageFuel}
          >
            Manage Fuel
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
