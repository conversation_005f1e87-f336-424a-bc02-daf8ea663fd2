
CREATE TABLE public.team_memberships (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    team_id TEXT NOT NULL REFERENCES public.teams(id) ON DELETE CASCADE,
    employee_id INTEGER NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    CONSTRAINT unique_team_member UNIQUE (team_id, employee_id)
);

-- Enable Row Level Security
ALTER TABLE public.team_memberships ENABLE ROW LEVEL SECURITY;

-- Allow full access for now, can be restricted later if needed
CREATE POLICY "Allow public read on team memberships" ON public.team_memberships FOR SELECT USING (true);
CREATE POLICY "Allow public modification of team memberships" ON public.team_memberships FOR ALL USING (true);

