
import { Card, CardContent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Package, Plus, Search } from "lucide-react";
import { Input } from "@/components/ui/input";

export const CollectionPage = () => {
  const collections = [
    { id: 1, name: "Standard Bricks", quantity: 1250, status: "Active" },
    { id: 2, name: "Premium Pavers", quantity: 890, status: "Active" },
    { id: 3, name: "Roof Tiles", quantity: 560, status: "Pending" },
    { id: 4, name: "Garden Blocks", quantity: 340, status: "Active" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Collection Management</h1>
          <p className="text-slate-600">Manage product collections and inventory</p>
        </div>
        <Button className="bg-slate-800 hover:bg-slate-700">
          <Plus size={20} className="mr-2" />
          Add Collection
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package size={20} />
            Active Collections
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search size={16} className="absolute left-3 top-3 text-slate-400" />
              <Input 
                placeholder="Search collections..." 
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Collection Name</th>
                  <th className="text-left py-3 px-4">Quantity</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {collections.map((collection) => (
                  <tr key={collection.id} className="border-b hover:bg-slate-50">
                    <td className="py-3 px-4 font-medium">{collection.name}</td>
                    <td className="py-3 px-4">{collection.quantity.toLocaleString()}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        collection.status === 'Active' 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {collection.status}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">Edit</Button>
                        <Button variant="outline" size="sm">View</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
