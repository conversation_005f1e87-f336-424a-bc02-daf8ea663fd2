
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { TestTube } from "lucide-react";
import { CarbonTestsPage } from "./CarbonTestsPage";
import { SpiralLoadsPage } from "./SpiralLoadsPage";

export const CarbonSpiralTrackerPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <TestTube size={28} className="text-slate-700" />
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Carbon & Spiral Tracker</h1>
          <p className="text-slate-600">Track carbon tests and spiral loads</p>
        </div>
      </div>

      <Tabs defaultValue="carbon-tests" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="carbon-tests">Carbon Tests</TabsTrigger>
          <TabsTrigger value="spiral-loads">Spiral Loads</TabsTrigger>
        </TabsList>

        <TabsContent value="carbon-tests" className="mt-6">
          <CarbonTestsPage />
        </TabsContent>

        <TabsContent value="spiral-loads" className="mt-6">
          <SpiralLoadsPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};
