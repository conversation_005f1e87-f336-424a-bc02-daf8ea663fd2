
import { useState } from "react";
import { DateNavigation } from "@/components/load-planning/DateNavigation";
import { LoadsList } from "@/components/load-planning/LoadsList";
import { LoadPlanningForecastTable } from "@/components/load-planning/LoadPlanningForecastTable";
import { AddLoadDialog } from "@/components/load-planning/AddLoadDialog";
import { Button } from "@/components/ui/button";
import { Plus, Calendar, List, BarChart3 } from "lucide-react";
import { DateNavigationState, ViewMode } from "@/types/loadPlanning";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useLoadPlanning } from "@/hooks/useLoadPlanning";
import { getDateRangeForView, getNextPeriod, getPreviousPeriod } from "@/utils/loadPlanningDateUtils";

export const LoadPlanningPage = () => {
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [dateNavState, setDateNavState] = useState<DateNavigationState>({
    currentDate: new Date(),
    viewMode: 'daily'
  });

  // Get date range for current view and fetch loads
  const dateRange = getDateRangeForView(dateNavState.currentDate, dateNavState.viewMode);
  const { loads, isLoading } = useLoadPlanning({
    dateFrom: dateRange.start,
    dateTo: dateRange.end
  });

  // Also get all loads for the forecast table (without date filtering)
  const { loads: allLoads, isLoading: isLoadingAll } = useLoadPlanning();

  const handleViewModeChange = (mode: ViewMode) => {
    setDateNavState(prev => ({ ...prev, viewMode: mode }));
  };

  const handleDateChange = (date: Date) => {
    setDateNavState(prev => ({ ...prev, currentDate: date }));
  };

  const handlePreviousPeriod = () => {
    const previousDate = getPreviousPeriod(dateNavState.currentDate, dateNavState.viewMode);
    setDateNavState(prev => ({ ...prev, currentDate: previousDate }));
  };

  const handleNextPeriod = () => {
    const nextDate = getNextPeriod(dateNavState.currentDate, dateNavState.viewMode);
    setDateNavState(prev => ({ ...prev, currentDate: nextDate }));
  };

  const handleTodayClick = () => {
    setDateNavState(prev => ({ ...prev, currentDate: new Date() }));
  };

  return (
    <TooltipProvider>
      <div className="space-y-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Load Planning</h1>
            <p className="text-gray-600">Plan and track brick deliveries</p>
          </div>
          <div className="flex items-center gap-2">
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={dateNavState.viewMode === 'daily' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleViewModeChange('daily')}
                >
                  <Calendar className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Daily View</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={dateNavState.viewMode === 'weekly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleViewModeChange('weekly')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Weekly View</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant={dateNavState.viewMode === 'monthly' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => handleViewModeChange('monthly')}
                >
                  <BarChart3 className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Monthly View</p>
              </TooltipContent>
            </Tooltip>
            
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  onClick={() => setIsAddDialogOpen(true)}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  Add Load
                </Button>
              </TooltipTrigger>
              <TooltipContent>
                <p>Add New Load</p>
              </TooltipContent>
            </Tooltip>
          </div>
        </div>

        {/* Load Planning Forecast Table */}
        <LoadPlanningForecastTable 
          loads={allLoads} 
          isLoading={isLoadingAll} 
        />

        <DateNavigation
          currentDate={dateNavState.currentDate}
          viewMode={dateNavState.viewMode}
          onDateChange={handleDateChange}
          onPreviousPeriod={handlePreviousPeriod}
          onNextPeriod={handleNextPeriod}
          onTodayClick={handleTodayClick}
        />

        <LoadsList
          loads={loads}
          isLoading={isLoading}
        />

        <AddLoadDialog
          open={isAddDialogOpen}
          onOpenChange={setIsAddDialogOpen}
        />
      </div>
    </TooltipProvider>
  );
};
