import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { CreditCard, FileText } from "lucide-react";
import { PaymentsPage } from "./PaymentsPage";
import { ReportsPage } from "./ReportsPage";

export const FinancialManagementPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <CreditCard className="h-8 w-8 text-slate-600" />
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Financial Management</h1>
          <p className="text-slate-600">Manage payments and reports</p>
        </div>
      </div>

      <Tabs defaultValue="payments" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="payments" className="flex items-center gap-2">
            <CreditCard size={16} />
            Payments
          </TabsTrigger>
          <TabsTrigger value="reports" className="flex items-center gap-2">
            <FileText size={16} />
            Reports
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="payments" className="mt-6">
          <PaymentsPage />
        </TabsContent>
        
        <TabsContent value="reports" className="mt-6">
          <ReportsPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};
