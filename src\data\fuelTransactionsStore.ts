
export type FuelTransactionType = "dispensing" | "delivery";

export interface FuelDispensingTransaction {
  id: string;
  type: "dispensing";
  date: Date;
  bunkerId: string;
  assetId: string;
  assetType: "machinery" | "vehicle";
  startReading: number;
  endReading?: number; // For closing calculation if applicable
  litresFilled: number;
}

export interface FuelDeliveryTransaction {
  id: string;
  type: "delivery";
  date: Date;
  bunkerId: string;
  supplier: string;
  invoiceNumber: string;
  quantity: number;
  costPerLiter: number;
}

export type FuelTransaction = FuelDispensingTransaction | FuelDeliveryTransaction;

let fuelTransactions: FuelTransaction[] = [];

export const logFuelTransaction = (tx: FuelTransaction) => {
  fuelTransactions.push(tx);
};

export const getFuelTransactions = () => fuelTransactions;

// Calculate opening/closing balances (basic version)
export function getBunkerBalance(bunkerId: string) {
  // Assume you start with bunkers' "current" from fuelBunkersData at midnight
  // This function simply computes closing balance by applying transactions for the day
  const dayTransactions = fuelTransactions.filter(t =>
    ("bunkerId" in t) && t.bunkerId === bunkerId
  );
  let balanceChange = 0;
  for (let t of dayTransactions) {
    if (t.type === "dispensing") {
      balanceChange -= t.litresFilled;
    } else if (t.type === "delivery") {
      balanceChange += t.quantity;
    }
  }
  return balanceChange;
}

// For demo/testing only: reset all transactions
export function resetFuelTransactions() {
  fuelTransactions = [];
}
