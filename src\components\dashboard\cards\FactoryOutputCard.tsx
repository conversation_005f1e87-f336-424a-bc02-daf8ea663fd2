
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Factory } from "lucide-react";
import { useMemo } from "react";
import { useQuery } from "@tanstack/react-query";
import { getBrickTypes, type BrickType } from "@/data/fuelBunkersData";
import { supabase } from "@/integrations/supabase/client";

interface FactoryOutputCardProps {
  onRecordProduction: () => void;
}

export const FactoryOutputCard = ({ onRecordProduction }: FactoryOutputCardProps) => {
  const { data: productionEntries = [] } = useQuery({
    queryKey: ['productionEntries'],
    queryFn: async () => {
        const { data, error } = await supabase.from('production_entries').select('*');
        if (error) throw new Error(error.message);
        return data;
    }
  });

  const { data: brickTypes = [] } = useQuery<BrickType[]>({
    queryKey: ['brickTypes'],
    queryFn: getBrickTypes,
  });

  const { pallets, bricks } = useMemo(() => {
    const brickTypeMap = new Map(brickTypes.map(bt => [bt.id, bt.bricks_per_pallet]));

    return productionEntries.reduce(
      (acc, entry) => {
        acc.pallets += entry.pallet_count;
        const bricksPerPallet = brickTypeMap.get(entry.brick_type_id) || 0;
        acc.bricks += entry.pallet_count * bricksPerPallet;
        return acc;
      },
      { pallets: 0, bricks: 0 }
    );
  }, [productionEntries, brickTypes]);

  return (
    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-orange-100">
      <CardHeader className="pb-3">
        <CardTitle className="text-lg font-semibold text-orange-800 flex items-center gap-2">
          <Factory size={20} />
          Factory Output
        </CardTitle>
        <p className="text-sm text-orange-600">Live production tracking</p>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex gap-6">
            <div>
              <p className="text-sm text-orange-600">Bricks</p>
              <p className="text-2xl font-bold text-orange-800">{bricks.toLocaleString()}</p>
            </div>
            <div>
              <p className="text-sm text-orange-600">Pallets</p>
              <p className="text-2xl font-bold text-orange-800">{pallets.toLocaleString()}</p>
            </div>
          </div>
          <Button 
            className="w-full bg-orange-500 hover:bg-orange-600"
            onClick={onRecordProduction}
          >
            Record Production
          </Button>
        </div>
      </CardContent>
    </Card>
  );
};
