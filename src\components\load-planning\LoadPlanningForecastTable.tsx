
import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { TrendingUp, Package, Calendar } from "lucide-react";
import { format, startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth } from "date-fns";
import { LoadPlanningEntry } from "@/types/loadPlanning";

interface LoadPlanningForecastTableProps {
  loads: LoadPlanningEntry[];
  isLoading?: boolean;
}

// Define the product types we want to track
const PRODUCT_TYPES = [
  { key: 'imp_nfx', label: 'IMP NFX', category: 'Imperial' },
  { key: 'imp_nfp', label: 'IMP NFP', category: 'Imperial' },
  { key: 'imp_2nd_grade', label: 'IMP 2nd Grade', category: 'Imperial' },
  { key: 'maxi_nfx', label: 'MAXI NFX', category: 'Maxi' },
  { key: 'maxi_nfp', label: 'MAXI NFP', category: 'Maxi' },
  { key: 'maxi_2nd_grade', label: 'MAXI 2nd Grade', category: 'Maxi' },
];

export const LoadPlanningForecastTable = ({ loads, isLoading }: LoadPlanningForecastTableProps) => {
  const today = new Date();

  // Helper function to normalize brick type names for matching
  const normalizeBrickTypeName = (name: string): string => {
    return name.toLowerCase()
      .replace(/\s+/g, '_')
      .replace('imperial', 'imp')
      .replace('maxi', 'maxi')
      .replace('2nd_grade', '2nd_grade')
      .replace('nfp', 'nfp')
      .replace('nfx', 'nfx');
  };

  // Helper function to get loads for a specific period
  const getLoadsForPeriod = (startDate: Date, endDate: Date): LoadPlanningEntry[] => {
    return loads.filter(load => {
      const loadDate = new Date(load.date);
      return loadDate >= startDate && loadDate <= endDate;
    });
  };

  // Helper function to calculate brick requirements by product type
  const calculateBrickRequirements = (periodLoads: LoadPlanningEntry[]) => {
    const requirements: Record<string, number> = {};
    
    // Initialize all product types to 0
    PRODUCT_TYPES.forEach(product => {
      requirements[product.key] = 0;
    });

    periodLoads.forEach(load => {
      if (load.management_brick_types?.name && load.brick_count) {
        const normalizedName = normalizeBrickTypeName(load.management_brick_types.name);
        
        // Find matching product type
        const matchingProduct = PRODUCT_TYPES.find(product => {
          const productKey = product.key.toLowerCase();
          return normalizedName.includes(productKey.replace('_', '')) || 
                 normalizedName.includes(productKey) ||
                 (productKey.includes('nfx') && normalizedName.includes('nfx')) ||
                 (productKey.includes('nfp') && normalizedName.includes('nfp')) ||
                 (productKey.includes('2nd_grade') && (normalizedName.includes('2nd') || normalizedName.includes('second')));
        });

        if (matchingProduct) {
          requirements[matchingProduct.key] += load.brick_count;
        }
      }
    });

    return requirements;
  };

  // Calculate periods
  const todayStart = startOfDay(today);
  const todayEnd = endOfDay(today);
  
  const weekStart = startOfWeek(today, { weekStartsOn: 1 });
  const weekEnd = endOfWeek(today, { weekStartsOn: 1 });
  
  const monthStart = startOfMonth(today);
  const monthEnd = endOfMonth(today);

  // Get loads for each period
  const todayLoads = getLoadsForPeriod(todayStart, todayEnd);
  const weekLoads = getLoadsForPeriod(weekStart, weekEnd);
  const monthLoads = getLoadsForPeriod(monthStart, monthEnd);

  // Calculate requirements
  const todayRequirements = calculateBrickRequirements(todayLoads);
  const weekRequirements = calculateBrickRequirements(weekLoads);
  const monthRequirements = calculateBrickRequirements(monthLoads);

  // Calculate totals
  const todayTotal = Object.values(todayRequirements).reduce((sum, count) => sum + count, 0);
  const weekTotal = Object.values(weekRequirements).reduce((sum, count) => sum + count, 0);
  const monthTotal = Object.values(monthRequirements).reduce((sum, count) => sum + count, 0);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Load Planning Forecast
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="animate-pulse space-y-4">
            <div className="h-4 bg-slate-200 rounded w-1/2"></div>
            <div className="space-y-2">
              {[...Array(7)].map((_, i) => (
                <div key={i} className="h-8 bg-slate-200 rounded"></div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Load Planning Forecast
        </CardTitle>
        <div className="flex items-center gap-4 text-sm text-muted-foreground">
          <div className="flex items-center gap-1">
            <Calendar className="h-4 w-4" />
            <span>Today: {format(today, 'MMM dd, yyyy')}</span>
          </div>
          <div className="flex items-center gap-1">
            <Package className="h-4 w-4" />
            <span>Total Loads: {loads.length}</span>
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="font-semibold">Product Type</TableHead>
                <TableHead className="text-center font-semibold">
                  Today
                  <div className="text-xs font-normal text-muted-foreground">
                    {format(today, 'MMM dd')}
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold">
                  This Week
                  <div className="text-xs font-normal text-muted-foreground">
                    {format(weekStart, 'MMM dd')} - {format(weekEnd, 'MMM dd')}
                  </div>
                </TableHead>
                <TableHead className="text-center font-semibold">
                  This Month
                  <div className="text-xs font-normal text-muted-foreground">
                    {format(monthStart, 'MMM yyyy')}
                  </div>
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {PRODUCT_TYPES.map((product) => (
                <TableRow key={product.key}>
                  <TableCell className="font-medium">
                    <div className="flex items-center gap-2">
                      <Badge variant={product.category === 'Imperial' ? 'default' : 'secondary'} className="text-xs">
                        {product.category}
                      </Badge>
                      {product.label}
                    </div>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className={`font-semibold ${todayRequirements[product.key] > 0 ? 'text-blue-600' : 'text-muted-foreground'}`}>
                      {todayRequirements[product.key].toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className={`font-semibold ${weekRequirements[product.key] > 0 ? 'text-green-600' : 'text-muted-foreground'}`}>
                      {weekRequirements[product.key].toLocaleString()}
                    </span>
                  </TableCell>
                  <TableCell className="text-center">
                    <span className={`font-semibold ${monthRequirements[product.key] > 0 ? 'text-purple-600' : 'text-muted-foreground'}`}>
                      {monthRequirements[product.key].toLocaleString()}
                    </span>
                  </TableCell>
                </TableRow>
              ))}
              
              {/* Totals Row */}
              <TableRow className="border-t-2 bg-muted/50">
                <TableCell className="font-bold">
                  <div className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    TOTAL BRICKS
                  </div>
                </TableCell>
                <TableCell className="text-center">
                  <span className="font-bold text-blue-700 text-lg">
                    {todayTotal.toLocaleString()}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  <span className="font-bold text-green-700 text-lg">
                    {weekTotal.toLocaleString()}
                  </span>
                </TableCell>
                <TableCell className="text-center">
                  <span className="font-bold text-purple-700 text-lg">
                    {monthTotal.toLocaleString()}
                  </span>
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
          <Card className="border-blue-200 bg-blue-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-blue-600">Today's Demand</p>
                  <p className="text-2xl font-bold text-blue-700">{todayTotal.toLocaleString()}</p>
                  <p className="text-xs text-blue-500">{todayLoads.length} load{todayLoads.length !== 1 ? 's' : ''}</p>
                </div>
                <Calendar className="h-8 w-8 text-blue-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-green-200 bg-green-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-green-600">Weekly Demand</p>
                  <p className="text-2xl font-bold text-green-700">{weekTotal.toLocaleString()}</p>
                  <p className="text-xs text-green-500">{weekLoads.length} load{weekLoads.length !== 1 ? 's' : ''}</p>
                </div>
                <TrendingUp className="h-8 w-8 text-green-400" />
              </div>
            </CardContent>
          </Card>

          <Card className="border-purple-200 bg-purple-50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-purple-600">Monthly Demand</p>
                  <p className="text-2xl font-bold text-purple-700">{monthTotal.toLocaleString()}</p>
                  <p className="text-xs text-purple-500">{monthLoads.length} load{monthLoads.length !== 1 ? 's' : ''}</p>
                </div>
                <Package className="h-8 w-8 text-purple-400" />
              </div>
            </CardContent>
          </Card>
        </div>
      </CardContent>
    </Card>
  );
};
