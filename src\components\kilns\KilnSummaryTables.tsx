
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useQuery } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { format } from "date-fns";
import { Loader2 } from "lucide-react";

interface KilnSummaryData {
  kiln_name: string;
  fire_name: string;
  imperial_count: number;
  maxi_count: number;
  nfx_imp_count: number;
  nfp_imp_count: number;
  second_grade_imp_count: number;
  maxi_nfx_count: number;
  maxi_nfp_count: number;
  maxi_second_grade_count: number;
}

interface KilnForecastData {
  kiln_name: string;
  imperial_total: number;
  maxi_total: number;
}

export const KilnSummaryTables = () => {
  const today = format(new Date(), 'yyyy-MM-dd');
  
  // Kiln Forecast - what bricks are currently set in kilns
  const { data: kilnForecast = [], isLoading: forecastLoading } = useQuery({
    queryKey: ['kiln-forecast', today],
    queryFn: async () => {
      // Get data from the last 30 days to show what's currently in kilns
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const { data, error } = await supabase
        .from('setting_production_entries')
        .select(`
          *,
          management_brick_types(name, category, bricks_per_pallet),
          fires(name, kiln_id, kilns(name))
        `)
        .gte('date', thirtyDaysAgo.toISOString().split('T')[0])
        .lte('date', today);

      if (error) {
        console.error('Error fetching kiln forecast:', error);
        return [];
      }

      // Group by kiln and calculate totals
      const grouped = data.reduce((acc: any[], entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const brickType = entry.management_brick_types?.category || 'Unknown';
        
        let existing = acc.find(item => item.kiln_name === kilnName);
        if (!existing) {
          existing = {
            kiln_name: kilnName,
            imperial_total: 0,
            maxi_total: 0
          };
          acc.push(existing);
        }

        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 0);
        
        if (brickType.toLowerCase().includes('imperial')) {
          existing.imperial_total += brickCount;
        } else if (brickType.toLowerCase().includes('maxi')) {
          existing.maxi_total += brickCount;
        }

        return acc;
      }, []);

      // Sort by kiln name
      return grouped.sort((a, b) => {
        if (a.kiln_name === 'Habla') return -1;
        if (b.kiln_name === 'Habla') return 1;
        return a.kiln_name.localeCompare(b.kiln_name);
      });
    },
  });

  const { data: settingSummary = [], isLoading: settingLoading } = useQuery({
    queryKey: ['kiln-setting-summary', today],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('setting_production_entries')
        .select(`
          *,
          management_brick_types(name, category),
          fires(name, kiln_id, kilns(name))
        `)
        .eq('date', today);

      if (error) {
        console.error('Error fetching setting summary:', error);
        return [];
      }

      // Group by kiln and fire
      const grouped = data.reduce((acc: any[], entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const fireName = entry.fires?.name || 'Unknown';
        const brickType = entry.management_brick_types?.category || 'Unknown';
        
        let existing = acc.find(item => item.kiln_name === kilnName && item.fire_name === fireName);
        if (!existing) {
          existing = {
            kiln_name: kilnName,
            fire_name: fireName,
            imperial_count: 0,
            maxi_count: 0
          };
          acc.push(existing);
        }

        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 0);
        
        if (brickType.toLowerCase().includes('imperial')) {
          existing.imperial_count += brickCount;
        } else if (brickType.toLowerCase().includes('maxi')) {
          existing.maxi_count += brickCount;
        }

        return acc;
      }, []);

      // Sort by kiln name
      return grouped.sort((a, b) => {
        if (a.kiln_name === 'Habla') return -1;
        if (b.kiln_name === 'Habla') return 1;
        return a.kiln_name.localeCompare(b.kiln_name);
      });
    },
  });

  const { data: dehackingSummary = [], isLoading: dehackingLoading } = useQuery({
    queryKey: ['kiln-dehacking-summary', today],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('dehacking_entries')
        .select(`
          *,
          management_brick_types(name, category, grade),
          fires(name, kiln_id, kilns(name))
        `)
        .eq('date', today);

      if (error) {
        console.error('Error fetching dehacking summary:', error);
        return [];
      }

      // Group by kiln and fire
      const grouped = data.reduce((acc: any[], entry: any) => {
        const kilnName = entry.fires?.kilns?.name || 'Unknown';
        const fireName = entry.fires?.name || 'Unknown';
        const brickType = entry.management_brick_types?.category || 'Unknown';
        const grade = entry.management_brick_types?.grade || 'Unknown';
        
        let existing = acc.find(item => item.kiln_name === kilnName && item.fire_name === fireName);
        if (!existing) {
          existing = {
            kiln_name: kilnName,
            fire_name: fireName,
            nfx_imp_count: 0,
            nfp_imp_count: 0,
            second_grade_imp_count: 0,
            maxi_nfx_count: 0,
            maxi_nfp_count: 0,
            maxi_second_grade_count: 0
          };
          acc.push(existing);
        }

        const brickCount = entry.pallet_count * (entry.management_brick_types?.bricks_per_pallet || 0);
        
        if (brickType.toLowerCase().includes('imperial')) {
          if (grade.toLowerCase().includes('nfx')) {
            existing.nfx_imp_count += brickCount;
          } else if (grade.toLowerCase().includes('nfp')) {
            existing.nfp_imp_count += brickCount;
          } else if (grade.toLowerCase().includes('2nd') || grade.toLowerCase().includes('second')) {
            existing.second_grade_imp_count += brickCount;
          }
        } else if (brickType.toLowerCase().includes('maxi')) {
          if (grade.toLowerCase().includes('nfx')) {
            existing.maxi_nfx_count += brickCount;
          } else if (grade.toLowerCase().includes('nfp')) {
            existing.maxi_nfp_count += brickCount;
          } else if (grade.toLowerCase().includes('2nd') || grade.toLowerCase().includes('second')) {
            existing.maxi_second_grade_count += brickCount;
          }
        }

        return acc;
      }, []);

      // Sort by kiln name
      return grouped.sort((a, b) => {
        if (a.kiln_name === 'Habla') return -1;
        if (b.kiln_name === 'Habla') return 1;
        return a.kiln_name.localeCompare(b.kiln_name);
      });
    },
  });

  if (settingLoading || dehackingLoading || forecastLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
        <p className="ml-2 text-slate-500">Loading summary data...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Kiln Forecast Table */}
      <Card>
        <CardHeader>
          <CardTitle>Kiln Forecast - Bricks Currently in Kilns</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">Imperial Total</TableHead>
                <TableHead className="text-right">Maxi Total</TableHead>
                <TableHead className="text-right">Grand Total</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {kilnForecast.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">{item.kiln_name}</TableCell>
                  <TableCell className="text-right">
                    {item.imperial_total.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_total.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right font-semibold">
                    {(item.imperial_total + item.maxi_total).toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {kilnForecast.length === 0 && (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-slate-500">
                    No forecast data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Setting Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Setting Summary - {format(new Date(), 'dd MMM yyyy')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">Imperial</TableHead>
                <TableHead className="text-right">Maxi</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {settingSummary.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {item.kiln_name} ({item.fire_name})
                  </TableCell>
                  <TableCell className="text-right">
                    {item.imperial_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_count.toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {settingSummary.length === 0 && (
                <TableRow>
                  <TableCell colSpan={3} className="text-center text-slate-500">
                    No setting data for today
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Dehacking Summary Table */}
      <Card>
        <CardHeader>
          <CardTitle>Dehacking Summary - {format(new Date(), 'dd MMM yyyy')}</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Kiln</TableHead>
                <TableHead className="text-right">NFX IMP</TableHead>
                <TableHead className="text-right">NFP IMP</TableHead>
                <TableHead className="text-right">2nd Grade IMP</TableHead>
                <TableHead className="text-right">Maxi NFX</TableHead>
                <TableHead className="text-right">Maxi NFP</TableHead>
                <TableHead className="text-right">Maxi 2nd Grade</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dehackingSummary.map((item, index) => (
                <TableRow key={index}>
                  <TableCell className="font-medium">
                    {item.kiln_name} ({item.fire_name})
                  </TableCell>
                  <TableCell className="text-right">
                    {item.nfx_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.nfp_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.second_grade_imp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_nfx_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_nfp_count.toLocaleString()}
                  </TableCell>
                  <TableCell className="text-right">
                    {item.maxi_second_grade_count.toLocaleString()}
                  </TableCell>
                </TableRow>
              ))}
              {dehackingSummary.length === 0 && (
                <TableRow>
                  <TableCell colSpan={7} className="text-center text-slate-500">
                    No dehacking data for today
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  );
};
