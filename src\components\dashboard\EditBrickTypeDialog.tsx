import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { toast } from "sonner";
import { supabaseAdmin } from "@/integrations/supabase/client";
import { ManagementBrickType } from "@/data/managementBrickTypes";

interface EditBrickTypeDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  brickType: ManagementBrickType | null;
  onSuccess?: () => void;
}

interface EditBrickTypeFormData {
  name: string;
  category: string;
  grade: string;
  settingRate: number;
  dehackingRate: number;
  overtimeRate: number;
  status: string;
  brickStage: string;
}

const EditBrickTypeDialog: React.FC<EditBrickTypeDialogProps> = ({ 
  open, 
  onOpenChange,
  brickType,
  onSuccess
}) => {
  const [formData, setFormData] = useState<EditBrickTypeFormData>({
    name: '',
    category: '',
    grade: '',
    settingRate: 0,
    dehackingRate: 0,
    overtimeRate: 0,
    status: 'Active',
    brickStage: 'finished'
  });
  const [loading, setLoading] = useState(false);

  // Populate form when brickType changes
  useEffect(() => {
    if (brickType) {
      setFormData({
        name: brickType.name,
        category: brickType.category,
        grade: brickType.grade,
        settingRate: brickType.setting_rate,
        dehackingRate: brickType.dehacking_rate,
        overtimeRate: brickType.overtime_rate,
        status: brickType.status,
        brickStage: brickType.brick_stage || 'finished'
      });
    }
  }, [brickType]);

  const handleChange = (field: keyof EditBrickTypeFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    if (!brickType) return;
    
    setLoading(true);
    
    const payload = {
      name: formData.name,
      category: formData.category,
      grade: formData.grade,
      setting_rate: formData.settingRate,
      dehacking_rate: formData.dehackingRate,
      overtime_rate: formData.overtimeRate,
      status: formData.status,
      brick_stage: formData.brickStage,
    };

    const { error } = await supabaseAdmin
      .from('management_brick_types')
      .update(payload)
      .eq('id', brickType.id);

    setLoading(false);

    if (error) {
      toast.error("Failed to update brick type: " + error.message);
      return;
    }
    
    toast.success("Brick type updated successfully");
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  const handleDelete = async () => {
    if (!brickType) return;
    
    if (!window.confirm(`Are you sure you want to delete "${brickType.name}"? This action cannot be undone.`)) {
      return;
    }

    setLoading(true);
    
    const { error } = await supabaseAdmin
      .from('management_brick_types')
      .delete()
      .eq('id', brickType.id);

    setLoading(false);

    if (error) {
      toast.error("Failed to delete brick type: " + error.message);
      return;
    }
    
    toast.success("Brick type deleted successfully");
    onOpenChange(false);
    if (onSuccess) onSuccess();
  };

  if (!brickType) return null;

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Edit Brick Type: {brickType.name}</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <label htmlFor="brickName">Brick Name</label>
            <Input 
              id="brickName" 
              placeholder="Enter brick type name" 
              value={formData.name}
              onChange={(e) => handleChange('name', e.target.value)}
            />
          </div>
          
          <div className="grid gap-2">
            <label htmlFor="brickStage">Brick Stage</label>
            <Select 
              value={formData.brickStage} 
              onValueChange={(value) => handleChange('brickStage', value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select brick stage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="extruded">Extruded (Imperial/Maxi for output & settings)</SelectItem>
                <SelectItem value="finished">Finished (for dehacking/products)</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="category">Category</label>
              <Select 
                value={formData.category} 
                onValueChange={(value) => handleChange('category', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Facing">Facing</SelectItem>
                  <SelectItem value="Engineering">Engineering</SelectItem>
                  <SelectItem value="Decorative">Decorative</SelectItem>
                  <SelectItem value="Paving">Paving</SelectItem>
                  <SelectItem value="Extruded">Extruded</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="grade">Grade</label>
              <Select 
                value={formData.grade} 
                onValueChange={(value) => handleChange('grade', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select grade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A+">A+</SelectItem>
                  <SelectItem value="A">A</SelectItem>
                  <SelectItem value="B">B</SelectItem>
                  <SelectItem value="C">C</SelectItem>
                  <SelectItem value="2nd Grade">2nd Grade</SelectItem>
                  <SelectItem value="NFP">NFP</SelectItem>
                  <SelectItem value="NFX">NFX</SelectItem>
                  <SelectItem value="Blue">Blue</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="settingRate">Setting Rate (₹ per pallet)</label>
              <Input 
                id="settingRate" 
                type="number" 
                value={formData.settingRate.toString()}
                onChange={(e) => handleChange('settingRate', parseFloat(e.target.value) || 0)}
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="dehackingRate">Dehacking Rate (₹ per pallet)</label>
              <Input 
                id="dehackingRate" 
                type="number" 
                value={formData.dehackingRate.toString()}
                onChange={(e) => handleChange('dehackingRate', parseFloat(e.target.value) || 0)}
              />
            </div>
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div className="grid gap-2">
              <label htmlFor="overtimeRate">Overtime Rate (₹ per pallet)</label>
              <Input 
                id="overtimeRate" 
                type="number" 
                value={formData.overtimeRate.toString()}
                onChange={(e) => handleChange('overtimeRate', parseFloat(e.target.value) || 0)}
              />
            </div>
            
            <div className="grid gap-2">
              <label htmlFor="status">Status</label>
              <Select 
                value={formData.status} 
                onValueChange={(value) => handleChange('status', value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Active">Active</SelectItem>
                  <SelectItem value="Inactive">Inactive</SelectItem>
                  <SelectItem value="Discontinued">Discontinued</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>
        <DialogFooter className="flex justify-between">
          <Button 
            variant="destructive" 
            onClick={handleDelete} 
            disabled={loading}
          >
            Delete
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>Cancel</Button>
            <Button onClick={handleSubmit} disabled={loading}>
              {loading ? 'Updating...' : 'Update Brick Type'}
            </Button>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default EditBrickTypeDialog;
