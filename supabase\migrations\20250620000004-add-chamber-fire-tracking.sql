-- Create chamber_fire_status table for tracking which chamber the fire is currently burning in
CREATE TABLE IF NOT EXISTS public.chamber_fire_status (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  kiln_id TEXT NOT NULL REFERENCES public.kilns(id),
  chamber_number INT NOT NULL,
  is_burning BOOLEAN NOT NULL DEFAULT false,
  updated_by UUID REFERENCES public.users(id),
  UNIQUE(kiln_id, chamber_number)
);

-- Add RLS policies for chamber_fire_status
ALTER TABLE public.chamber_fire_status ENABLE ROW LEVEL SECURITY;

-- Policy to allow all users to view chamber fire status
CREATE POLICY "Allow all users to view chamber fire status" ON public.chamber_fire_status
  FOR SELECT USING (true);

-- Policy to allow authenticated users to update chamber fire status
CREATE POLICY "Allow authenticated users to update chamber fire status" ON public.chamber_fire_status
  FOR ALL WITH CHECK (true);

-- Create function to update chamber fire status
CREATE OR REPLACE FUNCTION public.update_chamber_fire_status(
  p_kiln_id TEXT,
  p_chamber_number INT,
  p_is_burning BOOLEAN,
  p_user_id UUID DEFAULT NULL
)
RETURNS void
LANGUAGE plpgsql
AS $$
BEGIN
  -- Insert or update the chamber status
  INSERT INTO public.chamber_fire_status (kiln_id, chamber_number, is_burning, updated_by, updated_at)
  VALUES (p_kiln_id, p_chamber_number, p_is_burning, p_user_id, NOW())
  ON CONFLICT (kiln_id, chamber_number)
  DO UPDATE SET 
    is_burning = EXCLUDED.is_burning,
    updated_by = EXCLUDED.updated_by,
    updated_at = NOW();
END;
$$;
