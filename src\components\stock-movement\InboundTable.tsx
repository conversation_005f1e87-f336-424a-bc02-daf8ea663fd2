
import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Check, Search, Filter } from "lucide-react";
import { format } from "date-fns";
import { InboundMovement } from "@/hooks/useStockMovement";

interface InboundTableProps {
  movements: InboundMovement[];
  isLoading: boolean;
  onMarkReceived: (id: string) => Promise<boolean>;
}

export const InboundTable: React.FC<InboundTableProps> = ({
  movements,
  isLoading,
  onMarkReceived
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "pending" | "received">("all");
  const [brickTypeFilter, setBrickTypeFilter] = useState<string>("all");

  // Get unique brick types for filter
  const brickTypes = Array.from(new Set(movements.map(m => m.brick_type)));

  // Filter movements
  const filteredMovements = movements.filter(movement => {
    const matchesSearch = 
      movement.brick_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.transporter.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.dnote.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = 
      statusFilter === "all" ||
      (statusFilter === "pending" && !movement.received) ||
      (statusFilter === "received" && movement.received);
    
    const matchesBrickType = 
      brickTypeFilter === "all" || movement.brick_type === brickTypeFilter;

    return matchesSearch && matchesStatus && matchesBrickType;
  });

  const handleMarkReceived = async (id: string) => {
    await onMarkReceived(id);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by brick type, transporter, or D-Note..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>
        
        <Select value={statusFilter} onValueChange={(value: "all" | "pending" | "received") => setStatusFilter(value)}>
          <SelectTrigger className="w-full sm:w-40">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Status</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
            <SelectItem value="received">Received</SelectItem>
          </SelectContent>
        </Select>

        <Select value={brickTypeFilter} onValueChange={setBrickTypeFilter}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="Brick Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {brickTypes.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Date</TableHead>
              <TableHead>Brick Type</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>D-Note</TableHead>
              <TableHead>Transporter</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMovements.length === 0 ? (
              <TableRow>
                <TableCell colSpan={7} className="text-center py-8 text-gray-500">
                  No inbound movements found
                </TableCell>
              </TableRow>
            ) : (
              filteredMovements.map((movement) => (
                <TableRow key={movement.id}>
                  <TableCell>
                    {format(new Date(movement.date), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell className="font-medium">
                    {movement.brick_type}
                  </TableCell>
                  <TableCell>
                    {movement.quantity.toLocaleString()}
                  </TableCell>
                  <TableCell>
                    {movement.dnote}
                  </TableCell>
                  <TableCell>
                    {movement.transporter}
                  </TableCell>
                  <TableCell>
                    <Badge 
                      variant={movement.received ? "default" : "secondary"}
                      className={movement.received ? "bg-green-100 text-green-800" : "bg-orange-100 text-orange-800"}
                    >
                      {movement.received ? "Received" : "Pending"}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {!movement.received && (
                      <Button
                        size="sm"
                        onClick={() => handleMarkReceived(movement.id)}
                        className="bg-green-600 hover:bg-green-700"
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Received
                      </Button>
                    )}
                    {movement.received && movement.received_at && (
                      <span className="text-sm text-gray-500">
                        {format(new Date(movement.received_at), 'MMM dd, HH:mm')}
                      </span>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Summary */}
      <div className="text-sm text-gray-600">
        Showing {filteredMovements.length} of {movements.length} inbound movements
      </div>
    </div>
  );
};
