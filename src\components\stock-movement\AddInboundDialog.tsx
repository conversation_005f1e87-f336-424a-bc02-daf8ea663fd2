
import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useStockMovement, NewInboundMovement } from "@/hooks/useStockMovement";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";
import { format } from "date-fns";

interface AddInboundDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAdd: (movement: NewInboundMovement) => Promise<boolean>;
}

export const AddInboundDialog: React.FC<AddInboundDialogProps> = ({
  open,
  onOpenChange,
  onAdd
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [brickTypes, setBrickTypes] = useState<ManagementBrickType[]>([]);
  const [isLoadingBrickTypes, setIsLoadingBrickTypes] = useState(false);
  const [formData, setFormData] = useState<NewInboundMovement>({
    date: format(new Date(), 'yyyy-MM-dd'),
    brick_type: "",
    quantity: 0,
    dnote: "",
    transporter: "",
    notes: ""
  });

  useEffect(() => {
    const fetchBrickTypes = async () => {
      setIsLoadingBrickTypes(true);
      try {
        const types = await getManagementBrickTypes();
        setBrickTypes(types.filter(type => type.status === 'Active'));
      } catch (error) {
        console.error('Error fetching brick types:', error);
      } finally {
        setIsLoadingBrickTypes(false);
      }
    };

    if (open) {
      fetchBrickTypes();
    }
  }, [open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const success = await onAdd(formData);
      if (success) {
        onOpenChange(false);
        // Reset form
        setFormData({
          date: format(new Date(), 'yyyy-MM-dd'),
          brick_type: "",
          quantity: 0,
          dnote: "",
          transporter: "",
          notes: ""
        });
      }
    } catch (error) {
      console.error('Error adding inbound movement:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleInputChange = (field: keyof NewInboundMovement, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add Inbound Delivery</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="date">Delivery Date</Label>
              <Input
                id="date"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                required
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="quantity">Quantity</Label>
              <Input
                id="quantity"
                type="number"
                min="1"
                value={formData.quantity || ''}
                onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                placeholder="Enter quantity"
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="brick_type">Brick Type</Label>
            <Select value={formData.brick_type} onValueChange={(value) => handleInputChange('brick_type', value)}>
              <SelectTrigger>
                <SelectValue placeholder={isLoadingBrickTypes ? "Loading..." : "Select brick type"} />
              </SelectTrigger>
              <SelectContent>
                {brickTypes.map(type => (
                  <SelectItem key={type.id} value={type.name}>{type.name}</SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="dnote">Delivery Note (D-Note)</Label>
            <Input
              id="dnote"
              value={formData.dnote}
              onChange={(e) => handleInputChange('dnote', e.target.value)}
              placeholder="Enter delivery note number"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="transporter">Transporter</Label>
            <Input
              id="transporter" 
              value={formData.transporter}
              onChange={(e) => handleInputChange('transporter', e.target.value)}
              placeholder="Enter transporter name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="notes">Notes (Optional)</Label>
            <Textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              placeholder="Additional notes..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => onOpenChange(false)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              disabled={isSubmitting || !formData.brick_type || !formData.transporter || !formData.dnote || formData.quantity <= 0}
            >
              {isSubmitting ? "Adding..." : "Add Delivery"}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  );
};
