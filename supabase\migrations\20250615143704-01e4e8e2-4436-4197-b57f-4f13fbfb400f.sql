
-- Table for outgoing pallet deliveries
CREATE TABLE public.pallet_movements (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  delivery_date DATE NOT NULL,
  delivery_note TEXT NOT NULL,
  vehicle_registration TEXT NOT NULL,
  product_type TEXT NOT NULL, -- string name, or brick type ID
  destination TEXT NOT NULL,
  pallets_loaded INTEGER NOT NULL CHECK (pallets_loaded > 0),
  status TEXT NOT NULL DEFAULT 'In Transit', -- "In Transit", "Delivered", "Pending Return", "Returned"
  comments TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Table for pallet returns
CREATE TABLE public.pallet_returns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  pallet_movement_id UUID NOT NULL REFERENCES public.pallet_movements(id) ON DELETE CASCADE,
  return_date DATE NOT NULL,
  pallets_returned INTEGER NOT NULL CHECK (pallets_returned > 0),
  condition TEXT NOT NULL, -- "good", "minor-damage", "major-damage", "unusable"
  comments TEXT,
  created_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS on both tables for future authentication
ALTER TABLE public.pallet_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.pallet_returns ENABLE ROW LEVEL SECURITY;

-- (Optional for now) Policies for SELECT/INSERT/UPDATE/DELETE
CREATE POLICY "Allow all for now" ON public.pallet_movements FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow all for now" ON public.pallet_returns FOR ALL USING (true) WITH CHECK (true);
