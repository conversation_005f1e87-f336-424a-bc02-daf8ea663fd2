import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Fuel, Database } from "lucide-react";
import { FuelManagementPage } from "./FuelManagementPage";
import { AssetsPage } from "./AssetsPage";

export const AssetManagementPage = () => {
  return (
    <div className="space-y-6">
      <div className="flex items-center gap-3">
        <Database className="h-8 w-8 text-slate-600" />
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Asset Management</h1>
          <p className="text-slate-600">Manage fuel and assets</p>
        </div>
      </div>

      <Tabs defaultValue="fuel" className="w-full">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="fuel" className="flex items-center gap-2">
            <Fuel size={16} />
            Fuel Management
          </TabsTrigger>
          <TabsTrigger value="assets" className="flex items-center gap-2">
            <Database size={16} />
            Assets
          </TabsTrigger>
        </TabsList>
        
        <TabsContent value="fuel" className="mt-6">
          <FuelManagementPage />
        </TabsContent>
        
        <TabsContent value="assets" className="mt-6">
          <AssetsPage />
        </TabsContent>
      </Tabs>
    </div>
  );
};
