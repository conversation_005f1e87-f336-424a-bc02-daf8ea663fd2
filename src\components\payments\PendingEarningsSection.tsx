
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Loader2, Receipt } from "lucide-react";
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useAddPayment } from "@/hooks/usePayments";
import { PendingEarnings } from "@/data/earningsCalculations";
import { NewPayment } from "@/data/paymentsData";

interface PendingEarningsSectionProps {
    pendingEarnings: PendingEarnings[];
    isLoading: boolean;
}

export const PendingEarningsSection = ({ pendingEarnings, isLoading }: PendingEarningsSectionProps) => {
    const { toast } = useToast();
    const addPaymentMutation = useAddPayment();

    const handleProcessPendingEarnings = (earning: PendingEarnings) => {
        const paymentsToCreate: NewPayment[] = [];
        const earningsByType = earning.breakdown.reduce((acc, item) => {
            const key = item.type;
            if (!acc[key]) acc[key] = 0;
            acc[key] += item.amount;
            return acc;
        }, {} as Record<string, number>);

        for (const [type, amount] of Object.entries(earningsByType)) {
            if (amount > 0.01) {
                paymentsToCreate.push({
                    employee_id: earning.employeeId,
                    amount: parseFloat(amount.toFixed(2)),
                    payment_type: type as NewPayment['payment_type'],
                    payment_date: new Date().toISOString().slice(0, 10),
                    status: 'Paid',
                    notes: `Automated payment for ${type} production.`,
                });
            }
        }

        if (paymentsToCreate.length > 0) {
            addPaymentMutation.mutate(paymentsToCreate, {
                onSuccess: () => {
                    toast({ title: "Success", description: `Pending earnings for ${earning.employeeName} processed.` });
                },
                onError: (error) => {
                    toast({ title: "Error", description: `Failed to process earnings: ${error.message}`, variant: "destructive" });
                }
            });
        } else {
            toast({ title: "Info", description: "No earnings to process." });
        }
    };
    
    return (
        <Card>
            <CardHeader>
                <CardTitle>Pending Earnings</CardTitle>
                <CardDescription>Earnings calculated from production data, ready to be paid.</CardDescription>
            </CardHeader>
            <CardContent>
                {isLoading ? (
                    <div className="flex justify-center items-center py-8">
                        <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
                    </div>
                ) : (
                    <>
                        <Accordion type="single" collapsible className="w-full">
                            {pendingEarnings.map(earning => (
                                <AccordionItem value={`item-${earning.employeeId}`} key={earning.employeeId}>
                                    <AccordionTrigger className="hover:no-underline">
                                        <div className="flex justify-between items-center w-full pr-4">
                                            <span className="font-semibold text-slate-700">{earning.employeeName}</span>
                                            <div className="flex items-center gap-4">
                                                <Badge variant="outline" className="text-green-600 border-green-600">R{earning.total.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</Badge>
                                            </div>
                                        </div>
                                    </AccordionTrigger>
                                    <AccordionContent>
                                        <div className="p-4 bg-slate-50 rounded-md">
                                            <h4 className="font-semibold mb-2 text-slate-600">Breakdown:</h4>
                                            <Table>
                                                <TableHeader>
                                                    <TableRow>
                                                        <TableHead>Date</TableHead>
                                                        <TableHead>Type</TableHead>
                                                        <TableHead>Details</TableHead>
                                                        <TableHead className="text-right">Amount</TableHead>
                                                    </TableRow>
                                                </TableHeader>
                                                <TableBody>
                                                    {earning.breakdown.map((item, index) => (
                                                        <TableRow key={`${item.sourceId}-${index}`}>
                                                            <TableCell>{item.date}</TableCell>
                                                            <TableCell><Badge variant="secondary">{item.type}</Badge></TableCell>
                                                            <TableCell className="text-sm text-slate-500">{item.details}</TableCell>
                                                            <TableCell className="text-right font-medium">R{item.amount.toLocaleString('en-ZA', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</TableCell>
                                                        </TableRow>
                                                    ))}
                                                </TableBody>
                                            </Table>
                                            <Button className="mt-4" onClick={() => handleProcessPendingEarnings(earning)} disabled={addPaymentMutation.isPending}>
                                                {addPaymentMutation.isPending ? <Loader2 className="mr-2 h-4 w-4 animate-spin" /> : <Receipt size={16} className="mr-2" />}
                                                Process Full Payment
                                            </Button>
                                        </div>
                                    </AccordionContent>
                                </AccordionItem>
                            ))}
                        </Accordion>
                        {pendingEarnings.length === 0 && !isLoading && (
                            <p className="text-center text-slate-500 py-4">No pending earnings to display.</p>
                        )}
                    </>
                )}
            </CardContent>
        </Card>
    );
};
