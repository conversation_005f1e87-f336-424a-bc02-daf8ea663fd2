
/**
 * Helper to determine if a time is considered 'day' or 'night' shift.
 * Day = 07:00-17:00 (7am to before 5pm), Night = otherwise
 */
export function getShiftType(hourString: string | undefined): "day" | "night" {
  if (!hourString) return "day";
  const hour = parseInt(hourString.split(':')[0], 10);
  // Day = 07:00-16:59, Night = 17:00-06:59
  return hour >= 7 && hour < 17 ? "day" : "night";
}
