
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Package2, Target } from "lucide-react";

interface StrappingCardProps {
  onManageStrapping: () => void;
}

export const StrappingCard = ({ onManageStrapping }: StrappingCardProps) => {
  return (
    <Card className="bg-gradient-to-br from-indigo-50 to-indigo-100 border-indigo-200">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-indigo-800">
          <Package2 size={20} />
          Strapping
        </CardTitle>
        <p className="text-sm text-indigo-600">Record strapped pallets production</p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-2 text-sm text-indigo-700">
          <div className="flex items-center gap-1">
            <Target size={14} />
            <span>20/hour target</span>
          </div>
          <div>
            <span>R1.50/pallet</span>
          </div>
        </div>
        
        <Button 
          onClick={onManageStrapping}
          className="w-full bg-indigo-600 hover:bg-indigo-700 text-white"
        >
          <Package2 size={16} className="mr-2" />
          Record Strapping
        </Button>
      </CardContent>
    </Card>
  );
};
