
import { TimeRange, CustomDateRange } from "./DashboardContent";
import { DateRangePicker } from "@/components/ui/date-range-picker";
import { DateRange } from "react-day-picker";
import { useState } from "react";

interface TimeRangeSelectorProps {
  selectedRange: TimeRange;
  onRangeChange: (range: TimeRange) => void;
  customDateRange?: CustomDateRange;
  onCustomDateChange?: (dateRange: CustomDateRange) => void;
}

export const TimeRangeSelector = ({ selectedRange, onRangeChange, customDateRange, onCustomDateChange }: TimeRangeSelectorProps) => {
  const ranges: { value: TimeRange; label: string }[] = [
    { value: "today", label: "Today" },
    { value: "week", label: "This Week" },
    { value: "month", label: "This Month" },
    { value: "year", label: "This Year" },
    { value: "custom", label: "Custom Range" },
  ];

  const handleDateRangeUpdate = (values: { range: DateRange; rangeCompare?: DateRange }) => {
    if (values.range.from && values.range.to && onCustomDateChange) {
      const customRange: CustomDateRange = {
        from: values.range.from.toISOString().split('T')[0],
        to: values.range.to.toISOString().split('T')[0]
      };
      onCustomDateChange(customRange);
    }
  };

  return (
    <div className="bg-white rounded-lg border border-slate-200 p-3 sm:p-4 w-full max-w-md mx-auto">
      <h3 className="text-base sm:text-lg font-semibold text-slate-800 mb-3 sm:mb-4 text-center">Time Range</h3>
      <div className="flex flex-wrap justify-center gap-3 sm:gap-4 mb-4">
        {ranges.map((range) => (
          <label key={range.value} className="flex items-center gap-2 cursor-pointer touch-manipulation">
            <input
              type="radio"
              name="timeRange"
              value={range.value}
              checked={selectedRange === range.value}
              onChange={() => onRangeChange(range.value)}
              className="w-4 h-4 text-slate-600"
            />
            <span className="text-sm sm:text-base text-slate-700 whitespace-nowrap">{range.label}</span>
          </label>
        ))}
      </div>
      
      {selectedRange === "custom" && (
        <div className="flex justify-center">
          <DateRangePicker
            onUpdate={handleDateRangeUpdate}
            initialDateFrom={customDateRange?.from}
            initialDateTo={customDateRange?.to}
            align="center"
          />
        </div>
      )}
    </div>
  );
};
