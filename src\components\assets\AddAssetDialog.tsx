
import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  <PERSON>alogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useToast } from "@/components/ui/use-toast";

interface AddAssetDialogProps {
  isOpen: boolean;
  onClose: () => void;
  onAddAsset: (asset: { name: string; type: string; identifier: string; status: "active" | "out_of_service" }) => void;
  isLoading?: boolean;
}

export const AddAssetDialog = ({ isOpen, onClose, onAddAsset, isLoading = false }: AddAssetDialogProps) => {
  const [name, setName] = useState("");
  const [type, setType] = useState("");
  const [identifier, setIdentifier] = useState("");
  const [status, setStatus] = useState<"active" | "out_of_service">("active");
  const { toast } = useToast();

  useEffect(() => {
    if (!isOpen) {
      setName("");
      setType("");
      setIdentifier("");
      setStatus("active");
    }
  }, [isOpen]);

  const handleSubmit = () => {
    if (!name || !type || !identifier) {
      toast({
        title: "Missing Information",
        description: "Please fill out all fields to add a new asset.",
        variant: "destructive",
      });
      return;
    }
    onAddAsset({ name, type, identifier, status });
    // Reset will be handled on close or after success
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Add New Asset</DialogTitle>
          <DialogDescription>
            Enter the details for the new asset. This will be used for tracking fuel dispensing.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="name">Asset Name</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="e.g., Main Generator"
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="type">Asset Type</Label>
            <Select value={type} onValueChange={setType} disabled={isLoading}>
              <SelectTrigger id="type">
                <SelectValue placeholder="Select asset type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Truck">Truck</SelectItem>
                <SelectItem value="Generator">Generator</SelectItem>
                <SelectItem value="Vehicle">Vehicle</SelectItem>
                <SelectItem value="Machinery">Machinery</SelectItem>
                <SelectItem value="Heavy Equipment">Heavy Equipment</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="identifier">Identifier</Label>
            <Input
              id="identifier"
              value={identifier}
              onChange={(e) => setIdentifier(e.target.value)}
              placeholder="e.g., License Plate, Serial No."
              disabled={isLoading}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="status">Status</Label>
            <Select value={status} onValueChange={v => setStatus(v as "active" | "out_of_service")} disabled={isLoading}>
              <SelectTrigger id="status">
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="active">Active</SelectItem>
                <SelectItem value="out_of_service">Out of Service</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={isLoading}>
            {isLoading ? "Adding..." : "Add Asset"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
