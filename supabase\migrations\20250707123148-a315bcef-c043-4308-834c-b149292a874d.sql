
-- Create a table for fire comments
CREATE TABLE public.fire_comments (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  fire_id TEXT NOT NULL REFERENCES public.fires(id),
  date DATE NOT NULL,
  comment TEXT NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id),
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  UNIQUE(fire_id, date)
);

-- Add Row Level Security (RLS)
ALTER TABLE public.fire_comments ENABLE ROW LEVEL SECURITY;

-- Create policies for fire comments
CREATE POLICY "Users can view all fire comments" 
  ON public.fire_comments 
  FOR SELECT 
  USING (true);

CREATE POLICY "Users can create fire comments" 
  ON public.fire_comments 
  FOR INSERT 
  WITH CHECK (true);

CREATE POLICY "Users can update fire comments" 
  ON public.fire_comments 
  FOR UPDATE 
  USING (true);

CREATE POLICY "Users can delete fire comments" 
  ON public.fire_comments 
  FOR DELETE 
  USING (true);

-- <PERSON><PERSON> updated_at trigger
CREATE OR REPLACE FUNCTION update_fire_comments_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_fire_comments_updated_at
    BEFORE UPDATE ON public.fire_comments
    FOR EACH ROW
    EXECUTE FUNCTION update_fire_comments_updated_at();
