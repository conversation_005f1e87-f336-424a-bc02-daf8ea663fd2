
import { supabase } from "@/integrations/supabase/client";
import { Database } from "@/integrations/supabase/types";

export type Fire = Database['public']['Tables']['fires']['Row'];

export const getFires = async (): Promise<Fire[]> => {
  const { data, error } = await supabase.from('fires').select('*');
  if (error) {
    console.error("Error fetching fires", error);
    throw new Error(error.message);
  }
  return data || [];
};

// Test function to debug the chamber fire status update
export const testChamberFireStatusUpdate = async (kilnId: string, chamberNumber: number, isBurning: boolean) => {
  console.log("🧪 Testing chamber fire status update with parameters:", { kilnId, chamberNumber, isBurning });
  
  try {
    // First, let's check if we can directly insert/update the table
    const { data: directUpdate, error: directError } = await supabase
      .from('chamber_fire_status')
      .upsert({
        kiln_id: kilnId,
        chamber_number: chamberNumber,
        is_burning: isBurning,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'kiln_id,chamber_number'
      })
      .select();
    
    if (directError) {
      console.error("❌ Direct upsert failed:", directError);
    } else {
      console.log("✅ Direct upsert succeeded:", directUpdate);
      return directUpdate;
    }
  } catch (error) {
    console.error("❌ Direct upsert exception:", error);
  }
  
  // If direct update fails, try the function
  try {
    const { data: funcData, error: funcError } = await supabase.rpc('update_chamber_fire_status', {
      p_kiln_id: kilnId,
      p_chamber_number: chamberNumber,
      p_is_burning: isBurning
    });
    
    if (funcError) {
      console.error("❌ Function call failed:", funcError);
      throw funcError;
    } else {
      console.log("✅ Function call succeeded:", funcData);
      return funcData;
    }
  } catch (error) {
    console.error("❌ Function call exception:", error);
    throw error;
  }
};
