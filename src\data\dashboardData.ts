
import { TimeRange } from "@/components/dashboard/DashboardContent";
import { getEmployees } from "./employeeData";
import { calculateGrossEarningsForPeriod } from "./earningsCalculations";
import { supabase } from "@/integrations/supabase/client";
import { startOfDay, endOfDay, startOfWeek, endOfWeek, startOfMonth, endOfMonth, startOfYear, endOfYear, format } from 'date-fns';

const getDateRange = (timeRange: TimeRange) => {
  const now = new Date();
  switch (timeRange) {
    case 'today':
      return { from: format(startOfDay(now), 'yyyy-MM-dd'), to: format(endOfDay(now), 'yyyy-MM-dd') };
    case 'week':
      return { from: format(startOfWeek(now, { weekStartsOn: 1 }), 'yyyy-MM-dd'), to: format(endOfWeek(now, { weekStartsOn: 1 }), 'yyyy-MM-dd') };
    case 'month':
      return { from: format(startOfMonth(now), 'yyyy-MM-dd'), to: format(endOfMonth(now), 'yyyy-MM-dd') };
    case 'year':
      return { from: format(startOfYear(now), 'yyyy-MM-dd'), to: format(endOfYear(now), 'yyyy-MM-dd') };
  }
};

export const getDashboardMetrics = async (timeRange: TimeRange) => {
    const { from, to } = getDateRange(timeRange);

    const [employees, settingPallets, dehackingPallets, factoryPallets, grossEarnings] = await Promise.all([
        getEmployees(),
        supabase.from('setting_production_entries').select('pallet_count', { count: 'exact' }).gte('date', from).lte('date', to),
        supabase.from('dehacking_entries').select('pallet_count', { count: 'exact' }).gte('date', from).lte('date', to),
        supabase.from('production_entries').select('pallet_count', { count: 'exact' }).gte('date', from).lte('date', to),
        calculateGrossEarningsForPeriod({ from, to })
    ]);

    if (settingPallets.error || dehackingPallets.error || factoryPallets.error) {
        console.error("Error fetching pallet data", settingPallets.error || dehackingPallets.error || factoryPallets.error);
        throw new Error("Could not fetch pallet data");
    }

    const totalEmployees = employees.length;
    const activeEmployees = employees.filter(e => e.status === 'Active').length;

    const totalSettingPallets = settingPallets.data?.reduce((sum, entry) => sum + (entry.pallet_count || 0), 0) || 0;
    const totalDehackingPallets = dehackingPallets.data?.reduce((sum, entry) => sum + (entry.pallet_count || 0), 0) || 0;
    const totalFactoryPallets = factoryPallets.data?.reduce((sum, entry) => sum + (entry.pallet_count || 0), 0) || 0;
    const totalPallets = totalSettingPallets + totalDehackingPallets + totalFactoryPallets;

    console.log('[Dashboard Metrics] Pallet breakdown:', {
        setting: totalSettingPallets,
        dehacking: totalDehackingPallets,
        factory: totalFactoryPallets,
        total: totalPallets,
        timeRange,
        dateRange: { from, to }
    });

    const projectedPayroll = grossEarnings.reduce((sum, e) => sum + e.total, 0);

    return {
        totalEmployees,
        activeEmployees,
        totalPallets,
        projectedPayroll
    };
};
