import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, <PERSON><PERSON>hart, Pie, Cell, BarChart, Bar, Legend } from "recharts";
import { TimeRange } from "./DashboardContent";
import { useAnalyticsData } from "@/hooks/useAnalyticsData";
import { Skeleton } from "@/components/ui/skeleton";
interface AnalyticsChartsProps {
  timeRange: TimeRange;
}
export const AnalyticsCharts = ({
  timeRange
}: AnalyticsChartsProps) => {
  const {
    data: analyticsData,
    isLoading
  } = useAnalyticsData(timeRange);
  const productionOverTimeData = analyticsData?.productionOverTime || [];
  const kilnProductionData = analyticsData?.kilnProduction || [];
  const totalKilnProduction = kilnProductionData.reduce((sum, item) => sum + item.value, 0);
  const renderNoData = (message: string) => <div className="flex items-center justify-center h-full text-slate-500">
      <div className="text-center">
        <p className="mb-2">{message}</p>
        <p className="text-sm">for the selected period</p>
      </div>
    </div>;
  return <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">Production Breakdown</CardTitle>
          <p className="text-sm text-slate-600">Factory, Setting & Dehacking pallets</p>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            {isLoading ? <Skeleton className="w-full h-full" /> : productionOverTimeData.length > 0 ? <ResponsiveContainer width="100%" height="100%">
                <LineChart data={productionOverTimeData}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="name" />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Line type="monotone" dataKey="factory" name="Factory" stroke="#ff7300" strokeWidth={2} dot={false} />
                  <Line type="monotone" dataKey="setting" name="Setting" stroke="#8884d8" strokeWidth={2} dot={false} />
                  <Line type="monotone" dataKey="dehacking" name="Dehacking" stroke="#82ca9d" strokeWidth={2} dot={false} />
                </LineChart>
              </ResponsiveContainer> : renderNoData("No production data available")}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-slate-800">Kiln Production Distribution</CardTitle>
          <p className="text-sm text-slate-600">Production distribution by kiln</p>
        </CardHeader>
        <CardContent>
          <div className="h-64">
            {isLoading ? <Skeleton className="w-full h-full" /> : kilnProductionData.length > 0 ? <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie data={kilnProductionData} cx="50%" cy="50%" outerRadius={80} fill="#8884d8" dataKey="value" labelLine={false} label={({
                name,
                value
              }) => `${name}: ${totalKilnProduction > 0 ? (value / totalKilnProduction * 100).toFixed(0) : 0}%`}>
                    {kilnProductionData.map((entry, index) => <Cell key={`cell-${index}`} fill={entry.color} />)}
                  </Pie>
                  <Tooltip formatter={(value: number) => `${value.toLocaleString()} pallets`} />
                </PieChart>
              </ResponsiveContainer> : renderNoData("No kiln production data available")}
          </div>
        </CardContent>
      </Card>

      
    </div>;
};