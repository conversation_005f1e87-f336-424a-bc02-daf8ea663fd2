
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Briefcase, Plus, Search, Clock } from "lucide-react";
import { Input } from "@/components/ui/input";

export const JobsPage = () => {
  const jobs = [
    { id: 1, title: "Daily Production Run", assignee: "<PERSON>", priority: "High", status: "In Progress", deadline: "Today" },
    { id: 2, title: "Quality Inspection - Batch 42", assignee: "<PERSON>", priority: "High", status: "Pending", deadline: "Tomorrow" },
    { id: 3, title: "Equipment Maintenance", assignee: "<PERSON>", priority: "Medium", status: "Scheduled", deadline: "Next Week" },
    { id: 4, title: "Safety Training Session", assignee: "<PERSON>", priority: "Low", status: "Planning", deadline: "Next Month" },
  ];

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Job Management</h1>
          <p className="text-slate-600">Track tasks, assignments, and deadlines</p>
        </div>
        <Button className="bg-slate-800 hover:bg-slate-700">
          <Plus size={20} className="mr-2" />
          Create Job
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-slate-800">12</div>
            <p className="text-slate-600">Total Jobs</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-blue-600">5</div>
            <p className="text-slate-600">In Progress</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-green-600">4</div>
            <p className="text-slate-600">Completed</p>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-6">
            <div className="text-2xl font-bold text-red-600">3</div>
            <p className="text-slate-600">Overdue</p>
          </CardContent>
        </Card>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Briefcase size={20} />
            Active Jobs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4 mb-6">
            <div className="relative flex-1">
              <Search size={16} className="absolute left-3 top-3 text-slate-400" />
              <Input 
                placeholder="Search jobs..." 
                className="pl-10"
              />
            </div>
            <Button variant="outline">Filter</Button>
          </div>

          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b">
                  <th className="text-left py-3 px-4">Job Title</th>
                  <th className="text-left py-3 px-4">Assignee</th>
                  <th className="text-left py-3 px-4">Priority</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Deadline</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobs.map((job) => (
                  <tr key={job.id} className="border-b hover:bg-slate-50">
                    <td className="py-3 px-4 font-medium">{job.title}</td>
                    <td className="py-3 px-4">{job.assignee}</td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        job.priority === 'High' 
                          ? 'bg-red-100 text-red-800' 
                          : job.priority === 'Medium'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-green-100 text-green-800'
                      }`}>
                        {job.priority}
                      </span>
                    </td>
                    <td className="py-3 px-4">
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        job.status === 'In Progress' 
                          ? 'bg-blue-100 text-blue-800' 
                          : job.status === 'Pending'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-slate-100 text-slate-800'
                      }`}>
                        {job.status}
                      </span>
                    </td>
                    <td className="py-3 px-4 flex items-center gap-1">
                      <Clock size={14} />
                      {job.deadline}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">Edit</Button>
                        <Button variant="outline" size="sm">View</Button>
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
