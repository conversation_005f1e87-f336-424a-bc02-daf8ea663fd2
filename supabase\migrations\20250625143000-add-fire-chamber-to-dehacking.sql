-- Add fire_id and chamber_number columns to dehacking_entries table
ALTER TABLE public.dehacking_entries
ADD COLUMN IF NOT EXISTS fire_id UUID REFERENCES public.fires(id),
ADD COLUMN IF NOT EXISTS chamber_number INTEGER;

-- Add comment to explain the purpose of these columns
COMMENT ON COLUMN public.dehacking_entries.fire_id IS 'Reference to the fire associated with this dehacking entry';
COMMENT ON COLUMN public.dehacking_entries.chamber_number IS 'The chamber number associated with this dehacking entry';

-- Update RLS policies to include the new columns
ALTER POLICY "Enable read access for all users" ON "public"."dehacking_entries"
  USING (true);

ALTER POLICY "Enable insert for authenticated users only" ON "public"."dehacking_entries"
  WITH CHECK (auth.role() = 'authenticated');

ALTER POLICY "Enable update for authenticated users only" ON "public"."dehacking_entries"
  USING (auth.role() = 'authenticated')
  WITH CHECK (auth.role() = 'authenticated');

ALTER POLICY "Enable delete for authenticated users only" ON "public"."dehacking_entries"
  USING (auth.role() = 'authenticated');
