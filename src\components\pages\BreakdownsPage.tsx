
import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { CalendarIcon, Plus, Trash2, Edit, Clock, AlertTriangle, CheckCircle } from 'lucide-react';
import { toast } from 'sonner';
import { useBreakdowns, useAddBreakdown, useUpdateBreakdown, useDeleteBreakdown } from '@/hooks/useBreakdowns';
import { useAuth } from '@/contexts/AuthContext';

export const BreakdownsPage = () => {
  const { currentUser } = useAuth();
  const { data: breakdowns = [], isLoading } = useBreakdowns();
  const addBreakdownMutation = useAddBreakdown();
  const updateBreakdownMutation = useUpdateBreakdown();
  const deleteBreakdownMutation = useDeleteBreakdown();
  
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [editingBreakdown, setEditingBreakdown] = useState<any>(null);
  const [formData, setFormData] = useState({
    date: new Date().toISOString().slice(0, 10),
    stop_time: '',
    start_time: '',
    breakdown_comment: ''
  });

  const resetForm = () => {
    setFormData({
      date: new Date().toISOString().slice(0, 10),
      stop_time: '',
      start_time: '',
      breakdown_comment: ''
    });
    setEditingBreakdown(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.date || !formData.breakdown_comment || !formData.stop_time) {
      toast.error('Please fill in date, stop time, and breakdown comment');
      return;
    }

    try {
      if (editingBreakdown) {
        await updateBreakdownMutation.mutateAsync({
          id: editingBreakdown.id,
          date: formData.date,
          stop_time: formData.stop_time,
          start_time: formData.start_time || null,
          breakdown_comment: formData.breakdown_comment
        });
        toast.success('Breakdown updated successfully');
      } else {
        await addBreakdownMutation.mutateAsync({
          date: formData.date,
          stop_time: formData.stop_time,
          start_time: formData.start_time || null,
          breakdown_comment: formData.breakdown_comment
        });
        toast.success('Breakdown recorded successfully');
      }
      
      resetForm();
      setIsFormOpen(false);
    } catch (error) {
      console.error('Form submission error:', error);
      toast.error(editingBreakdown ? 'Failed to update breakdown' : 'Failed to record breakdown');
    }
  };

  const handleEdit = (breakdown: any) => {
    setEditingBreakdown(breakdown);
    setFormData({
      date: breakdown.date,
      stop_time: breakdown.stop_time,
      start_time: breakdown.start_time || '',
      breakdown_comment: breakdown.breakdown_comment
    });
    setIsFormOpen(true);
  };

  const handleDelete = async (id: number) => {
    if (window.confirm('Are you sure you want to delete this breakdown?')) {
      try {
        await deleteBreakdownMutation.mutateAsync(id);
        toast.success('Breakdown deleted successfully');
      } catch (error) {
        toast.error('Failed to delete breakdown');
      }
    }
  };

  const handleMarkAsResolved = async (breakdown: any) => {
    const currentTime = new Date().toTimeString().slice(0, 5); // Get current time in HH:MM format

    try {
      await updateBreakdownMutation.mutateAsync({
        id: breakdown.id,
        date: breakdown.date,
        stop_time: breakdown.stop_time,
        start_time: currentTime,
        breakdown_comment: breakdown.breakdown_comment
      });
      toast.success('Breakdown marked as resolved');
    } catch (error) {
      toast.error('Failed to mark breakdown as resolved');
    }
  };

  const calculateDowntime = (stopTime: string, startTime: string | null, date: string) => {
    if (!startTime) return null;
    
    const stopDateTime = new Date(`${date}T${stopTime}`);
    const startDateTime = new Date(`${date}T${startTime}`);
    
    // If start time is earlier than stop time, assume it's the next day
    if (startDateTime < stopDateTime) {
      startDateTime.setDate(startDateTime.getDate() + 1);
    }
    
    const diffMs = startDateTime.getTime() - stopDateTime.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffMinutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${diffHours}h ${diffMinutes}m`;
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-800">Breakdowns</h1>
          <p className="text-slate-600">Track and manage equipment breakdowns</p>
        </div>
        <Button 
          onClick={() => {
            resetForm();
            setIsFormOpen(true);
          }}
          className="bg-red-500 hover:bg-red-600"
        >
          <Plus className="h-4 w-4 mr-2" />
          Record Breakdown
        </Button>
      </div>

      {isFormOpen && (
        <Card>
          <CardHeader>
            <CardTitle>{editingBreakdown ? 'Edit Breakdown' : 'Record Breakdown'}</CardTitle>
            <CardDescription>
              {editingBreakdown 
                ? 'Update the breakdown details and optionally add the start time when the asset becomes operational again' 
                : 'Enter the details of the breakdown. The start time can be added later when the asset is operational again'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="date">Date</Label>
                <div className="relative">
                  <Input 
                    type="date" 
                    id="date" 
                    value={formData.date} 
                    onChange={(e) => setFormData({ ...formData, date: e.target.value })} 
                    className="pr-10"
                  />
                  <CalendarIcon className="absolute right-2 top-1/2 -translate-y-1/2 text-gray-400 w-4 h-4 pointer-events-none" />
                </div>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="stop_time" className="flex items-center gap-2">
                  <AlertTriangle className="h-4 w-4 text-red-500" />
                  Stop Time (When asset stopped working) *
                </Label>
                <Input 
                  type="time" 
                  id="stop_time" 
                  value={formData.stop_time} 
                  onChange={(e) => setFormData({ ...formData, stop_time: e.target.value })} 
                  placeholder="Time when breakdown occurred" 
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="start_time" className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-green-500" />
                  Start Time (When asset became operational again)
                </Label>
                <Input 
                  type="time" 
                  id="start_time" 
                  value={formData.start_time} 
                  onChange={(e) => setFormData({ ...formData, start_time: e.target.value })} 
                  placeholder="Leave empty if asset is still down" 
                />
                <p className="text-sm text-gray-500">
                  Optional - can be added later when the asset is repaired and operational
                </p>
              </div>

              <div className="space-y-2">
                <Label htmlFor="breakdown_comment">Breakdown Comment *</Label>
                <Textarea
                  id="breakdown_comment"
                  placeholder="Describe the breakdown issue, cause, and any actions taken"
                  value={formData.breakdown_comment}
                  onChange={(e) => setFormData({ ...formData, breakdown_comment: e.target.value })}
                  required
                />
              </div>
              
              <div className="flex justify-end space-x-2">
                <Button 
                  type="button" 
                  variant="secondary" 
                  onClick={() => {
                    setIsFormOpen(false);
                    resetForm();
                  }}
                >
                  Cancel
                </Button>
                <Button type="submit" className="bg-red-500 hover:bg-red-600">
                  {editingBreakdown ? 'Update' : 'Record'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Breakdown History</CardTitle>
          <CardDescription>List of recorded breakdowns with downtime tracking</CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <p>Loading breakdowns...</p>
          ) : (
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Date
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Stop Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Start Time
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Downtime
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Status
                    </th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Comment
                    </th>
                    <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {breakdowns.map((breakdown) => (
                    <tr key={breakdown.id}>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {breakdown.date}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className="flex items-center gap-1">
                          <AlertTriangle className="h-3 w-3 text-red-500" />
                          {breakdown.stop_time}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {breakdown.start_time ? (
                          <span className="flex items-center gap-1">
                            <Clock className="h-3 w-3 text-green-500" />
                            {breakdown.start_time}
                          </span>
                        ) : (
                          <span className="text-gray-400 italic">Not restored</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {breakdown.start_time ? (
                          <span className="text-sm font-medium">
                            {calculateDowntime(breakdown.stop_time, breakdown.start_time, breakdown.date)}
                          </span>
                        ) : (
                          <span className="text-orange-500 text-sm">Ongoing</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        {breakdown.start_time ? (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                            Resolved
                          </span>
                        ) : (
                          <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                            Unresolved
                          </span>
                        )}
                      </td>
                      <td className="px-6 py-4 max-w-xs">
                        <div className="truncate" title={breakdown.breakdown_comment}>
                          {breakdown.breakdown_comment}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right space-x-2">
                        {!breakdown.start_time && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleMarkAsResolved(breakdown)}
                            className="text-green-500 hover:bg-green-100"
                            title="Mark as resolved with current time"
                          >
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEdit(breakdown)}
                          className="text-blue-500 hover:bg-blue-100"
                          title="Edit breakdown details"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(breakdown.id)}
                          className="text-red-500 hover:bg-red-100"
                          title="Delete breakdown"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
