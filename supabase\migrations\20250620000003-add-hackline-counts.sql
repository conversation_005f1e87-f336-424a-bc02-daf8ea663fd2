-- Create hackline_counts table for tracking brick counts drying in the sun
CREATE TABLE public.hackline_counts (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE NOT NULL,
  count_total INT NOT NULL,
  user_id UUID NOT NULL REFERENCES public.users(id),
  notes TEXT
);

-- Add RLS policies for hackline_counts
ALTER TABLE public.hackline_counts ENABLE ROW LEVEL SECURITY;

-- Policy to allow users to view all hackline counts (for supervisors)
CREATE POLICY "Allow all users to view hackline counts" ON public.hackline_counts
  FOR SELECT USING (true);

-- Policy to allow authenticated users to insert hackline counts
CREATE POLICY "Allow authenticated users to insert hackline counts" ON public.hackline_counts
  FOR INSERT WITH CHECK (true);

-- Policy to allow authenticated users to update hackline counts
CREATE POLICY "Allow authenticated users to update hackline counts" ON public.hackline_counts
  FOR UPDATE USING (true);
