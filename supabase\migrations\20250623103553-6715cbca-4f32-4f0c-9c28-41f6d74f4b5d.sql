
-- Create a storage bucket for company logos
INSERT INTO storage.buckets (id, name, public)
VALUES ('company-logos', 'company-logos', true);

-- Create policy to allow authenticated users to upload logos
CREATE POLICY "Allow authenticated users to upload company logos" ON storage.objects
FOR INSERT WITH CHECK (
  bucket_id = 'company-logos' AND
  auth.role() = 'authenticated'
);

-- Create policy to allow public read access to logos
CREATE POLICY "Allow public read access to company logos" ON storage.objects
FOR SELECT USING (bucket_id = 'company-logos');

-- Create policy to allow authenticated users to update logos
CREATE POLICY "Allow authenticated users to update company logos" ON storage.objects
FOR UPDATE USING (
  bucket_id = 'company-logos' AND
  auth.role() = 'authenticated'
);

-- Add logo_url column to system_config table
ALTER TABLE system_config ADD COLUMN logo_url TEXT;
