import { useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { useUser } from '@/contexts/UserContext';
import { toast } from 'sonner';

export interface InboundMovement {
  id: string;
  date: string;
  brick_type: string;
  quantity: number;
  dnote: string;
  transporter: string;
  received: boolean;
  received_at?: string;
  received_by?: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface OutboundMovement {
  id: string;
  sold_date: string;
  brick_type: string;
  quantity: number;
  client_name: string;
  delivery_note: string;
  notes?: string;
  created_at: string;
  updated_at: string;
  user_id: string;
}

export interface NewInboundMovement {
  date: string;
  brick_type: string;
  quantity: number;
  dnote: string;
  transporter: string;
  notes?: string;
}

export interface NewOutboundMovement {
  sold_date: string;
  brick_type: string;
  quantity: number;
  client_name: string;
  delivery_note: string;
  notes?: string;
}

export const useStockMovement = () => {
  const [inboundMovements, setInboundMovements] = useState<InboundMovement[]>([]);
  const [outboundMovements, setOutboundMovements] = useState<OutboundMovement[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { currentUser } = useUser();

  // Fetch inbound movements
  const fetchInboundMovements = async () => {
    try {
      const { data, error } = await supabase
        .from('stock_movement_inbound')
        .select('*')
        .order('date', { ascending: false });

      if (error) throw error;
      setInboundMovements(data || []);
    } catch (error) {
      console.error('Error fetching inbound movements:', error);
      toast.error('Failed to fetch inbound movements');
    }
  };

  // Fetch outbound movements
  const fetchOutboundMovements = async () => {
    try {
      const { data, error } = await supabase
        .from('stock_movement_outbound')
        .select('*')
        .order('sold_date', { ascending: false });

      if (error) throw error;
      setOutboundMovements(data || []);
    } catch (error) {
      console.error('Error fetching outbound movements:', error);
      toast.error('Failed to fetch outbound movements');
    }
  };

  // Initial data fetch
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      await Promise.all([fetchInboundMovements(), fetchOutboundMovements()]);
      setIsLoading(false);
    };

    fetchData();
  }, []);

  const addInboundMovement = async (movement: NewInboundMovement) => {
    if (!currentUser?.id) {
      toast.error('User not authenticated');
      return false;
    }

    try {
      const { error } = await supabase
        .from('stock_movement_inbound')
        .insert({
          ...movement,
          user_id: currentUser.id
        });

      if (error) throw error;

      toast.success('Inbound movement added successfully');
      await fetchInboundMovements();
      return true;
    } catch (error) {
      console.error('Error adding inbound movement:', error);
      toast.error('Failed to add inbound movement');
      return false;
    }
  };

  const addOutboundMovement = async (movement: NewOutboundMovement) => {
    if (!currentUser?.id) {
      toast.error('User not authenticated');
      return false;
    }

    try {
      const { error } = await supabase
        .from('stock_movement_outbound')
        .insert({
          ...movement,
          user_id: currentUser.id
        });

      if (error) throw error;

      toast.success('Outbound movement added successfully');
      await fetchOutboundMovements();
      return true;
    } catch (error) {
      console.error('Error adding outbound movement:', error);
      toast.error('Failed to add outbound movement');
      return false;
    }
  };

  const markAsReceived = async (id: string) => {
    if (!currentUser?.id) {
      toast.error('User not authenticated');
      return false;
    }

    try {
      const { error } = await supabase
        .from('stock_movement_inbound')
        .update({
          received: true,
          received_at: new Date().toISOString(),
          received_by: currentUser.id
        })
        .eq('id', id);

      if (error) throw error;

      toast.success('Movement marked as received');
      await fetchInboundMovements();
      return true;
    } catch (error) {
      console.error('Error marking as received:', error);
      toast.error('Failed to mark as received');
      return false;
    }
  };

  const deleteInboundMovement = async (id: string) => {
    try {
      const { error } = await supabase
        .from('stock_movement_inbound')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast.success('Inbound movement deleted');
      await fetchInboundMovements();
      return true;
    } catch (error) {
      console.error('Error deleting inbound movement:', error);
      toast.error('Failed to delete inbound movement');
      return false;
    }
  };

  const deleteOutboundMovement = async (id: string) => {
    try {
      const { error } = await supabase
        .from('stock_movement_outbound')
        .delete()
        .eq('id', id);

      if (error) throw error;

      toast.success('Outbound movement deleted');
      await fetchOutboundMovements();
      return true;
    } catch (error) {
      console.error('Error deleting outbound movement:', error);
      toast.error('Failed to delete outbound movement');
      return false;
    }
  };

  const refetch = async () => {
    setIsLoading(true);
    await Promise.all([fetchInboundMovements(), fetchOutboundMovements()]);
    setIsLoading(false);
  };

  return {
    inboundMovements,
    outboundMovements,
    isLoading,
    addInboundMovement,
    addOutboundMovement,
    markAsReceived,
    deleteInboundMovement,
    deleteOutboundMovement,
    refetch
  };
};