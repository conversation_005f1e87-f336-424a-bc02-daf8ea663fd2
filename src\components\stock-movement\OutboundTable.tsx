
import React, { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Search } from "lucide-react";
import { format } from "date-fns";
import { OutboundMovement } from "@/hooks/useStockMovement";

interface OutboundTableProps {
  movements: OutboundMovement[];
  isLoading: boolean;
}

export const OutboundTable: React.FC<OutboundTableProps> = ({
  movements,
  isLoading
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [brickTypeFilter, setBrickTypeFilter] = useState<string>("all");

  // Get unique brick types for filter
  const brickTypes = Array.from(new Set(movements.map(m => m.brick_type)));

  // Filter movements
  const filteredMovements = movements.filter(movement => {
    const matchesSearch = 
      movement.brick_type.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.client_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      movement.delivery_note.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesBrickType = 
      brickTypeFilter === "all" || movement.brick_type === brickTypeFilter;

    return matchesSearch && matchesBrickType;
  });

  if (isLoading) {
    return (
      <div className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-slate-700"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by brick type, client name, or delivery note..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10"
          />
        </div>

        <Select value={brickTypeFilter} onValueChange={setBrickTypeFilter}>
          <SelectTrigger className="w-full sm:w-40">
            <SelectValue placeholder="Brick Type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Types</SelectItem>
            {brickTypes.map(type => (
              <SelectItem key={type} value={type}>{type}</SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      {/* Table */}
      <div className="border rounded-lg overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Sold Date</TableHead>
              <TableHead>Brick Type</TableHead>
              <TableHead>Quantity</TableHead>
              <TableHead>Client Name</TableHead>
              <TableHead>Delivery Note</TableHead>
              <TableHead>Notes</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredMovements.length === 0 ? (
              <TableRow>
                <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                  No outbound movements found
                </TableCell>
              </TableRow>
            ) : (
              filteredMovements.map((movement) => (
                <TableRow key={movement.id}>
                  <TableCell>
                    {format(new Date(movement.sold_date), 'MMM dd, yyyy')}
                  </TableCell>
                  <TableCell className="font-medium">
                    {movement.brick_type}
                  </TableCell>
                  <TableCell>
                    <Badge variant="outline" className="bg-red-50 text-red-700 border-red-200">
                      -{movement.quantity.toLocaleString()}
                    </Badge>
                  </TableCell>
                  <TableCell>
                    {movement.client_name}
                  </TableCell>
                  <TableCell>
                    {movement.delivery_note}
                  </TableCell>
                  <TableCell>
                    {movement.notes && (
                      <span className="text-sm text-gray-600">
                        {movement.notes}
                      </span>
                    )}
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Summary */}
      <div className="text-sm text-gray-600">
        Showing {filteredMovements.length} of {movements.length} outbound movements
      </div>
    </div>
  );
};
