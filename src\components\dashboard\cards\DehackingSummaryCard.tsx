
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { PackageOpen, Loader2 } from "lucide-react";
import { TimeRange } from "../DashboardContent";
import { getDehackingBreakdown, subscribeToDehacking } from "@/data/dehackingStore";
import { isToday, isThisWeek, isThisMonth, startOfYear } from "date-fns";
import { useQuery } from "@tanstack/react-query";
import { getManagementBrickTypes, ManagementBrickType } from "@/data/managementBrickTypes";

interface DehackingSummaryCardProps {
  timeRange: TimeRange;
}

export const DehackingSummaryCard = ({ timeRange }: DehackingSummaryCardProps) => {
  const { data: allBrickTypes = [] } = useQuery<ManagementBrickType[]>({ 
    queryKey: ['managementBrickTypes'], 
    queryFn: getManagementBrickTypes 
  });
  
  const timeFilter = (date: Date): boolean => {
    switch (timeRange) {
      case "today": return isToday(date);
      case "week": return isThisWeek(date, { weekStartsOn: 1 });
      case "month": return isThisMonth(date);
      case "year": return date >= startOfYear(new Date());
      default: return true;
    }
  };
  
  const { data: breakdownData, isLoading } = useQuery({
    queryKey: ['dehackingBreakdown', timeRange, allBrickTypes.length],
    queryFn: () => getDehackingBreakdown(timeFilter, allBrickTypes),
    enabled: !!allBrickTypes.length,
    refetchInterval: 5000, // Refetch data every 5 seconds
  });

  const { typeBreakdown = [] } = breakdownData || {};
  const periodText = { today: "Today", week: "This Week", month: "This Month", year: "This Year" }[timeRange];

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg font-semibold text-slate-800">Dehacking Breakdown ({periodText})</CardTitle>
        <p className="text-sm text-slate-600">Production breakdown by brick type</p>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex justify-center items-center py-10">
            <Loader2 className="h-6 w-6 animate-spin text-slate-400" />
          </div>
        ) : (
          <div>
            <h4 className="flex items-center gap-2 font-semibold text-slate-700 mb-3">
              <PackageOpen size={16} /> 
              By Brick Type
            </h4>
            {typeBreakdown.length > 0 ? (
              <div className="space-y-2">
                {typeBreakdown.map(type => (
                  <div key={type.id} className="flex justify-between items-center py-2 px-3 bg-slate-50 rounded-md">
                    <span className="text-slate-700 font-medium">{type.name}</span>
                    <span className="font-semibold text-slate-800">{type.bricks.toLocaleString()} bricks</span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <PackageOpen className="h-12 w-12 text-slate-300 mx-auto mb-3" />
                <p className="text-slate-500 font-medium">No dehacking data</p>
                <p className="text-sm text-slate-400">for the selected period</p>
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
