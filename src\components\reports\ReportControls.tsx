
import { TimeRangeSelector } from "@/components/dashboard/TimeRangeSelector";
import { TimeRange, CustomDateRange } from "@/components/dashboard/DashboardContent";
import { ReportType } from "@/components/pages/ReportsPage";

interface ReportControlsProps {
  selectedTimeRange: TimeRange;
  onTimeRangeChange: (range: TimeRange) => void;
  selectedReportType: ReportType;
  onReportTypeChange: (type: ReportType) => void;
  reportTypes: { value: ReportType; label: string }[];
  customDateRange?: CustomDateRange;
  onCustomDateChange?: (dateRange: CustomDateRange) => void;
}

export const ReportControls = ({
  selectedTimeRange,
  onTimeRangeChange,
  selectedReportType,
  onReportTypeChange,
  reportTypes,
  customDateRange,
  onCustomDateChange,
}: ReportControlsProps) => {
  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <TimeRangeSelector 
        selectedRange={selectedTimeRange}
        onRangeChange={onTimeRangeChange}
        customDateRange={customDateRange}
        onCustomDateChange={onCustomDateChange}
      />
      
      <div className="bg-white rounded-lg border border-slate-200 p-4">
        <h3 className="text-lg font-semibold text-slate-800 mb-4">Report Type</h3>
        <div className="grid grid-cols-1 gap-2">
          {reportTypes.map((type) => (
            <label key={type.value} className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="reportType"
                value={type.value}
                checked={selectedReportType === type.value}
                onChange={() => onReportTypeChange(type.value)}
                className="w-4 h-4 text-slate-600"
              />
              <span className="text-slate-700">{type.label}</span>
            </label>
          ))}
        </div>
      </div>
    </div>
  );
};
