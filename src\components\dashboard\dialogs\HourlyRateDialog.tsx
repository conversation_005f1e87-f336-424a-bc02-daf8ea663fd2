
import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>Title } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useQuery } from "@tanstack/react-query";
import { getTeamMembers, getTeamsWithMembers } from "@/data/teamData";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "sonner";

interface HourlyRateDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

const HOURLY_RATE = 28.79;

export const HourlyRateDialog = ({ isOpen, onClose }: HourlyRateDialogProps) => {
  const [selectedTeam, setSelectedTeam] = useState<string>("");
  const [selectedEmployee, setSelectedEmployee] = useState<string>("");
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [startTime, setStartTime] = useState("");
  const [stopTime, setStopTime] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Get all teams from the database
  const { data: allTeams = [] } = useQuery({
    queryKey: ['teamsWithMembers'],
    queryFn: getTeamsWithMembers
  });

  // Get team members for the selected team
  const { data: teamMembers = [] } = useQuery({
    queryKey: ['team-members', selectedTeam],
    queryFn: () => getTeamMembers(selectedTeam),
    enabled: !!selectedTeam
  });

  const calculateHours = () => {
    if (!startTime || !stopTime) return 0;
    const start = new Date(`${date}T${startTime}`);
    const stop = new Date(`${date}T${stopTime}`);
    const diffMs = Math.abs(stop.getTime() - start.getTime());
    return diffMs / (1000 * 60 * 60);
  };

  const calculatePay = () => {
    return calculateHours() * HOURLY_RATE;
  };

  const handleSubmit = async () => {
    if (!selectedTeam || !selectedEmployee || !date || !startTime || !stopTime) {
      toast.error("Please fill in all fields");
      return;
    }

    const hours = calculateHours();
    if (hours <= 0) {
      toast.error("Stop time must be after start time");
      return;
    }

    const pay = calculatePay();
    setIsSubmitting(true);

    try {
      const { error } = await supabase
        .from('hourly_rate_records')
        .insert({
          employee_id: parseInt(selectedEmployee),
          team_id: selectedTeam,
          date: date,
          start_time: startTime,
          stop_time: stopTime,
          hours_worked: hours,
          hourly_rate: HOURLY_RATE,
          total_pay: pay
        });

      if (error) {
        console.error('Error saving hourly rate record:', error);
        toast.error("Failed to save hourly rate record");
        return;
      }

      toast.success(`Recorded ${hours.toFixed(2)} hours for R${pay.toFixed(2)}`);
      
      // Reset form
      setSelectedTeam("");
      setSelectedEmployee("");
      setDate(new Date().toISOString().split('T')[0]);
      setStartTime("");
      setStopTime("");
      onClose();
    } catch (error) {
      console.error('Error saving hourly rate record:', error);
      toast.error("Failed to save hourly rate record");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Record Hourly Work</DialogTitle>
        </DialogHeader>
        
        <div className="space-y-4">
          <div>
            <Label htmlFor="date">Date</Label>
            <Input
              id="date"
              type="date"
              value={date}
              onChange={(e) => setDate(e.target.value)}
            />
          </div>

          <div>
            <Label htmlFor="team">Team</Label>
            <Select value={selectedTeam} onValueChange={(value) => {
              setSelectedTeam(value);
              setSelectedEmployee(""); // Reset employee when team changes
            }}>
              <SelectTrigger>
                <SelectValue placeholder="Select team" />
              </SelectTrigger>
              <SelectContent>
                {allTeams.map((team) => (
                  <SelectItem key={team.id} value={team.id}>
                    {team.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="employee">Employee</Label>
            <Select value={selectedEmployee} onValueChange={setSelectedEmployee} disabled={!selectedTeam}>
              <SelectTrigger>
                <SelectValue placeholder="Select employee" />
              </SelectTrigger>
              <SelectContent>
                {teamMembers.map((member) => (
                  <SelectItem key={member.id} value={member.id.toString()}>
                    {member.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startTime">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={startTime}
                onChange={(e) => setStartTime(e.target.value)}
              />
            </div>
            <div>
              <Label htmlFor="stopTime">Stop Time</Label>
              <Input
                id="stopTime"
                type="time"
                value={stopTime}
                onChange={(e) => setStopTime(e.target.value)}
              />
            </div>
          </div>

          {startTime && stopTime && (
            <div className="bg-gray-50 p-3 rounded-md">
              <div className="text-sm text-gray-600">
                Hours: {calculateHours().toFixed(2)}h
              </div>
              <div className="text-sm text-gray-600">
                Rate: R{HOURLY_RATE}/hour
              </div>
              <div className="text-sm font-medium">
                Pay: R{calculatePay().toFixed(2)}
              </div>
            </div>
          )}

          <div className="flex gap-2 pt-4">
            <Button variant="outline" onClick={onClose} className="flex-1" disabled={isSubmitting}>
              Cancel
            </Button>
            <Button onClick={handleSubmit} className="flex-1" disabled={isSubmitting}>
              {isSubmitting ? "Recording..." : "Record Hours"}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
