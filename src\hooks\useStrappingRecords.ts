
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface StrappingRecord {
  id: string;
  employee_id: number;
  date: string;
  pallet_count: number;
  rate_per_pallet: number;
  total_pay: number;
  created_at: string;
  updated_at: string;
}

export const useStrappingRecords = (dateFilter?: string) => {
  return useQuery({
    queryKey: ['strapping-records', dateFilter],
    queryFn: async () => {
      let query = supabase
        .from('strapping_records')
        .select('*')
        .order('created_at', { ascending: false });

      if (dateFilter) {
        query = query.eq('date', dateFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching strapping records:', error);
        throw error;
      }

      return data as StrappingRecord[];
    },
  });
};
