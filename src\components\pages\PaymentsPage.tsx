
import { useState } from "react";
import { Loader2 } from "lucide-react";
import { usePayments } from "@/hooks/usePayments";
import { useEmployees } from "@/hooks/useEmployees";
import { usePendingEarnings } from "@/hooks/useEarnings";
import { PaymentWithEmployeeName } from "@/data/paymentsData";
import { PaymentsHeader } from "../payments/PaymentsHeader";
import { PaymentsStatsCards } from "../payments/PaymentsStatsCards";
import { PendingEarningsSection } from "../payments/PendingEarningsSection";
import { RecentPaymentsSection } from "../payments/RecentPaymentsSection";
import { ViewPaymentDialog } from "../payments/ViewPaymentDialog";
import { Employee } from "@/data/employeeData";

export const PaymentsPage = () => {
  const [viewedPayment, setViewedPayment] = useState<PaymentWithEmployeeName | null>(null);

  const { data: payments = [], isLoading: isLoadingPayments } = usePayments();
  const { data: employees = [], isLoading: isLoadingEmployees } = useEmployees();
  const { data: pendingEarnings = [], isLoading: isLoadingPendingEarnings } = usePendingEarnings();

  if (isLoadingPayments || isLoadingEmployees || isLoadingPendingEarnings) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Loader2 className="h-16 w-16 animate-spin text-slate-800" />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <PaymentsHeader employees={employees} />

      <PaymentsStatsCards payments={payments} employees={employees} />

      <PendingEarningsSection 
        pendingEarnings={pendingEarnings} 
        isLoading={isLoadingPendingEarnings} 
      />

      <RecentPaymentsSection 
        payments={payments}
        onViewPayment={setViewedPayment}
      />
      
      <ViewPaymentDialog
        payment={viewedPayment}
        onOpenChange={(isOpen) => !isOpen && setViewedPayment(null)}
      />
    </div>
  );
};
