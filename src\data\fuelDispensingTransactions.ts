
import { supabase } from "@/integrations/supabase/client";

export interface FuelDispensingTransaction {
  id: string;
  fuel_bunker_id: string;
  asset_id: string;
  operator_id: number;
  transaction_date: string;
  starting_reading?: number | null;
  ending_reading?: number | null;
  quantity_liters: number;
  notes?: string | null;
  created_at: string;
  // Add more if needed
}

export async function fetchFuelDispensingTransactions(): Promise<FuelDispensingTransaction[]> {
  const { data, error } = await supabase
    .from('fuel_dispensing_transactions')
    .select('*')
    .order('transaction_date', { ascending: false });
  if (error) throw new Error(error.message);
  return data as FuelDispensingTransaction[];
}

// Record new dispensing transaction
export async function recordFuelDispensingTransaction(payload: Omit<FuelDispensingTransaction, "id" | "created_at">) {
  const { data, error } = await supabase
    .from('fuel_dispensing_transactions')
    .insert(payload)
    .select()
    .single();
  if (error) throw new Error(error.message);
  return data as FuelDispensingTransaction;
}
