
-- Create table for kiln monitoring measurements
CREATE TABLE public.kiln_monitoring_measurements (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  kiln_id TEXT NOT NULL,
  chamber_number INTEGER NOT NULL,
  fire_zone TEXT,
  measurement_time TIME NOT NULL,
  measurement_date DATE NOT NULL DEFAULT CURRENT_DATE,
  parameters JSONB NOT NULL,
  user_id UUID,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create table for parameter norms configuration
CREATE TABLE public.kiln_parameter_norms (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  parameter_name TEXT NOT NULL UNIQUE,
  unit TEXT NOT NULL,
  min_value NUMERIC NOT NULL,
  max_value NUMERIC NOT NULL,
  cause TEXT NOT NULL,
  action TEXT NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create table for chamber zone assignments
CREATE TABLE public.kiln_chamber_zones (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  kiln_id TEXT NOT NULL,
  chamber_number INTEGER NOT NULL,
  zone TEXT NOT NULL DEFAULT 'Inactive',
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(kiln_id, chamber_number)
);

-- Enable RLS for the new tables
ALTER TABLE public.kiln_monitoring_measurements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kiln_parameter_norms ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.kiln_chamber_zones ENABLE ROW LEVEL SECURITY;

-- Create policies for kiln_monitoring_measurements
CREATE POLICY "Users can view all measurements" ON public.kiln_monitoring_measurements FOR SELECT USING (true);
CREATE POLICY "Users can create measurements" ON public.kiln_monitoring_measurements FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update measurements" ON public.kiln_monitoring_measurements FOR UPDATE USING (true);
CREATE POLICY "Users can delete measurements" ON public.kiln_monitoring_measurements FOR DELETE USING (true);

-- Create policies for kiln_parameter_norms
CREATE POLICY "Users can view all parameter norms" ON public.kiln_parameter_norms FOR SELECT USING (true);
CREATE POLICY "Users can create parameter norms" ON public.kiln_parameter_norms FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update parameter norms" ON public.kiln_parameter_norms FOR UPDATE USING (true);
CREATE POLICY "Users can delete parameter norms" ON public.kiln_parameter_norms FOR DELETE USING (true);

-- Create policies for kiln_chamber_zones
CREATE POLICY "Users can view all chamber zones" ON public.kiln_chamber_zones FOR SELECT USING (true);
CREATE POLICY "Users can create chamber zones" ON public.kiln_chamber_zones FOR INSERT WITH CHECK (true);
CREATE POLICY "Users can update chamber zones" ON public.kiln_chamber_zones FOR UPDATE USING (true);
CREATE POLICY "Users can delete chamber zones" ON public.kiln_chamber_zones FOR DELETE USING (true);

-- Insert default parameter norms
INSERT INTO public.kiln_parameter_norms (parameter_name, unit, min_value, max_value, cause, action) VALUES
('Fire Position', 'm', 0, 25, 'Delayed fire/misalignment', 'Verify alignment'),
('Fire Travel Rate', 'm/day', 2.5, 3.75, 'Over/underfiring', 'Adjust fuel or draught'),
('Draught Pressure', 'mmWC', -8, -4, 'Blockage or leak', 'Inspect fan/seals'),
('Fire Zone Temp', '°C', 850, 950, 'Fuel/air imbalance', 'Tune fuel-air mix'),
('Preheat Temp', '°C', 200, 400, 'Weak flame', 'Check curtain'),
('Cooling Zone Temp', '°C', 50, 120, 'Short cooling/excess air', 'Adjust fans/timing'),
('Brick Moisture', '%', 8, 10, 'Poor drying', 'Improve loading/drying'),
('Fuel-to-Brick Ratio', '%', 5.0, 6.5, 'Over/underfiring', 'Adjust dosing'),
('O₂', '%', 3, 5, 'Air leak/shortage', 'Balance airflow'),
('CO', '%', 0, 0.1, 'Incomplete combustion', 'Improve mixing, dry fuel'),
('CO₂', '%', 12, 15, 'Inefficient combustion', 'Tune air-fuel ratio'),
('Brick Core Temp', '°C', 850, 900, 'Improper dwell/fire speed', 'Adjust timing');
