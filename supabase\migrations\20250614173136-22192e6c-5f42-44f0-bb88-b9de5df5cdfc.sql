
-- 1. Create a table to store forklift driver allocations to teams.
CREATE TABLE IF NOT EXISTS public.forklift_allocations (
  id SERIAL PRIMARY KEY,
  forklift_driver_id INTEGER NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
  allocation_type TEXT NOT NULL, -- 'setting' or 'dehacking'
  allocated_teams TEXT[] NOT NULL, -- array of team IDs (for setting) or employee IDs (for dehacking)
  start_date DATE NOT NULL DEFAULT CURRENT_DATE,
  end_date DATE,
  is_active BOOLEAN NOT NULL DEFAULT true
);

-- 2. Insert forklist driver roles, if not already present.
-- (Assume 'forklift_driver_setting' and 'forklift_driver_dehacking' role names; add your specific role employee names below)

-- Add new drivers (if not exist)
INSERT INTO public.employees (name)
SELECT s FROM (VALUES ('Derrick (Setting Forklift)'), ('Ta<PERSON>wa (Dehacking Forklift)')) AS names(s)
WHERE NOT EXISTS (
  SELECT 1 FROM public.employees e WHERE e.name = names.s
);

-- Assign role for setting forklift driver
INSERT INTO public.employee_roles (employee_id, role)
SELECT e.id, 'forklift_driver_setting'
FROM public.employees e
LEFT JOIN public.employee_roles r ON e.id = r.employee_id AND r.role = 'forklift_driver_setting'
WHERE e.name = 'Derrick (Setting Forklift)' AND r.id IS NULL;

-- Assign role for dehacking forklift driver
INSERT INTO public.employee_roles (employee_id, role)
SELECT e.id, 'forklift_driver_dehacking'
FROM public.employees e
LEFT JOIN public.employee_roles r ON e.id = r.employee_id AND r.role = 'forklift_driver_dehacking'
WHERE e.name = 'Tapiwa (Dehacking Forklift)' AND r.id IS NULL;

-- 3. (Optional) Insert initial allocation (example)
-- Assume team IDs available are 'setting_team_1', 'setting_team_2', 'dehacking_team_1'
INSERT INTO public.forklift_allocations (forklift_driver_id, allocation_type, allocated_teams)
SELECT e.id, 'setting', ARRAY['setting_team_1', 'setting_team_2']
FROM public.employees e WHERE e.name = 'Derrick (Setting Forklift)'
AND NOT EXISTS (
  SELECT 1 FROM public.forklift_allocations a WHERE a.forklift_driver_id = e.id AND a.is_active = true
);

INSERT INTO public.forklift_allocations (forklift_driver_id, allocation_type, allocated_teams)
SELECT e.id, 'dehacking', ARRAY['dehacking_team_1']
FROM public.employees e WHERE e.name = 'Tapiwa (Dehacking Forklift)'
AND NOT EXISTS (
  SELECT 1 FROM public.forklift_allocations a WHERE a.forklift_driver_id = e.id AND a.is_active = true
);
