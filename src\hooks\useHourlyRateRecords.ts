
import { useQuery } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';

export interface HourlyRateRecord {
  id: string;
  employee_id: number;
  team_id: string;
  date: string;
  start_time: string;
  stop_time: string;
  hours_worked: number;
  hourly_rate: number;
  total_pay: number;
  created_at: string;
  updated_at: string;
}

export const useHourlyRateRecords = (dateFilter?: string) => {
  return useQuery({
    queryKey: ['hourly-rate-records', dateFilter],
    queryFn: async () => {
      let query = supabase
        .from('hourly_rate_records')
        .select('*')
        .order('created_at', { ascending: false });

      if (dateFilter) {
        query = query.eq('date', dateFilter);
      }

      const { data, error } = await query;

      if (error) {
        console.error('Error fetching hourly rate records:', error);
        throw error;
      }

      return data as HourlyRateRecord[];
    },
  });
};
