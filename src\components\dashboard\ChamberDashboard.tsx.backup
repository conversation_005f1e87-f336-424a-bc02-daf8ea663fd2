
import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON>H<PERSON>er, Card<PERSON><PERSON><PERSON>, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Flame, Loader2, RotateCcw, Calendar } from "lucide-react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { getKilns, KilnConfig, FireConfig } from "@/data/kilnData";
import { getManagementBrickTypes } from "@/data/managementBrickTypes";
import { supabase } from "@/integrations/supabase/client";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { subscribeToDehacking } from "@/data/dehackingStore";

// Chamber component with toggle functionality for setting
const SettingChamber = ({ 
  number, 
  fireName, 
  brickCount, 
  brickTypes, 
  isBurning, 
  onToggle,
  isLoading,
  chamberCode
}: { 
  number: number; 
  fireName?: string;
  brickCount?: number;
  brickTypes?: string[];
  isBurning: boolean;
  onToggle: (isBurning: boolean) => void;
  isLoading: boolean;
  chamberCode?: string;
}) => (
  <div className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
    isBurning ? 'bg-orange-50 border-orange-200' : 'bg-slate-50'
  }`}>
    <div className="flex flex-col items-center flex-1">
      <div className="text-sm font-medium mb-1">
        Chamber {number} {chamberCode ? `(${chamberCode})` : ''}
      </div>
      <div className={`text-${brickCount ? 'blue' : 'gray'}-700 font-bold text-xl mb-1`}>
        {brickCount ? brickCount.toLocaleString() : '-'}
      </div>
      {brickTypes && brickTypes.length > 0 ? (
        <div className="text-xs text-slate-600 text-center">
          {brickTypes.join(", ")}
        </div>
      ) : null}
    </div>
    <div className="flex items-center gap-2 mt-2">
      <Flame
        size={16}
        className={isBurning ? "text-orange-500" : "text-slate-400"}
      />
      <Switch
        checked={isBurning}
        onCheckedChange={onToggle}
        disabled={isLoading}
        className="data-[state=checked]:bg-orange-500"
      />
      <Label className="text-xs text-slate-600">
        {isBurning ? "Burning" : "Off"}
      </Label>
    </div>
  </div>
);

// Chamber component for dehacking with checkbox
const DehackingChamber = ({ 
  number, 
  fireName, 
  brickCount, 
  brickTypes, 
  isSelected,
  onSelect,
  chamberCode,
  kilnId
}: { 
  number: number; 
  fireName?: string;
  brickCount?: number;
  brickTypes?: string[];
  isSelected: boolean;
  onSelect: (checked: boolean) => void;
  chamberCode?: string;
  kilnId: string;
}) => (
  <div className={`border rounded p-3 min-h-[80px] text-center flex flex-col items-center justify-between ${
    isSelected ? 'bg-green-50 border-green-200' : 'bg-slate-50'
  }`}>
    <div className="flex flex-col items-center flex-1">
      <div className="text-sm font-medium mb-1">
        Chamber {number} {chamberCode ? `(${chamberCode})` : ''}
      </div>
      <div className={`text-${brickCount ? 'blue' : 'gray'}-700 font-bold text-xl mb-1`}>
        {brickCount ? brickCount.toLocaleString() : '-'}
      </div>
      {brickTypes && brickTypes.length > 0 ? (
        <div className="text-xs text-slate-600 text-center">
          {brickTypes.join(", ")}
        </div>
      ) : null}
    </div>
    <div className="flex items-center gap-2 mt-2">
      <Checkbox 
        checked={isSelected}
        onCheckedChange={onSelect}
        id={`dehacking-${kilnId}-${number}`}
        className="data-[state=checked]:bg-green-500 data-[state=checked]:border-green-500"
      />
      <Label 
        htmlFor={`dehacking-${kilnId}-${number}`}
        className="text-xs text-slate-600 cursor-pointer"
      >
        {isSelected ? "Dehacking" : "Available"}
      </Label>
    </div>
  </div>
);

// Interface for chamber fire status
interface ChamberFireStatus {
  id: number | string;
  kiln_id: string;
  chamber_number: number;
  is_burning: boolean;
}

interface SettingProductionEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  [key: string]: any;
}

interface DehackingEntry {
  id: number;
  brick_type_id: string;
  pallet_count: number;
  fire_id?: string;
  chamber_number?: number;
  [key: string]: any;
}

interface ChamberData {
  chamberNumber: number;
  chamberCode?: string;
  brickCount: number;
  brickTypes: string[];
  fireName?: string;
  isBurning: boolean;
}

// Helper function to get chamber count for each kiln
const getChamberCount = (kilnName: string): number => {
  if (kilnName === 'Habla') {
    return 10;
  }
  return 12; // Default for all other kilns
};

export const ChamberDashboard = () => {
  const [settingData, setSettingData] = useState<Record<string, ChamberData[]>>({});
  const [dehackingData, setDehackingData] = useState<Record<string, ChamberData[]>>({});
  const [selectedDehackingChambers, setSelectedDehackingChambers] = useState<Set<string>>(() => {
    // Load from localStorage on component mount
    const stored = localStorage.getItem('selectedDehackingChambers');
    return stored ? new Set(JSON.parse(stored)) : new Set();
  });

  // State for date filtering - default to current date but allow user control
  const [filterDate, setFilterDate] = useState(() => {
    // Check if user has set a custom date, otherwise use current date
    const storedDate = localStorage.getItem('chamberDashboardFilterDate');
    return storedDate || new Date().toISOString().split('T')[0];
  });

  // Get query client for cache management
  const client = useQueryClient();

  // Use React Query to fetch kilns data
  const { data: kilns = [], isLoading: kilnsLoading, isError: kilnsError } = useQuery({
    queryKey: ['kilns'],
    queryFn: getKilns,
    select: (data) => {
      // Sort kilns: Habla first, then Kiln 1-5
      return data.sort((a, b) => {
        if (a.name === 'Habla') return -1;
        if (b.name === 'Habla') return 1;
        
        const aNum = parseInt(a.name.replace('Kiln ', ''));
        const bNum = parseInt(b.name.replace('Kiln ', ''));
        return aNum - bNum;
      });
    }
  });

  const { data: brickTypes = [], isLoading: brickTypesLoading } = useQuery({
    queryKey: ['brickTypes'],
    queryFn: getManagementBrickTypes,
  });

  const { data: settingEntries = [], isLoading: settingEntriesLoading } = useQuery({
    queryKey: ['setting-production-entries', filterDate],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('setting_production_entries')
        .select('*, management_brick_types(name), fires(kiln_id)')
        .eq('date', filterDate);

      if (error) {
        console.error("Error fetching setting entries:", error);
        return [];
      }

      return data || [];
    },
  });

  const { data: dehackingEntries = [], isLoading: dehackingEntriesLoading } = useQuery({
    queryKey: ['dehacking-entries', filterDate],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('dehacking_entries')
        .select('*, management_brick_types(name), fires(kiln_id)')
        .eq('date', filterDate);

      if (error) {
        console.error("Error fetching dehacking entries:", error);
        return [];
      }

      return data || [];
    },
  });

  const { data: chamberFireStatus = [], isLoading: chamberStatusLoading } = useQuery({
    queryKey: ['chamber-fire-status'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('chamber_fire_status')
        .select('*');
      
      if (error) {
        console.error("Error fetching chamber fire status:", error);
        throw error;
      }
      
      return (data || []) as ChamberFireStatus[];
    },
    staleTime: 0,
    refetchOnWindowFocus: true,
    refetchInterval: 1000,
    refetchOnMount: true,
  });

  // Mutation for updating fire status
  const updateFireStatusMutation = useMutation({
    mutationFn: async ({ kilnId, chamberNumber, isBurning }: { kilnId: string; chamberNumber: number; isBurning: boolean }) => {
      console.log(`🔥 Updating fire status for kiln ${kilnId}, chamber ${chamberNumber} to ${isBurning}`);
      
      const { data, error } = await supabase
        .from('chamber_fire_status')
        .upsert({
          kiln_id: kilnId,
          chamber_number: chamberNumber,
          is_burning: isBurning,
          updated_at: new Date().toISOString()
        }, {
          onConflict: 'kiln_id,chamber_number'
        })
        .select();

      if (error) {
        console.error("❌ Fire status update failed:", error);
        throw error;
      }

      console.log("✅ Fire status updated successfully:", data);
      return data;
    },
    onSuccess: (data, variables) => {
      console.log("🎉 Mutation successful, updating cache...");
      
      // Immediately update the cache with optimistic update
      client.setQueryData(['chamber-fire-status'], (oldData: ChamberFireStatus[] | undefined) => {
        if (!oldData) return oldData;

        const updatedData = oldData.map(status => {
          if (status.kiln_id === variables.kilnId && status.chamber_number === variables.chamberNumber) {
            return { ...status, is_burning: variables.isBurning };
          }
          return status;
        });

        // If no existing record found, add new one
        const existingRecord = oldData.find(s => s.kiln_id === variables.kilnId && s.chamber_number === variables.chamberNumber);
        if (!existingRecord) {
          updatedData.push({
            id: Date.now(), // temporary id
            kiln_id: variables.kilnId,
            chamber_number: variables.chamberNumber,
            is_burning: variables.isBurning
          });
        }

        return updatedData;
      });

      // Then invalidate to ensure fresh data
      client.invalidateQueries({ queryKey: ['chamber-fire-status'] });
      
      toast.success(`Chamber ${variables.chamberNumber} fire ${variables.isBurning ? 'turned on' : 'turned off'}`);
    },
    onError: (error, variables) => {
      console.error("❌ Fire status update mutation failed:", error);
      toast.error(`Failed to update Chamber ${variables.chamberNumber} fire status`);
    },
  });

  // Show loading state
  if (kilnsLoading || brickTypesLoading || chamberStatusLoading || settingEntriesLoading || dehackingEntriesLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loader2 className="h-8 w-8 animate-spin text-slate-500" />
        <p className="ml-2 text-slate-500">Loading chamber dashboard...</p>
      </div>
    );
  }

  if (kilnsError) {
    return (
      <div className="text-center py-12 text-red-500">
        <p>Failed to load chamber data. Please try again later.</p>
      </div>
    );
  }

  // Updated processKilnData function to use dynamic chamber count
  const processKilnData = (kiln: any) => {
    const settingChambers: ChamberData[] = [];
    const dehackingChambers: ChamberData[] = [];
    
    const chamberCount = getChamberCount(kiln.name);
    
    for (let i = 1; i <= chamberCount; i++) {
      // Get burning status
      const status = chamberFireStatus.find(
        s => s.kiln_id === kiln.id && s.chamber_number === i
      );
      
      // Calculate setting chamber data
      const settingEntriesForChamber = settingEntries.filter(
        entry => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );
      
      const settingBrickCount = settingEntriesForChamber.reduce((sum, entry) => sum + (entry.pallet_count || 0), 0);
      const settingBrickTypes = [...new Set(settingEntriesForChamber.map(entry => entry.management_brick_types?.name).filter(Boolean))];
      
      // Calculate dehacking chamber data
      const dehackingEntriesForChamber = dehackingEntries.filter(
        entry => entry.fires?.kiln_id === kiln.id && entry.chamber_number === i
      );
      
      const dehackingBrickCount = dehackingEntriesForChamber.reduce((sum, entry) => sum + (entry.pallet_count || 0), 0);
      const dehackingBrickTypes = [...new Set(dehackingEntriesForChamber.map(entry => entry.management_brick_types?.name).filter(Boolean))];
      
      settingChambers.push({
        chamberNumber: i,
        chamberCode: `${i}`,
        brickCount: settingBrickCount,
        brickTypes: settingBrickTypes,
        fireName: undefined,
        isBurning: status?.is_burning || false
      });
      
      dehackingChambers.push({
        chamberNumber: i,
        chamberCode: `${i}`,
        brickCount: dehackingBrickCount,
        brickTypes: dehackingBrickTypes,
        fireName: undefined,
        isBurning: false
      });
    }
    
    return { settingChambers, dehackingChambers };
  };

  // Handler for fire toggle in setting section
  const handleFireToggle = (kilnId: string, chamberNumber: number, isBurning: boolean) => {
    updateFireStatusMutation.mutate({ kilnId, chamberNumber, isBurning });
  };

  const handleDehackingSelect = (kilnId: string, chamberNumber: number, checked: boolean) => {
    const key = `${kilnId}-${chamberNumber}`;
    setSelectedDehackingChambers(prev => {
      const newSet = new Set(prev);
      if (checked) {
        newSet.add(key);
      } else {
        newSet.delete(key);
      }
      // Save to localStorage
      localStorage.setItem('selectedDehackingChambers', JSON.stringify(Array.from(newSet)));
      return newSet;
    });

    console.log(`📦 Dehacking chamber ${chamberNumber} ${checked ? 'selected' : 'deselected'}`);
  };

  // Handle date change
  const handleDateChange = (newDate: string) => {
    setFilterDate(newDate);
    localStorage.setItem('chamberDashboardFilterDate', newDate);
  };

  // Manual clear function - resets to current date
  const handleManualClear = () => {
    const currentDate = new Date().toISOString().split('T')[0];
    if (window.confirm('Are you sure you want to clear all chamber data and reset to today\'s date? This will show only today\'s entries.')) {
      setFilterDate(currentDate);
      localStorage.setItem('chamberDashboardFilterDate', currentDate);

      // Clear selected dehacking chambers
      setSelectedDehackingChambers(new Set());
      localStorage.removeItem('selectedDehackingChambers');

      // Invalidate queries to refetch with new date
      client.invalidateQueries({ queryKey: ['setting-production-entries'] });
      client.invalidateQueries({ queryKey: ['dehacking-entries'] });

      toast.success('Chamber data cleared and reset to today');
    }
  };

  // Reset to current date without clearing selections
  const handleResetToToday = () => {
    const currentDate = new Date().toISOString().split('T')[0];
    setFilterDate(currentDate);
    localStorage.setItem('chamberDashboardFilterDate', currentDate);
    toast.success('Date reset to today');
  };

  return (
    <div className="space-y-6">
      {/* Date Controls and Clear Buttons */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              Chamber Dashboard Controls
            </span>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <Label htmlFor="filter-date" className="text-sm font-medium">
                  Filter Date:
                </Label>
                <Input
                  id="filter-date"
                  type="date"
                  value={filterDate}
                  onChange={(e) => handleDateChange(e.target.value)}
                  className="w-40"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetToToday}
                className="flex items-center gap-2"
              >
                <Calendar className="h-4 w-4" />
                Today
              </Button>
              <Button
                variant="destructive"
                size="sm"
                onClick={handleManualClear}
                className="flex items-center gap-2"
              >
                <RotateCcw className="h-4 w-4" />
                Clear & Reset
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-sm text-slate-600">
            <p>
              <strong>Current Filter:</strong> {new Date(filterDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>
            <p className="mt-1">
              <strong>Note:</strong> Data is filtered by the selected date. Use "Clear & Reset" to clear all selections and reset to today's date.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Kiln Chambers */}
      <div className="space-y-8">
        {kilns.map(kiln => {
          const { settingChambers, dehackingChambers } = processKilnData(kiln);

          return (
            <Card key={kiln.id}>
              <CardHeader>
                <CardTitle>{kiln.name} - Chamber Dashboard</CardTitle>
              </CardHeader>
              <CardContent>
              <h3 className="font-semibold text-slate-800 mb-2">Setting</h3>
              <div className="grid grid-cols-4 gap-4 mb-6">
                {settingChambers.map((chamber) => (
                  <SettingChamber 
                    key={`${kiln.id}-setting-chamber-${chamber.chamberNumber}`}
                    number={chamber.chamberNumber}
                    chamberCode={chamber.chamberCode}
                    fireName={chamber.fireName}
                    brickCount={chamber.brickCount}
                    brickTypes={chamber.brickTypes}
                    isBurning={chamber.isBurning}
                    onToggle={(isBurning) => handleFireToggle(kiln.id, chamber.chamberNumber, isBurning)}
                    isLoading={updateFireStatusMutation.isPending}
                  />
                ))}
              </div>

              <h3 className="font-semibold text-slate-800 mb-2">Dehacking</h3>
              <div className="grid grid-cols-4 gap-4">
                {dehackingChambers.map((chamber) => (
                  <DehackingChamber 
                    key={`${kiln.id}-dehacking-chamber-${chamber.chamberNumber}`}
                    number={chamber.chamberNumber}
                    chamberCode={chamber.chamberCode}
                    fireName={chamber.fireName}
                    brickCount={chamber.brickCount}
                    brickTypes={chamber.brickTypes}
                    isSelected={selectedDehackingChambers.has(`${kiln.id}-${chamber.chamberNumber}`)}
                    onSelect={(checked) => handleDehackingSelect(kiln.id, chamber.chamberNumber, checked)}
                    kilnId={kiln.id}
                  />
                ))}
              </div>
            </CardContent>
          </Card>
        );
      })}
      </div>
    </div>
  );
};

export default ChamberDashboard;
