
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";

export interface NotificationSettings {
  id: string;
  user_id: string;
  email_notifications: boolean;
  production_alerts: boolean;
  fuel_level_warnings: boolean;
  employee_updates: boolean;
}

export function useNotificationSettings(userId: string) {
  return useQuery({
    queryKey: ["notificationSettings", userId],
    queryFn: async () => {
      const { data, error } = await supabase
        .from("notification_settings")
        .select("*")
        .eq("user_id", userId)
        .single();
      
      if (error && error.code !== 'PGRST116') {
        throw new Error(error.message);
      }
      
      return data as NotificationSettings | null;
    },
    enabled: !!userId
  });
}

export function useUpdateNotificationSettings() {
  const queryClient = useQueryClient();
  
  return useMutation({
    mutationFn: async ({ userId, settings }: { 
      userId: string; 
      settings: Partial<NotificationSettings> 
    }) => {
      const { data, error } = await supabase
        .from("notification_settings")
        .upsert({
          user_id: userId,
          ...settings
        })
        .select()
        .single();
      
      if (error) throw new Error(error.message);
      return data;
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: ["notificationSettings", variables.userId] });
    }
  });
}
