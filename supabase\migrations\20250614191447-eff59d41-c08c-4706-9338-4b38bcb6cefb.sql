
-- Add a column to store the brick stage/category
ALTER TABLE public.management_brick_types
ADD COLUMN brick_stage TEXT NOT NULL DEFAULT 'finished';

-- Update existing Imperial and Maxi (if present) to have brick_stage = 'extruded'
UPDATE public.management_brick_types
SET brick_stage = 'extruded'
WHERE LOWER(name) IN ('imperial', 'maxi');

-- Insert missing extruded brick types if not present
INSERT INTO public.management_brick_types
  (id, name, category, grade, setting_rate, dehacking_rate, overtime_rate, status, bricks_per_pallet, brick_stage)
VALUES
  ('imperial_extruded', 'Imperial', 'Extruded', 'A+', 0, 0, 0, 'Active', 384, 'extruded')
ON CONFLICT (id) DO NOTHING;

INSERT INTO public.management_brick_types
  (id, name, category, grade, setting_rate, dehacking_rate, overtime_rate, status, bricks_per_pallet, brick_stage)
VALUES
  ('maxi_extruded', 'Maxi', 'Extruded', 'A+', 0, 0, 0, 'Active', 252, 'extruded')
ON CONFLICT (id) DO NOTHING;

-- Insert finished brick types (after dehacking)
INSERT INTO public.management_brick_types
  (id, name, category, grade, setting_rate, dehacking_rate, overtime_rate, status, bricks_per_pallet, brick_stage)
VALUES
  ('imp_2nd_grade', '2nd Grade Imp', 'Imperial', '2nd Grade', 0, 0, 0, 'Active', 384, 'finished'),
  ('imp_nfp', 'NFP Imp', 'Imperial', 'NFP', 0, 0, 0, 'Active', 384, 'finished'),
  ('imp_nfx', 'NFX Imp', 'Imperial', 'NFX', 0, 0, 0, 'Active', 384, 'finished'),
  ('imp_blue', 'Blue Imp', 'Imperial', 'Blue', 0, 0, 0, 'Active', 384, 'finished'),
  ('maxi_2nd_grade', '2nd Grade Maxi', 'Maxi', '2nd Grade', 0, 0, 0, 'Active', 252, 'finished'),
  ('maxi_nfp', 'NFP Maxi', 'Maxi', 'NFP', 0, 0, 0, 'Active', 252, 'finished'),
  ('maxi_nfx', 'NFX Maxi', 'Maxi', 'NFX', 0, 0, 0, 'Active', 252, 'finished')
ON CONFLICT (id) DO NOTHING;
