import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Calendar, Package, Truck, Edit, CheckCircle, Star, RotateCcw, X, Plus } from 'lucide-react';
import { 
  format, 
  eachDayOfInterval, 
  startOfMonth, 
  endOfMonth, 
  startOfWeek, 
  endOfWeek,
  isSameMonth,
  isToday as isDateToday
} from 'date-fns';
import { LoadPlanningEntry } from '@/types/loadPlanning';
import { filterLoadsByDateRange, getLoadsForDay } from '@/utils/loadPlanningDateUtils';

interface MonthlyViewProps {
  loads: LoadPlanningEntry[];
  currentDate: Date;
  isLoading: boolean;
  onEditLoad: (load: LoadPlanningEntry) => void;
  onRescheduleLoad: (load: LoadPlanningEntry) => void;
  onMarkAsReady: (loadId: string) => void;
  onMarkAsDispatched: (loadId: string) => void;
  onCancelLoad: (loadId: string) => void;
  onAddLoad: (selectedDate?: Date) => void;
  isMarkingReady: boolean;
  isMarkingDispatched: boolean;
}

export const MonthlyView: React.FC<MonthlyViewProps> = ({
  loads,
  currentDate,
  isLoading,
  onEditLoad,
  onRescheduleLoad,
  onMarkAsReady,
  onMarkAsDispatched,
  onCancelLoad,
  onAddLoad,
  isMarkingReady,
  isMarkingDispatched
}) => {
  const monthlyLoads = filterLoadsByDateRange(loads, currentDate, 'monthly');
  
  // Get calendar grid (6 weeks to show full month)
  const monthStart = startOfMonth(currentDate);
  const monthEnd = endOfMonth(currentDate);
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 1 }); // Monday
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 1 }); // Sunday
  const calendarDays = eachDayOfInterval({ start: calendarStart, end: calendarEnd });

  const getStatusColor = (load: LoadPlanningEntry) => {
    if (load.dispatched) return 'bg-green-100 border-green-300';
    if (load.ready) return 'bg-blue-100 border-blue-300';
    return 'bg-gray-100 border-gray-300';
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="grid grid-cols-7 gap-2">
          {Array.from({ length: 42 }).map((_, index) => (
            <Card key={index} className="animate-pulse h-24">
              <CardContent className="p-2">
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-2 bg-gray-200 rounded w-3/4"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-medium text-slate-800">
          {format(currentDate, 'MMMM yyyy')}
        </h3>
        <div className="text-sm text-slate-600">
          {monthlyLoads.length} total load{monthlyLoads.length !== 1 ? 's' : ''}
        </div>
      </div>

      {/* Calendar Header */}
      <div className="grid grid-cols-7 gap-2 mb-2">
        {['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'].map((day) => (
          <div key={day} className="text-center text-sm font-medium text-slate-600 py-2">
            {day}
          </div>
        ))}
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-2">
        {calendarDays.map((day) => {
          const dayLoads = getLoadsForDay(monthlyLoads, day);
          const isCurrentMonth = isSameMonth(day, currentDate);
          const isToday = isDateToday(day);
          
          return (
            <Card 
              key={day.toISOString()} 
              className={`
                h-24 overflow-hidden transition-all hover:shadow-md cursor-pointer
                ${isToday ? 'ring-2 ring-blue-500' : ''}
                ${!isCurrentMonth ? 'opacity-50' : ''}
              `}
              onClick={() => onAddLoad(day)}
            >
              <CardContent className="p-2 h-full flex flex-col">
                <div className="flex items-center justify-between mb-1">
                  <span className={`
                    text-sm font-medium
                    ${isToday ? 'text-blue-600 font-bold' : ''}
                    ${!isCurrentMonth ? 'text-gray-400' : 'text-slate-800'}
                  `}>
                    {format(day, 'd')}
                  </span>
                  {dayLoads.length === 0 && isCurrentMonth && (
                    <Plus className="h-3 w-3 text-gray-400 opacity-0 group-hover:opacity-100 transition-opacity" />
                  )}
                </div>
                
                <div className="flex-1 overflow-y-auto space-y-1">
                  {dayLoads.slice(0, 3).map((load) => (
                    <div
                      key={load.id}
                      className={`
                        text-xs p-1 rounded border cursor-pointer
                        ${getStatusColor(load)}
                        hover:shadow-sm transition-shadow
                      `}
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditLoad(load);
                      }}
                    >
                      <div className="font-medium truncate">
                        {load.client_name}
                      </div>
                      <div className="text-xs text-slate-600 truncate">
                        {load.brick_count} {load.load_type}
                      </div>
                      {load.rank && (
                        <div className="flex items-center gap-1 mt-1">
                          <Star className="h-2 w-2" />
                          <span className="text-xs">{load.rank}</span>
                        </div>
                      )}
                    </div>
                  ))}
                  
                  {dayLoads.length > 3 && (
                    <div className="text-xs text-slate-500 text-center py-1">
                      +{dayLoads.length - 3} more
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {/* Monthly Summary */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Monthly Summary</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-2xl font-bold text-slate-800">
                {monthlyLoads.length}
              </div>
              <div className="text-sm text-slate-600">Total Loads</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-blue-600">
                {monthlyLoads.filter(load => load.ready && !load.dispatched).length}
              </div>
              <div className="text-sm text-slate-600">Ready</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-green-600">
                {monthlyLoads.filter(load => load.dispatched).length}
              </div>
              <div className="text-sm text-slate-600">Dispatched</div>
            </div>
            <div>
              <div className="text-2xl font-bold text-slate-600">
                {monthlyLoads.reduce((total, load) => total + (load.brick_count || 0), 0)}
              </div>
              <div className="text-sm text-slate-600">Total Bricks</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
