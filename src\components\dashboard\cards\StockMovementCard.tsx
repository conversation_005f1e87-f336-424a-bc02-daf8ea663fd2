
import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>T<PERSON>le, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeftRight, ArrowDown, ArrowUp, Package } from "lucide-react";
import { useStockMovement } from "@/hooks/useStockMovement";

interface StockMovementCardProps {
  onNavigateToStockMovement: () => void;
}

export const StockMovementCard: React.FC<StockMovementCardProps> = ({
  onNavigateToStockMovement
}) => {
  const { inboundMovements, outboundMovements, isLoading } = useStockMovement();

  // Calculate quick stats
  const totalPending = inboundMovements
    .filter(movement => !movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalReceived = inboundMovements
    .filter(movement => movement.received)
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const totalSold = outboundMovements
    .reduce((sum, movement) => sum + movement.quantity, 0);

  const stockOnHand = totalReceived - totalSold;

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">Stock Movement</CardTitle>
        <ArrowLeftRight className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center h-20">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-slate-700"></div>
          </div>
        ) : (
          <div className="space-y-3">
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3 text-blue-600" />
                <span className="text-muted-foreground">On Hand:</span>
                <span className="font-medium text-blue-600">
                  {stockOnHand.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <Package className="h-3 w-3 text-orange-600" />
                <span className="text-muted-foreground">Pending:</span>
                <span className="font-medium text-orange-600">
                  {totalPending.toLocaleString()}
                </span>
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-2 text-xs">
              <div className="flex items-center gap-1">
                <ArrowDown className="h-3 w-3 text-green-600" />
                <span className="text-muted-foreground">Received:</span>
                <span className="font-medium text-green-600">
                  {totalReceived.toLocaleString()}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <ArrowUp className="h-3 w-3 text-red-600" />
                <span className="text-muted-foreground">Sold:</span>
                <span className="font-medium text-red-600">
                  {totalSold.toLocaleString()}
                </span>
              </div>
            </div>

            <Button 
              onClick={onNavigateToStockMovement}
              className="w-full mt-4" 
              size="sm"
            >
              Manage Stock Movement
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
