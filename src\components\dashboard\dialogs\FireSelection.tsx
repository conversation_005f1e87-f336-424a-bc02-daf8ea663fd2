
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Label } from "@/components/ui/label";

interface FireSelectionProps {
  loading: boolean;
  fires: { id: string; name: string }[];
  error?: Error | null;
  value: string | undefined;
  onChange: (val: string) => void;
}

export const FireSelection = ({ loading, fires, error, value, onChange }: FireSelectionProps) => (
  <div className="mb-6">
    <div className="text-lg font-bold mb-2">Step 2: Select Fire</div>
    {loading ? (
      <div>Loading fires...</div>
    ) : error ? (
      <div className="text-red-500">Error loading fires: {error.message}</div>
    ) : fires.length > 0 ? (
      <RadioGroup 
        value={value} 
        onValueChange={onChange}
        className="flex flex-col gap-2"
      >
        {fires.map((fire) => (
          <div key={fire.id} className="flex items-center gap-2">
            <RadioGroupItem value={fire.id} id={fire.id} />
            <Label htmlFor={fire.id} className="cursor-pointer">{fire.name}</Label>
          </div>
        ))}
      </RadioGroup>
    ) : (
      <div className="text-gray-500">No active fires found in database.</div>
    )}
  </div>
);
