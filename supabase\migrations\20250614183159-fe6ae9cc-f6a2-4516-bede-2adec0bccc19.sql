
-- Assign correct fire records to each kiln, using your actual kiln IDs from the image.

-- HABLA: Only "A"
INSERT INTO fires (id, kiln_id, name, status)
VALUES ('fire_habla_A', 'kiln_a', 'A', 'active')
ON CONFLICT (id) DO UPDATE SET name = 'A', kiln_id = 'kiln_a', status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_a' AND name <> 'A';

-- Kiln 1: fires "1A", "1B"
INSERT INTO fires (id, kiln_id, name, status)
VALUES 
  ('fire_kiln1_A', 'kiln_b', '1A', 'active'),
  ('fire_kiln1_B', 'kiln_b', '1B', 'active')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, kiln_id = EXCLUDED.kiln_id, status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_b' AND name NOT IN ('1A', '1B');

-- Kiln 2: fires "2A", "2B"
INSERT INTO fires (id, kiln_id, name, status)
VALUES 
  ('fire_kiln2_A', 'kiln_c', '2A', 'active'),
  ('fire_kiln2_B', 'kiln_c', '2B', 'active')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, kiln_id = EXCLUDED.kiln_id, status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_c' AND name NOT IN ('2A', '2B');

-- Kiln 3: fires "3A", "3B"
INSERT INTO fires (id, kiln_id, name, status)
VALUES 
  ('fire_kiln3_A', 'kiln_d', '3A', 'active'),
  ('fire_kiln3_B', 'kiln_d', '3B', 'active')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, kiln_id = EXCLUDED.kiln_id, status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_d' AND name NOT IN ('3A', '3B');

-- Kiln 4: fires "4A", "4B"
INSERT INTO fires (id, kiln_id, name, status)
VALUES 
  ('fire_kiln4_A', 'kiln_e', '4A', 'active'),
  ('fire_kiln4_B', 'kiln_e', '4B', 'active')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, kiln_id = EXCLUDED.kiln_id, status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_e' AND name NOT IN ('4A', '4B');

-- Kiln 5: fires "5A", "5B"
INSERT INTO fires (id, kiln_id, name, status)
VALUES 
  ('fire_kiln5_A', 'kiln_f', '5A', 'active'),
  ('fire_kiln5_B', 'kiln_f', '5B', 'active')
ON CONFLICT (id) DO UPDATE SET name = EXCLUDED.name, kiln_id = EXCLUDED.kiln_id, status = 'active';

DELETE FROM fires WHERE kiln_id = 'kiln_f' AND name NOT IN ('5A', '5B');
