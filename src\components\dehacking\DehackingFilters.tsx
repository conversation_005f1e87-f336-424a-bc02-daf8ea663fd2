
import { useState, useEffect } from "react";
import {
  Sheet,
  She<PERSON><PERSON>ontent,
  She<PERSON><PERSON>eader,
  SheetTitle,
  SheetDes<PERSON>,
  SheetFooter,
} from "@/components/ui/sheet";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface DehackingFiltersProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  employees: { id: number; name: string }[];
  onApplyFilters: (filters: { employeeId?: string }) => void;
  onResetFilters: () => void;
  filters: { employeeId?: string };
}

export const DehackingFilters = ({
  open,
  onOpenChange,
  employees,
  onApplyFilters,
  onResetFilters,
  filters,
}: DehackingFiltersProps) => {
  const [selectedEmployee, setSelectedEmployee] = useState<string | undefined>(filters.employeeId);

  useEffect(() => {
    if (open) {
      setSelectedEmployee(filters.employeeId);
    }
  }, [filters, open]);

  const handleApply = () => {
    onApplyFilters({ employeeId: selectedEmployee });
    onOpenChange(false);
  };

  const handleReset = () => {
    setSelectedEmployee(undefined);
    onResetFilters();
  };

  return (
    <Sheet open={open} onOpenChange={onOpenChange}>
      <SheetContent>
        <SheetHeader>
          <SheetTitle>Filter Dehacking Report</SheetTitle>
          <SheetDescription>
            Select filters to apply to the performance report.
          </SheetDescription>
        </SheetHeader>
        <div className="py-4 space-y-4">
          <div>
            <Label htmlFor="employee" className="text-right">
              Employee
            </Label>
            <Select onValueChange={setSelectedEmployee} value={selectedEmployee || "all"}>
              <SelectTrigger id="employee">
                <SelectValue placeholder="All Employees" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Employees</SelectItem>
                {employees.map((e) => (
                  <SelectItem key={e.id} value={String(e.id)}>
                    {e.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        <SheetFooter className="sm:justify-between">
          <Button variant="ghost" onClick={handleReset}>
            Reset Filters
          </Button>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => onOpenChange(false)}>
              Cancel
            </Button>
            <Button onClick={handleApply}>Apply Filters</Button>
          </div>
        </SheetFooter>
      </SheetContent>
    </Sheet>
  );
};
