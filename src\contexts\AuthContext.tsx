
import React, { createContext, useContext, useState, useEffect } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { User, Session } from '@supabase/supabase-js';
import type { UserRole } from '@/hooks/useUsers';
import bcrypt from 'bcryptjs';

interface LoginCredentials {
  username: string;
  password: string;
}

interface UserProfile {
  id: string;
  username?: string;
  full_name?: string;
  email?: string;
  role: UserRole;
  active: boolean;
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  currentUser: UserProfile | null;
  user: User | null;
  session: Session | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (credentials: LoginCredentials) => Promise<{ error?: string }>;
  logout: () => Promise<void>;
  loginTime: Date | null;
  getSessionDuration: () => string;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [loginTime, setLoginTime] = useState<Date | null>(null);

  // Check for existing session on app load
  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Check if we have a stored session
        const storedUser = localStorage.getItem('currentUser');
        const storedLoginTime = localStorage.getItem('loginTime');
        
        if (storedUser) {
          const userData = JSON.parse(storedUser);
          setCurrentUser(userData);
          
          // Create a mock session for compatibility
          const mockSession = {
            user: { id: userData.id, email: userData.email } as User,
            access_token: 'mock-token',
            expires_at: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
          } as Session;
          
          setSession(mockSession);
          setUser(mockSession.user);
          
          if (storedLoginTime) {
            setLoginTime(new Date(storedLoginTime));
          }
        }
      } catch (error) {
        console.error('Error initializing auth:', error);
        localStorage.removeItem('currentUser');
        localStorage.removeItem('loginTime');
      } finally {
        setIsLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const login = async (credentials: LoginCredentials): Promise<{ error?: string }> => {
    try {
      setIsLoading(true);
      
      // Query the users table directly
      const { data: userData, error: queryError } = await supabase
        .from('users')
        .select('*')
        .eq('username', credentials.username)
        .eq('active', true)
        .single();

      if (queryError || !userData) {
        return { error: 'Invalid username or password' };
      }

      // Verify password
      const passwordMatch = await bcrypt.compare(credentials.password, userData.password_hash);
      
      if (!passwordMatch) {
        return { error: 'Invalid username or password' };
      }

      // Create user profile object
      const userProfile: UserProfile = {
        id: userData.id,
        username: userData.username,
        full_name: userData.full_name,
        email: userData.email,
        role: userData.role,
        active: userData.active,
        created_at: userData.created_at,
        updated_at: userData.updated_at,
      };

      // Set auth state
      setCurrentUser(userProfile);
      
      // Create a mock session for compatibility with existing code
      const mockSession = {
        user: { id: userData.id, email: userData.email } as User,
        access_token: 'mock-token',
        expires_at: Date.now() + 24 * 60 * 60 * 1000, // 24 hours
      } as Session;
      
      setSession(mockSession);
      setUser(mockSession.user);
      
      // Set login time
      const now = new Date();
      setLoginTime(now);
      
      // Store in localStorage for persistence
      localStorage.setItem('currentUser', JSON.stringify(userProfile));
      localStorage.setItem('loginTime', now.toISOString());

      return {};
    } catch (error) {
      console.error('Login error:', error);
      return { error: 'An unexpected error occurred during login' };
    } finally {
      setIsLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // Clear all state
      setUser(null);
      setSession(null);
      setCurrentUser(null);
      setLoginTime(null);
      
      // Clear localStorage
      localStorage.removeItem('currentUser');
      localStorage.removeItem('loginTime');
    } catch (error) {
      console.error('Logout error:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const getSessionDuration = (): string => {
    if (!loginTime) return '0h 0m';
    
    const now = new Date();
    const diffMs = now.getTime() - loginTime.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    
    return `${hours}h ${minutes}m`;
  };

  const contextValue: AuthContextType = {
    currentUser,
    user,
    session,
    isAuthenticated: !!currentUser,
    isLoading,
    login,
    logout,
    loginTime,
    getSessionDuration,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
