
-- Add day/night rates to management_brick_types
ALTER TABLE public.management_brick_types
  ADD COLUMN IF NOT EXISTS dehacking_day_rate NUMERIC,
  ADD COLUMN IF NOT EXISTS dehacking_night_rate NUMERIC,
  ADD COLUMN IF NOT EXISTS setting_day_rate NUMERIC,
  ADD COLUMN IF NOT EXISTS setting_night_rate NUMERIC;

-- Update sample data for imperial/maxi to match your provided rates
UPDATE public.management_brick_types
  SET dehacking_day_rate = 22.00, dehacking_night_rate = 27.50,
      setting_day_rate = 10.90, setting_night_rate = 13.60
  WHERE id IN ('imperial', 'maxi');

-- Create table for employee roles
CREATE TABLE IF NOT EXISTS public.employee_roles (
  id SERIAL PRIMARY KEY,
  employee_id INTEGER NOT NULL REFERENCES public.employees(id) ON DELETE CASCADE,
  role TEXT NOT NULL -- e.g., 'dehacker', 'setter', 'supervisor', 'forklift_driver', 'strapper'
);

-- Insert supervisors (if not already in employees table)
INSERT INTO public.employees (name)
SELECT s FROM (VALUES ('Taurai'), ('<PERSON><PERSON>'), ('Mercy')) AS names(s)
WHERE NOT EXISTS (
  SELECT 1 FROM public.employees e WHERE e.name = names.s
);

-- Assign role 'supervisor' to the supervisors
INSERT INTO public.employee_roles (employee_id, role)
SELECT e.id, 'supervisor'
FROM public.employees e
LEFT JOIN public.employee_roles r ON e.id = r.employee_id AND r.role = 'supervisor'
WHERE e.name IN ('Taurai', 'Reki', 'Mercy') AND r.id IS NULL;
