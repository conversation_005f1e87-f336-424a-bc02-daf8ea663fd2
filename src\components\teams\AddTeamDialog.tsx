
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON><PERSON><PERSON>le, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAddTeam } from "@/hooks/useTeams";

interface AddTeamDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export const AddTeamDialog = ({ isOpen, onClose }: AddTeamDialogProps) => {
  const [name, setName] = useState('');
  const { mutate: addTeam, isPending } = useAddTeam();

  const handleAddTeam = () => {
    if (name.trim()) {
      addTeam(name.trim(), {
        onSuccess: () => {
          setName('');
          onClose();
        }
      });
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add New Team</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Label htmlFor="team-name">Team Name</Label>
          <Input id="team-name" value={name} onChange={(e) => setName(e.target.value)} placeholder="e.g., Day Shift A" />
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleAddTeam} disabled={isPending || !name.trim()}>
            {isPending ? "Adding..." : "Add Team"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
