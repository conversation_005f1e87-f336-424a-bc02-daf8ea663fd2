-- <PERSON>reate function to update timestamps if it doesn't exist
CREATE OR <PERSON><PERSON>LACE FUNCTION public.update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON>reate stock_movement_inbound table for tracking deliveries to Kraaifontein stock yard
CREATE TABLE public.stock_movement_inbound (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  date DATE NOT NULL,
  brick_type TEXT NOT NULL,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  dnote TEXT NOT NULL, -- Delivery Note
  transporter TEXT NOT NULL,
  received BOOLEAN DEFAULT false,
  received_at TIMESTAMP WITH TIME ZONE,
  received_by UUI<PERSON>, -- User who marked as received
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  user_id UUID NOT NULL
);

-- <PERSON>reate stock_movement_outbound table for tracking sales from Kraaifontein stock yard
CREATE TABLE public.stock_movement_outbound (
  id UUID NOT NULL DEFAULT gen_random_uuid() PRIMARY KEY,
  sold_date DATE NOT NULL,
  brick_type TEXT NOT NULL,
  quantity INTEGER NOT NULL CHECK (quantity > 0),
  client_name TEXT NOT NULL,
  delivery_note TEXT NOT NULL,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  user_id UUID NOT NULL
);

-- Enable Row Level Security
ALTER TABLE public.stock_movement_inbound ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_movement_outbound ENABLE ROW LEVEL SECURITY;

-- Create policies for stock_movement_inbound
CREATE POLICY "Users can view all inbound stock movements" 
ON public.stock_movement_inbound 
FOR SELECT 
USING (true);

CREATE POLICY "Users can create inbound stock movements" 
ON public.stock_movement_inbound 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Users can update inbound stock movements" 
ON public.stock_movement_inbound 
FOR UPDATE 
USING (true);

CREATE POLICY "Users can delete inbound stock movements" 
ON public.stock_movement_inbound 
FOR DELETE 
USING (true);

-- Create policies for stock_movement_outbound
CREATE POLICY "Users can view all outbound stock movements" 
ON public.stock_movement_outbound 
FOR SELECT 
USING (true);

CREATE POLICY "Users can create outbound stock movements" 
ON public.stock_movement_outbound 
FOR INSERT 
WITH CHECK (true);

CREATE POLICY "Users can update outbound stock movements" 
ON public.stock_movement_outbound 
FOR UPDATE 
USING (true);

CREATE POLICY "Users can delete outbound stock movements" 
ON public.stock_movement_outbound 
FOR DELETE 
USING (true);

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_stock_movement_inbound_updated_at
BEFORE UPDATE ON public.stock_movement_inbound
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

CREATE TRIGGER update_stock_movement_outbound_updated_at
BEFORE UPDATE ON public.stock_movement_outbound
FOR EACH ROW
EXECUTE FUNCTION public.update_updated_at_column();

-- Create indexes for better performance
CREATE INDEX idx_stock_movement_inbound_date ON public.stock_movement_inbound(date);
CREATE INDEX idx_stock_movement_inbound_brick_type ON public.stock_movement_inbound(brick_type);
CREATE INDEX idx_stock_movement_inbound_received ON public.stock_movement_inbound(received);
CREATE INDEX idx_stock_movement_outbound_sold_date ON public.stock_movement_outbound(sold_date);
CREATE INDEX idx_stock_movement_outbound_brick_type ON public.stock_movement_outbound(brick_type);
