
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogFooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { useUnassignedEmployees, useAddTeamMember } from "@/hooks/useTeams";
import { TeamWithMembers } from '@/data/teamData';
import { Loader2, Check, ChevronsUpDown } from 'lucide-react';
import { cn } from "@/lib/utils";

interface AddMemberDialogProps {
  isOpen: boolean;
  onClose: () => void;
  team: TeamWithMembers;
}

export const AddMemberDialog = ({ isOpen, onClose, team }: AddMemberDialogProps) => {
  const [selectedEmployeeId, setSelectedEmployeeId] = useState<string | null>(null);
  const [open, setOpen] = useState(false);
  const { data: employees, isLoading } = useUnassignedEmployees();
  const { mutate: addMember, isPending } = useAddTeamMember();

  const handleAddMember = () => {
    if (selectedEmployeeId) {
      addMember({ teamId: team.id, employeeId: parseInt(selectedEmployeeId) }, {
        onSuccess: () => {
          setSelectedEmployeeId(null);
          setOpen(false);
          onClose();
        }
      });
    }
  };

  const selectedEmployee = employees?.find(emp => String(emp.id) === selectedEmployeeId);

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Add Member to {team.name}</DialogTitle>
        </DialogHeader>
        <div className="py-4">
          <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                role="combobox"
                aria-expanded={open}
                className="w-full justify-between"
                disabled={isLoading || isPending}
              >
                {selectedEmployee ? selectedEmployee.name : "Select an employee"}
                <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
              <Command>
                <CommandInput placeholder="Search employees..." />
                <CommandList>
                  <CommandEmpty>
                    {isLoading ? (
                      <div className="flex items-center justify-center p-4">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span className="ml-2">Loading employees...</span>
                      </div>
                    ) : (
                      "No employees found."
                    )}
                  </CommandEmpty>
                  <CommandGroup>
                    {employees?.map((employee) => (
                      <CommandItem
                        key={employee.id}
                        value={`${employee.name} ${employee.employee_code || ''}`}
                        onSelect={() => {
                          setSelectedEmployeeId(String(employee.id));
                          setOpen(false);
                        }}
                      >
                        <Check
                          className={cn(
                            "mr-2 h-4 w-4",
                            selectedEmployeeId === String(employee.id) ? "opacity-100" : "opacity-0"
                          )}
                        />
                        <div className="flex flex-col">
                          <span>{employee.name}</span>
                          {employee.employee_code && (
                            <span className="text-xs text-muted-foreground">
                              {employee.employee_code}
                            </span>
                          )}
                        </div>
                      </CommandItem>
                    ))}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleAddMember} disabled={!selectedEmployeeId || isPending}>
            {isPending ? "Adding..." : "Add Member"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
