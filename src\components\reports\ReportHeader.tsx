
import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

interface ReportHeaderProps {
  onGenerateReport: () => void;
  isLoading: boolean;
}

export const ReportHeader = ({ onGenerateReport, isLoading }: ReportHeaderProps) => {
  return (
    <div className="flex justify-between items-center">
      <div>
        <h1 className="text-3xl font-bold text-slate-800">Reports & Analytics</h1>
        <p className="text-slate-600">Comprehensive business intelligence and reporting</p>
      </div>
      <Button className="bg-slate-800 hover:bg-slate-700" onClick={onGenerateReport} disabled={isLoading}>
        <Download size={20} className="mr-2" />
        Generate Report
      </Button>
    </div>
  );
};
