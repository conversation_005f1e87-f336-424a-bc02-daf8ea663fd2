
import { supabase } from "@/integrations/supabase/client";

export interface Employee {
  id: number;
  name: string;
  employee_code: string | null;
  role: string | null;
  department: string | null;
  status: string;
  performance: number;
}

export type EmployeeFormValues = {
  name: string;
  employeeCode: string;
  role: string;
  department: string;
};

export const getEmployees = async (): Promise<Employee[]> => {
  const { data, error } = await supabase.from('employees').select('*').order('name');
  if (error) {
    console.error("Error fetching employees", error);
    throw new Error(error.message);
  }
  return data || [];
};

export const addEmployee = async (employee: EmployeeFormValues) => {
  const { data, error } = await supabase.from('employees').insert([
    { 
        name: employee.name,
        employee_code: employee.employeeCode,
        role: employee.role,
        department: employee.department
    }
  ]).select().single();
  if (error) {
    console.error("Error adding employee:", error);
    throw error;
  }
  return data;
};

export const updateEmployee = async (employee: Partial<EmployeeFormValues> & { id: number }) => {
  const { id, ...employeeData } = employee;
  
  const updateData = {
    name: employeeData.name,
    employee_code: employeeData.employeeCode,
    role: employeeData.role,
    department: employeeData.department
  };

  // remove undefined fields
  Object.keys(updateData).forEach(key => updateData[key as keyof typeof updateData] === undefined && delete updateData[key as keyof typeof updateData]);

  const { data, error } = await supabase.from('employees').update(updateData).eq('id', id).select().single();
  
  if (error) {
    console.error("Error updating employee:", error);
    throw error;
  }
  return data;
};

export const deleteEmployee = async (id: number) => {
  const { error } = await supabase.from('employees').delete().eq('id', id);
  if (error) {
    console.error("Error deleting employee:", error);
    throw error;
  }
  return { id };
};
