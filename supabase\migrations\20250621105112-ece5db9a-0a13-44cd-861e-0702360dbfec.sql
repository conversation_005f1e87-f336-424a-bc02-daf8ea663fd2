
-- Add new columns to hackline_counts table to track pallet type and count
ALTER TABLE public.hackline_counts 
ADD COLUMN pallet_type TEXT NOT NULL DEFAULT 'Imperial' CHECK (pallet_type IN ('Imperial', 'Maxi')),
ADD COLUMN pallet_count INT NOT NULL DEFAULT 0;

-- Update the table to calculate brick count based on pallet type and count
-- We'll keep count_total for backward compatibility and calculate it automatically
CREATE OR REPLACE FUNCTION calculate_brick_count()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.pallet_type = 'Imperial' THEN
    NEW.count_total = NEW.pallet_count * 500;
  ELSIF NEW.pallet_type = 'Maxi' THEN
    NEW.count_total = NEW.pallet_count * 400;
  END IF;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically calculate brick count
CREATE TRIGGER hackline_counts_calculate_bricks
  BEFORE INSERT OR UPDATE ON public.hackline_counts
  FOR EACH ROW
  EXECUTE FUNCTION calculate_brick_count();
