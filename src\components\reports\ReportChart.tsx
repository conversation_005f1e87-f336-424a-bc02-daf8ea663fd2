import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Loader2 } from "lucide-react";
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveC<PERSON>r, Legend } from "recharts";
import { TimeRange } from "@/components/dashboard/DashboardContent";
import { ReportType } from "@/components/pages/ReportsPage";
import { ReportData } from "@/data/reports";

interface ReportChartProps {
  reportData: ReportData;
  isLoading: boolean;
  isError: boolean;
  selectedReportType: ReportType;
  selectedTimeRange: TimeRange;
  reportTypes: { value: ReportType; label: string }[];
}

const getChartConfig = (reportType: ReportType) => {
  switch (reportType) {
    case "factory-output":
      return { dataKey1: "production", dataKey2: "target", name1: "Production", name2: "Target" };
    case "setting-teams":
      return { dataKey1: "bricks_set", dataKey2: "target", name1: "Bricks Set", name2: "Target" };
    case "dehacking":
      return { dataKey1: "pallets_dehacked", dataKey2: "target", name1: "Pallets Dehacked", name2: "Target" };
    case "fuel-management":
      return { dataKey1: "consumed", dataKey2: "delivered", name1: "Consumed", name2: "Delivered" };
    case "employee-hours":
      return { dataKey1: "setting_earnings", dataKey2: "dehacking_earnings", name1: "Setting Earnings", name2: "Dehacking Earnings" };
    default:
      return { dataKey1: "value1", dataKey2: "value2", name1: "Value 1", name2: "Value 2" };
  }
};

export const ReportChart = ({
  reportData,
  isLoading,
  isError,
  selectedReportType,
  selectedTimeRange,
  reportTypes,
}: ReportChartProps) => {

  const renderChart = () => {
    if (isLoading) {
      return <div className="flex items-center justify-center h-full"><Loader2 className="h-8 w-8 animate-spin text-slate-500" /></div>;
    }
    if (isError) {
      return <div className="flex items-center justify-center h-full text-red-500">Error loading report data. Please try again.</div>;
    }
    if (!reportData || !reportData.main || reportData.main.length === 0) {
      return <div className="flex items-center justify-center h-full text-slate-500">No data for this period.</div>
    }
    
    // Default BarChart
    const chartConfig = getChartConfig(selectedReportType);
    return (
      <ResponsiveContainer width="100%" height="100%">
        <BarChart data={reportData.main}>
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis dataKey="name" />
          <YAxis />
          <Tooltip formatter={(value: number) => `R${value.toLocaleString()}`} />
          <Legend />
          <Bar dataKey={chartConfig.dataKey1} fill="#3b82f6" name={chartConfig.name1} />
          {chartConfig.dataKey2 && <Bar dataKey={chartConfig.dataKey2} fill="#e5e7eb" name={chartConfig.name2} />}
        </BarChart>
      </ResponsiveContainer>
    );
  };
  
  const reportTitle = reportTypes.find(type => type.value === selectedReportType)?.label;
  const subTitle = 'Performance metrics for the selected period';

  return (
    <Card>
      <CardHeader>
        <CardTitle>
          {reportTitle} - {selectedTimeRange.charAt(0).toUpperCase() + selectedTimeRange.slice(1)}
        </CardTitle>
        <p className="text-sm text-slate-600">{subTitle}</p>
      </CardHeader>
      <CardContent>
        <div className="h-80">
          {renderChart()}
        </div>
      </CardContent>
    </Card>
  );
};
