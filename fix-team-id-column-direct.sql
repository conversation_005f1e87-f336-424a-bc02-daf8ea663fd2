-- Direct SQL fix for dehacking_entries team_id column issue
-- Run this directly on the Supabase database via the SQL Editor

-- Check if team_id column exists and add it if missing
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 
        FROM information_schema.columns 
        WHERE table_schema = 'public'
        AND table_name = 'dehacking_entries' 
        AND column_name = 'team_id'
    ) THEN
        -- Add the team_id column
        ALTER TABLE public.dehacking_entries 
        ADD COLUMN team_id TEXT;
        
        -- Add foreign key constraint to teams table
        ALTER TABLE public.dehacking_entries 
        ADD CONSTRAINT dehacking_entries_team_id_fkey 
        FOREIGN KEY (team_id) REFERENCES public.teams(id);
        
        -- Add comment
        COMMENT ON COLUMN public.dehacking_entries.team_id IS 'References the team assigned to this dehacking entry';
        
        RAISE NOTICE 'Successfully added team_id column to dehacking_entries table';
    ELSE
        RAISE NOTICE 'team_id column already exists in dehacking_entries table';
    END IF;
END $$;

-- Ensure the required dehacking teams exist
INSERT INTO public.teams (id, name) VALUES 
('dehacking-team-mercy', 'Dehacking Team Mercy'),
('dehacking-team-taurai', 'Dehacking Team Taurai')
ON CONFLICT (id) DO NOTHING;

-- Verify the column was added
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public'
AND table_name = 'dehacking_entries' 
AND column_name = 'team_id';

-- Show all columns in dehacking_entries for verification
SELECT 
    column_name,
    data_type,
    is_nullable
FROM information_schema.columns 
WHERE table_schema = 'public'
AND table_name = 'dehacking_entries' 
ORDER BY ordinal_position;
