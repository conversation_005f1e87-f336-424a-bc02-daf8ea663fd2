
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { TimeRange } from "@/components/dashboard/DashboardContent";
import { ReportType } from "@/components/pages/ReportsPage";
import * as XLSX from 'xlsx';
import { toast } from "sonner";
import { Download } from "lucide-react";
import { ReportData } from "@/data/reports";

interface ReportPreviewDialogProps {
  isOpen: boolean;
  onClose: () => void;
  reportData?: ReportData;
  timeRange: TimeRange;
  reportType: ReportType;
  reportTitle: string;
}

export const ReportPreviewDialog = ({
  isOpen,
  onClose,
  reportData,
  timeRange,
  reportType,
  reportTitle,
}: ReportPreviewDialogProps) => {

  const handleDownloadXLS = () => {
    try {
      if (!reportData) {
        toast.error("Report data is not available yet.");
        return;
      }
      const wb = XLSX.utils.book_new();

      // Convert main data to sheet
      const wsMain = XLSX.utils.json_to_sheet(reportData.main);
      XLSX.utils.book_append_sheet(wb, wsMain, "Main Data");

      // Convert secondary data to sheet
      if (reportData.secondary && reportData.secondary.length > 0) {
        const wsSecondary = XLSX.utils.json_to_sheet(reportData.secondary);
        XLSX.utils.book_append_sheet(wb, wsSecondary, "Detailed Data");
      }

      const fileName = `${reportTitle.replace(/\s+/g, '_')}_${timeRange}_${new Date().toISOString().split('T')[0]}.xlsx`;

      XLSX.writeFile(wb, fileName);

      toast.success("Report downloaded successfully!", {
        description: `The file ${fileName} has been saved.`,
      });
      onClose();
    } catch (error) {
        console.error("Failed to generate report:", error);
        toast.error("Failed to generate report.", {
            description: "An unexpected error occurred. Please try again."
        });
    }
  };

  const getMainTableHeaders = (reportType: ReportType) => {
    switch (reportType) {
      case "factory-output":
        return ["Period", "Production (Bricks)", "Target", "Efficiency %"];
      case "setting-teams":
        return ["Team", "Bricks Set", "Target", "Efficiency %"];
      case "dehacking":
        return ["Employee", "Pallets Dehacked", "Target", "Efficiency %"];
      case "fuel-management":
        return ["Fuel Type", "Consumed (L)", "Delivered (L)", "Cost (R)"];
      case "employee-hours":
        return ["Department", "Employees", "Setting Earnings (R)", "Dehacking Earnings (R)", "Total Earnings (R)"];
      case "user-activity":
        return ["User", "Login Time", "Logout Time", "Session Duration", "Total Activities"];
      default:
        return ["Name", "Value 1", "Value 2"];
    }
  };

  const getSecondaryTableHeaders = (reportType: ReportType) => {
    switch (reportType) {
      case "factory-output":
        return ["Category", "Quantity (Bricks)"];
      case "setting-teams":
        return ["Team", "Members"];
      case "dehacking":
        return ["Team", "Damaged Bricks", "Rejects", "Quality Score"];
      case "fuel-management":
        return ["Asset", "Fuel Type", "Consumption (L)"];
      case "employee-hours":
        return ["Employee", "Employee Code", "Department", "Total Earnings (R)"];
      case "user-activity":
        return ["User", "Action", "Details", "Category", "Timestamp"];
      default:
        return ["Item", "Value"];
    }
  };

  const renderMainTableRow = (item: any, reportType: ReportType) => {
    switch (reportType) {
      case "factory-output":
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.production?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.target?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.efficiency}%</TableCell>
          </>
        );
      case "setting-teams":
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.bricks_set?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.target?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.efficiency}%</TableCell>
          </>
        );
      case "dehacking":
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.pallets_dehacked}</TableCell>
            <TableCell className="text-right">{item.target}</TableCell>
            <TableCell className="text-right">{item.efficiency}%</TableCell>
          </>
        );
      case "fuel-management":
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.consumed?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.delivered?.toLocaleString()}</TableCell>
            <TableCell className="text-right">R{item.cost?.toLocaleString()}</TableCell>
          </>
        );
      case "employee-hours":
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.employees}</TableCell>
            <TableCell className="text-right">R{item.setting_earnings?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</TableCell>
            <TableCell className="text-right">R{item.dehacking_earnings?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</TableCell>
            <TableCell className="text-right">R{item.total_earnings?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</TableCell>
          </>
        );
      case "user-activity":
        return (
          <>
            <TableCell className="font-medium">{item.userName}</TableCell>
            <TableCell className="text-right">{item.loginTime || 'N/A'}</TableCell>
            <TableCell className="text-right">{item.logoutTime || 'N/A'}</TableCell>
            <TableCell className="text-right">{item.sessionDuration}</TableCell>
            <TableCell className="text-right">{item.totalActivities}</TableCell>
          </>
        );
      default:
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.value1}</TableCell>
            <TableCell className="text-right">{item.value2}</TableCell>
          </>
        );
    }
  };

  const renderSecondaryTableRow = (item: any, reportType: ReportType) => {
    switch (reportType) {
      case "factory-output":
        return (
          <>
            <TableCell className="font-medium">{item.category}</TableCell>
            <TableCell className="text-right">{item.quantity?.toLocaleString()}</TableCell>
          </>
        );
      case "setting-teams":
        return (
          <>
            <TableCell className="font-medium">{item.team}</TableCell>
            <TableCell className="text-right">{item.members}</TableCell>
          </>
        );
      case "dehacking":
        return (
          <>
            <TableCell className="font-medium">{item.team}</TableCell>
            <TableCell className="text-right">{item.damaged_bricks?.toLocaleString()}</TableCell>
            <TableCell className="text-right">{item.rejects}</TableCell>
            <TableCell className="text-right">{item.quality_score}%</TableCell>
          </>
        );
      case "fuel-management":
        return (
          <>
            <TableCell className="font-medium">{item.asset}</TableCell>
            <TableCell className="text-right">{item.fuel_type}</TableCell>
            <TableCell className="text-right">{item.consumption?.toLocaleString()}</TableCell>
          </>
        );
      case "employee-hours":
        return (
          <>
            <TableCell className="font-medium">{item.employee}</TableCell>
            <TableCell>{item.employee_code}</TableCell>
            <TableCell>{item.department}</TableCell>
            <TableCell className="text-right">R{item.earnings?.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</TableCell>
          </>
        );
      case "user-activity":
        return (
          <>
            <TableCell className="font-medium">{item.userName}</TableCell>
            <TableCell>{item.action}</TableCell>
            <TableCell>{item.details}</TableCell>
            <TableCell>{item.category}</TableCell>
            <TableCell className="text-right">{item.timestamp}</TableCell>
          </>
        );
      default:
        return (
          <>
            <TableCell className="font-medium">{item.name}</TableCell>
            <TableCell className="text-right">{item.value}</TableCell>
          </>
        );
    }
  };

  const mainHeaders = getMainTableHeaders(reportType);
  const secondaryHeaders = getSecondaryTableHeaders(reportType);

  if (!reportData) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-6xl max-h-[90vh]">
        <DialogHeader>
          <DialogTitle>{reportTitle} Report ({timeRange.charAt(0).toUpperCase() + timeRange.slice(1)})</DialogTitle>
          <DialogDescription>
            This is a preview of the {reportTitle.toLowerCase()} report for the selected time range. You can download it as an XLS file.
          </DialogDescription>
        </DialogHeader>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 py-4 max-h-[60vh] overflow-y-auto">
          {reportData.main && reportData.main.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2 text-slate-700">Main Data</h4>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {mainHeaders.map((header, index) => (
                        <TableHead key={index} className={index === 0 ? "" : "text-right"}>{header}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportData.main.map((item: any, index: number) => (
                      <TableRow key={index}>
                        {renderMainTableRow(item, reportType)}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
          {reportData.secondary && reportData.secondary.length > 0 && (
            <div>
              <h4 className="font-semibold mb-2 text-slate-700">Detailed Data</h4>
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      {secondaryHeaders.map((header, index) => (
                        <TableHead key={index} className={index === 0 ? "" : "text-right"}>{header}</TableHead>
                      ))}
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {reportData.secondary.map((item: any, index: number) => (
                      <TableRow key={index}>
                        {renderSecondaryTableRow(item, reportType)}
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          )}
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleDownloadXLS} className="bg-slate-800 hover:bg-slate-700">
            <Download size={16} className="mr-2" />
            Download as XLS
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
