
import { Input } from "@/components/ui/input";

interface PalletCountInputProps {
  value: string;
  onChange: (val: string) => void;
}

export const PalletCountInput = ({ value, onChange }: PalletCountInputProps) => (
  <div className="mb-4">
    <div className="text-lg font-bold mb-2">Step 4: Number of Pallets</div>
    <Input 
      id="settingPalletCount"
      type="number"
      placeholder="Enter pallet count"
      value={value}
      onChange={e => onChange(e.target.value)}
      className="w-full rounded border border-gray-300 h-12 text-base"
    />
  </div>
);
