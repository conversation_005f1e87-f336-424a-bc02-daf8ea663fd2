
-- Create production_entries table
CREATE TABLE public.production_entries (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE NOT NULL,
  brick_type_id TEXT NOT NULL REFERENCES public.management_brick_types(id),
  pallet_count INT NOT NULL
);

-- Create setting_production_entries table
CREATE TABLE public.setting_production_entries (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE DEFAULT CURRENT_DATE NOT NULL,
  team_id TEXT NOT NULL REFERENCES public.teams(id),
  fire_id TEXT NOT NULL REFERENCES public.fires(id),
  brick_type_id TEXT NOT NULL REFERENCES public.management_brick_types(id),
  pallet_count INT NOT NULL
);

-- Create dehacking_entries table
CREATE TABLE public.dehacking_entries (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  "date" DATE NOT NULL,
  employee_id INT NOT NULL REFERENCES public.employees(id),
  brick_type_id TEXT NOT NULL REFERENCES public.management_brick_types(id),
  pallet_count INT NOT NULL,
  "hour" INT NOT NULL
);

-- Create assets table
CREATE TABLE public.assets (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  type TEXT NOT NULL
);

-- Enable RLS for the new tables
ALTER TABLE public.production_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.setting_production_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.dehacking_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.assets ENABLE ROW LEVEL SECURITY;

-- Create permissive policies for now.
-- In a real production app with users, you'd want more restrictive policies.
CREATE POLICY "Allow public access to production_entries" ON public.production_entries FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access to setting_production_entries" ON public.setting_production_entries FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access to dehacking_entries" ON public.dehacking_entries FOR ALL USING (true) WITH CHECK (true);
CREATE POLICY "Allow public access to assets" ON public.assets FOR ALL USING (true) WITH CHECK (true);

